﻿<?xml version="1.0" encoding="utf-8"?>
<xs:schema attributeFormDefault="unqualified" elementFormDefault="qualified" xmlns:xs="http://www.w3.org/2001/XMLSchema">
	<xs:element name="DH">
		<xs:complexType>
			<xs:sequence>
				<xs:element maxOccurs="unbounded" name="item">
					<xs:complexType>
						<xs:attribute name="id" type="xs:int" use="required" />
						<xs:attribute name="text" type="xs:string" use="required" />
            			<xs:attribute name="user" type="xs:string" />
					</xs:complexType>
				</xs:element>
			</xs:sequence>
		</xs:complexType>
		<xs:unique name="uniqueID">
			<xs:selector xpath=".//item" />
			<xs:field xpath="@id" />
		</xs:unique>
	</xs:element>
</xs:schema>