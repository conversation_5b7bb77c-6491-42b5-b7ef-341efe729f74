"use strict";(self.webpackChunkzhongji_experiment_react=self.webpackChunkzhongji_experiment_react||[]).push([[6988],{63804:(e,t,n)=>{n.d(t,{d:()=>u,A:()=>s});var o=n(65043),i=n(67208),l=n(36950),r=n(19853),d=n.n(r);function a(){let{threshold:e=0,rootMargin:t="0px",enableIntersectionObserver:n=!0,enablePageVisibility:i=!0,enableMutationObserver:l=!0,onVisibilityChange:r=null}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const d=(0,o.useRef)(null),a=(0,o.useRef)({isIntersecting:!1,isPageVisible:!0,displayStyle:"block",position:{top:0,left:0}}),u=(0,o.useRef)(!1),s=(0,o.useCallback)((()=>{const e=a.current,t=e.isIntersecting&&e.isPageVisible&&"none"!==e.displayStyle;t!==u.current&&(u.current=t,r&&r(t))}),[r]);return(0,o.useEffect)((()=>{if(!d.current)return()=>{};const o=[];if(n){const n=new IntersectionObserver((e=>{e.forEach((e=>{const t=a.current.isIntersecting,n=e.isIntersecting;t!==n&&(a.current={...a.current,isIntersecting:n},s(),n?console.log("\ud83d\udd0d \u5143\u7d20\u8fdb\u5165\u89c6\u53e3"):console.log("\ud83d\udc7b \u5143\u7d20\u79bb\u5f00\u89c6\u53e3"))}))}),{threshold:e,rootMargin:t});n.observe(d.current),o.push((()=>n.disconnect()))}if(i){const e=()=>{const e=!document.hidden;a.current={...a.current,isPageVisible:e},s(),e?console.log("\ud83d\udc41\ufe0f \u9875\u9762\u53d8\u4e3a\u53ef\u89c1"):console.log("\ud83d\ude48 \u9875\u9762\u53d8\u4e3a\u4e0d\u53ef\u89c1")};document.addEventListener("visibilitychange",e),o.push((()=>document.removeEventListener("visibilitychange",e)))}if(l){const e=new MutationObserver((e=>{e.forEach((e=>{if("attributes"===e.type&&"style"===e.attributeName){const e=window.getComputedStyle(d.current).display;a.current={...a.current,displayStyle:e},s(),"none"===e?console.log("\ud83d\udeab \u5143\u7d20\u88ab\u9690\u85cf (display: none)"):console.log("\u2705 \u5143\u7d20\u663e\u793a\u6837\u5f0f\u6062\u590d")}}))}));e.observe(d.current,{attributes:!0,attributeFilter:["style"]}),o.push((()=>e.disconnect()))}return s(),()=>{o.forEach((e=>e()))}}),[e,t,n,i,l,s]),{targetRef:d}}const u={daqbuffer:"daqbuffer","\u4e8c\u7ef4\u6570\u7ec4":"doubleArray","\u4e8c\u7ef4\u6570\u7ec4\u96c6\u5408":"doubleArraySet"},s=(e,t)=>{let{controlCompId:n,dataSourceType:r,dataSourceCode:u,dataCodes:s,timer:c=-1,number:v=-1,testStatus:g=1,daqCurveSelectedSampleCodes:m}=e;const p=(0,o.useRef)(!1),f=(0,o.useRef)(!1),h=(0,o.useRef)(null),b=(0,o.useRef)(!1),x=(0,o.useRef)(!1),y=(0,o.useRef)();(0,o.useRef)(t).current=t,(0,o.useEffect)((()=>{if(!u||!n||!r||!s||0===s.length)return;const e={templateName:(0,l.n1)(),controlCompId:n,dataSourceType:r,dataSourceCode:u,dataCodes:s,timer:c,number:v,testStatus:g,daqCurveSelectedSampleCodes:null!==m&&void 0!==m?m:[]};d()(e,y.current)||(null===t||void 0===t||t(),y.current=e,b.current?p.current?(0,i.pj8)({...y.current}):(0,i.i_N)({...y.current}).then((()=>{p.current=!0,f.current=!0})):p.current&&(x.current=!0))}),[n,r,u,s,c,v,g,m]);const{targetRef:C}=a({onVisibilityChange:(0,o.useCallback)((async e=>{var t,n,o;if(b.current=e,e&&y.current){if(!p.current)return await(0,i.i_N)({...y.current}),p.current=!0,void(f.current=!0);if(x.current)return(0,i.pj8)({...y.current}),void(x.current=!1)}(h.current&&clearTimeout(h.current),e&&!f.current&&null!==(t=y.current)&&void 0!==t&&t.controlCompId)&&(await(0,i.pkF)(null===(o=y.current)||void 0===o?void 0:o.controlCompId),f.current=!0);!e&&f.current&&null!==(n=y.current)&&void 0!==n&&n.controlCompId&&(h.current=setTimeout((async()=>{await(0,i.UVQ)(y.current.controlCompId),f.current=!1}),3e3))}),[])});return(0,o.useEffect)((()=>()=>{h.current&&clearTimeout(h.current),p.current&&(0,i.UWZ)(y.current.controlCompId)}),[]),{targetRef:C}}},64607:(e,t,n)=>{n.r(t),n.d(t,{default:()=>E});var o=n(65043),i=n(16569),l=n(74117),r=n(80077),d=n(84),a=n(45303),u=n(80231),s=n(67299),c=n(36950),v=n(67208),g=n(34458),m=n(81143),p=n(68374);const f=m.Ay.div`
    /* min-height: ${(0,p.D0)("30px")}; */
    /* height: ${p.OZ.layoutHeader} ; */
    /* padding: 0px 10px 0px 10px; */
    height: 100%;
    margin-bottom: 10px;
    width: 100%;
    z-index: 999;
    .gridLayout {
        padding: 3px;
        height: 100%;
        /* border-radius: 8px; */
    }

    .content {
        height: 100%;
        /* height:  ${p.OZ.layoutHeader} ; */
        background-color: #000;
        color: #FFF; 
    }
`,h=m.Ay.div`
    .unique { 
        border-bottom: 1px solid rgba(220 ,220, 220,1);
        padding: 2px
    }
    .disabled {
        cursor: no-drop;
    }
    .unique-content {
        padding: 2px;
    }

`;var b=n(56543),x=n(63804),y=n(84617);const C=m.Ay.div`  
    height: 100%;
    width: 100%;
    .grid {
        display: grid;
        /* grid-auto-rows: 100px; */
        /* height: ${p.OZ.layoutHeader}; */
        height: 100%;
        .layout-content {
            display: flex;
            justify-content: space-between;
        }
    }

    .content {
        /* max-height: ${(0,p.D0)("100px")}; */
        border-right: 1px solid #0643ED;
        border-bottom: 1px solid #0643ED;
        .title {
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: right;
            .img {
                height: ${(0,p.D0)("40px")};
                width: ${(0,p.D0)("40px")};
                margin-right: 10px;
            }
        }
      .rows {
        display: flex;
      }  
    }
    

    .content-border {
         border-right: none;
    }
    .content-border-button{
         border-bottom: none;
    }

`,I=m.Ay.div`
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    .signal-content {
        display: flex;
        justify-content: flex-end;
    }

`,j="between",w="rows",S="row",k="center";var D=n(70579);const N=e=>{let{i:t,replaceData:n,tempData:i,clientData:l}=e;const r=(0,o.useMemo)((()=>{let e=(null===t||void 0===t?void 0:t.value)||0;if(l&&l.length>0){const{currentCode:o="",replaceCode:r=""}=n||{},d=null===i||void 0===i?void 0:i.some((e=>(null===e||void 0===e?void 0:e.code)===o)),a=null===l||void 0===l?void 0:l.find((e=>o&&d&&t.code===o?r===(null===e||void 0===e?void 0:e.Code):(null===t||void 0===t?void 0:t.code)===(null===e||void 0===e?void 0:e.Code)));(null!==a&&void 0!==a&&a.Value||0===(null===a||void 0===a?void 0:a.Value))&&(e=null===a||void 0===a?void 0:a.Value)}const o=(0,c.tJ)(e,t.dimensionId,t.unitId);return(null===o||void 0===o?void 0:o.toFixed(null===t||void 0===t?void 0:t.decimal))||Number(0).toFixed(null===t||void 0===t?void 0:t.decimal)}),[t,n,i,l]);return(0,D.jsx)(D.Fragment,{children:r})},R=e=>{let{initVal:t,onCallback:n,domId:i}=e;const{backgroundColor:l,colNum:d,rowNum:a,data:u,updateFreq:s,showLayout:v,fontSizeColor:g,fontSize:m,variableCode:p,gridLineColor:f}=t,[h,R]=(0,o.useState)(),A=(0,r.d4)((e=>e.subTask.subTaskReplaceData)),E=(0,r.d4)((e=>e.global.unitList)),V=(0,r.d4)((e=>e.template.signalList)),[_,M]=(0,o.useState)({}),[$,q]=(0,o.useState)([]),{targetRef:F}=(0,x.A)({controlCompId:i,dataSourceType:x.d.daqbuffer,dataSourceCode:p,dataCodes:(0,o.useMemo)((()=>null===u||void 0===u?void 0:u.map((e=>null===e||void 0===e?void 0:e.code))),[u]),timer:null!==s&&void 0!==s?s:100,number:1,testStatus:1});(0,y.A)({controlCompId:i,onMessage:e=>{if(null!==e&&void 0!==e&&e.data){const t=[];Object.entries(e.data).forEach((e=>{let[n,o]=e;t.push({Code:n,Name:n,Value:o.at(-1),Index:-1})})),q(t)}}}),(0,o.useEffect)((()=>()=>{M({}),q([])}),[p]),(0,o.useEffect)((()=>{A.UICmd===b.zx.REPLACE_DATA&&M(null===A||void 0===A?void 0:A.UIParams)}),[A]);(0,o.useEffect)((()=>{const e=null===u||void 0===u?void 0:u.map((e=>{var t;return{...e,signals:(null===e||void 0===e||null===(t=e.signals)||void 0===t?void 0:t.map((t=>((e,t)=>{const n=(0,c.cN)(e);return{id:null===n||void 0===n?void 0:n.signalVariableId,key:null===n||void 0===n?void 0:n.signalVariableId,label:null===n||void 0===n?void 0:n.variableName,unitId:null===t||void 0===t?void 0:t.unitId,decimal:3,isIcon:!1,img:"",value:0,dimensionId:null===t||void 0===t?void 0:t.dimensionId,code:null===n||void 0===n?void 0:n.code}})(null===V||void 0===V?void 0:V.find((e=>(null===e||void 0===e?void 0:e.signal_variable_id)===t)),e))))||[]}}));R(e),L(e)}),[t,(0,c.n1)(),V]),(0,o.useEffect)((()=>{h&&L(h)}),[d,a]);const L=e=>{const t=a*d;(null===e||void 0===e?void 0:e.length)<a*d&&R(e.concat(Array.from({length:t-e.length}).map((e=>({key:crypto.randomUUID()})))))},T={[k]:"center",[j]:"space-between",[S]:"flex-start",[w]:"flex-start"},O=e=>{let t="content layout-content";const n=a*d-d;return e>=n&&n+e-n+1&&(t+=" content-border-button"),(e+1)%d===0&&(t+=" content-border"),t};return(0,D.jsx)(C,{ref:F,children:(0,D.jsx)("div",{id:"content",className:"grid",style:(()=>{let e=a;return(null===h||void 0===h?void 0:h.length)>a*d&&(e+=Math.floor((null===h||void 0===h?void 0:h.length)/(a*d))),{gridTemplateColumns:`repeat(${d}, 1fr)`,gridTemplateRows:`repeat(${e}, 1fr)`}})(),children:null===h||void 0===h?void 0:h.map(((e,t)=>{var o,i,r;return(0,D.jsxs)("div",{style:{backgroundColor:l,color:g,fontSize:m,justifyContent:T[v],borderColor:f},className:O(t),onContextMenuCapture:()=>{n(e)},children:[(0,D.jsxs)("div",{style:{display:"flex",alignItems:"center",paddingLeft:"10px",paddingRight:"10px"},onContextMenuCapture:()=>{n(e)},children:[(0,D.jsxs)("div",{className:"title",children:[(null===e||void 0===e?void 0:e.isIcon)&&(0,D.jsx)("img",{src:null===e||void 0===e?void 0:e.img,className:"img",alt:""}),null===e||void 0===e?void 0:e.label,(null===e||void 0===e?void 0:e.label)&&" \uff1a"]}),(0,D.jsxs)("div",{className:[v===w?"":"rows"],children:[(0,D.jsx)("div",{children:"value"in e&&(0,D.jsx)(N,{i:e,replaceData:_,tempData:h,clientData:$})}),(0,D.jsx)("div",{children:null===E||void 0===E||null===(o=E.find((t=>t.id===(null===e||void 0===e?void 0:e.dimensionId))))||void 0===o||null===(i=o.units.find((t=>(null===t||void 0===t?void 0:t.id)===(null===e||void 0===e?void 0:e.unitId))))||void 0===i?void 0:i.name})]})]},e.key),(null===e||void 0===e?void 0:e.signals)&&(null===e||void 0===e?void 0:e.signals.length)>0&&(0,D.jsx)(D.Fragment,{children:(0,D.jsx)(I,{children:null===e||void 0===e||null===(r=e.signals)||void 0===r?void 0:r.map((e=>{var t,o;return(0,D.jsxs)("div",{className:"signal-content",onContextMenuCapture:()=>{n(e)},children:[(0,D.jsxs)("div",{children:[null===e||void 0===e?void 0:e.label,(null===e||void 0===e?void 0:e.label)&&"\uff1a"]}),(0,D.jsxs)("div",{className:[v===w?"":"rows"],children:[(0,D.jsx)("div",{children:"value"in e&&(0,D.jsx)(N,{i:e,replaceData:_,tempData:h,clientData:$})}),(0,D.jsx)("div",{children:null===E||void 0===E||null===(t=E.find((t=>t.id===(null===e||void 0===e?void 0:e.dimensionId))))||void 0===t||null===(o=t.units.find((t=>(null===t||void 0===t?void 0:t.id)===(null===e||void 0===e?void 0:e.unitId))))||void 0===o?void 0:o.name})]})]},e.key)}))})})]},e.key)}))})})},A=e=>{var t;let{domId:n,current:o,layoutConfig:r}=e;const{openDialog:c}=(0,d.A)(),{t:m}=(0,l.Bd)(),{subContextMenuId:p}=(0,s.A)(),f=null===n||void 0===n||null===(t=n.split("edit-"))||void 0===t?void 0:t.at(-1),b=function(){let e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];(0,g.HN)()?(0,v.KJP)({code:null===o||void 0===o?void 0:o.code,reset:e}):i.Ay.error(m("\u6a21\u677f\u91cc\u4e0d\u80fd\u6e05\u96f6"))};return(0,D.jsx)(h,{children:(0,D.jsxs)(u.A,{domId:n,layoutConfig:r,children:[(0,D.jsx)("div",{className:"unique-content",onClick:()=>{p(f),c({type:a.fQ})},children:m("\u7f16\u8f91")}),(null===o||void 0===o?void 0:o.code)&&(0,D.jsx)("div",{className:"unique-content",onClick:()=>b(),children:m(`\u6e05\u96f6-${o.label}`)}),(null===o||void 0===o?void 0:o.code)&&(0,D.jsx)("div",{className:"unique-content",onClick:()=>b(!1),children:m(`\u6062\u590d-${o.label}`)})]})})},E=e=>{let{id:t,item:n,layoutConfig:i}=e;const[l,d]=(0,o.useState)({}),[a,u]=(0,o.useState)(),s=(0,r.d4)((e=>e.template.headerData)),v=(0,r.d4)((e=>e.template.widgetData));(0,o.useEffect)((()=>{const e=(0,c.Rm)(v,"widget_id",n.widget_id);if(e&&null!==e&&void 0!==e&&e.data_source){const t=null===e||void 0===e?void 0:e.data_source;d({...null===s||void 0===s?void 0:s.find((e=>String(t)===(null===e||void 0===e?void 0:e.id)))})}}),[n,v,s]);return(0,D.jsxs)(D.Fragment,{children:[(0,D.jsx)(f,{children:(0,D.jsx)("div",{className:"gridLayout",style:{background:null===l||void 0===l?void 0:l.backgroundColor},children:l&&(0,D.jsx)(R,{initVal:l,domId:t,onCallback:e=>{u(e)}})})}),(0,D.jsx)(A,{domId:t,current:a,layoutConfig:i})]})}},84617:(e,t,n)=>{n.d(t,{A:()=>u});var o=n(65043),i=n(60383),l=n(80077),r=n(39713),d=n(36950),a=n(91465);const u=e=>{let{controlCompId:t,onMessage:n}=e;const u=(0,l.wA)(),{useSubscriber:s}=(0,r.A)(),c=(0,o.useRef)(),v=(0,o.useRef)(n),g=(0,o.useRef)();(0,o.useEffect)((()=>{v.current=n}),[n]),(0,o.useEffect)((()=>(m(),()=>{var e,t;null===(e=c.current)||void 0===e||null===(t=e.close)||void 0===t||t.call(e)})),[t]);const m=async()=>{const e=`${(0,d.n1)()}-ControlCompUIData-${t}-UIData`;c.current=await s(e);for await(const[t,l]of c.current){let e;try{e=i.D(l)}catch(n){try{e=JSON.parse(l)}catch(o){console.error("GridLayout\u6570\u636e\u89e3\u6790\u5931\u8d25",o)}}2===e.mode?g.current=u((0,a.J_)("\u5927\u6570\u636e\u91cf\u52a0\u8f7d\u4e2d...")):3===e.mode?u((0,a.ge)(g.current)):v.current(e)}}}}}]);
//# sourceMappingURL=6988.682be75f.chunk.js.map