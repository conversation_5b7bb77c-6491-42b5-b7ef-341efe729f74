{"version": 3, "file": "static/js/9882.649fe475.chunk.js", "mappings": "wMAGA,MAkCA,EAlCiBA,IAEV,IAFW,MACdC,EAAK,UAAEC,EAAS,GAAEC,EAAE,OAAEC,EAAM,OAAEC,EAAM,SAAEC,GAAW,EAAK,QAAEC,EAAU,MAAOC,GAC5ER,EACG,MAAOS,EAAUC,IAAeC,EAAAA,EAAAA,UAASV,IAEzCW,EAAAA,EAAAA,YAAU,KACNF,EAAYT,EAAM,GACnB,CAACA,IAWJ,OACIY,EAAAA,EAAAA,KAACC,EAAAA,EAAK,CACFX,GAAI,OAAOA,IAEXI,QAASA,EACTQ,WAAY,EACZX,OAAQA,EACRF,UAAWI,OAAWU,EAXRC,CAACC,EAAWC,KAC9BjB,EAAUgB,EAAWC,EAAM,EAWvBd,OAAQC,OAAWU,EAjBRI,CAACF,EAAWC,EAAOE,KAClCX,EAAYW,GACZhB,EAAOa,EAAWC,EAAOE,EAAM,EAgB3BC,iBAAkBb,EAClBH,SAAUA,KACNE,GARC,OAAOL,IASd,C", "sources": ["components/split/splitVer/index.js"], "names": ["_ref", "sizes", "onDragEnd", "id", "render", "onDrag", "disabled", "minSize", "rest", "initSize", "setInitSize", "useState", "useEffect", "_jsx", "Split", "snapOffset", "undefined", "handleDragEnd", "direction", "track", "handleDrag", "style", "gridTemplateRows"], "sourceRoot": ""}