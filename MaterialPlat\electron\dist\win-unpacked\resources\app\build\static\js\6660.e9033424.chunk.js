"use strict";(self.webpackChunkzhongji_experiment_react=self.webpackChunkzhongji_experiment_react||[]).push([[6660],{5427:(e,l,a)=>{a.d(l,{A:()=>p});var n=a(65043),t=a(25055),i=a(36497),o=a(8354),d=a(74117),r=a(74125),s=a(13327),u=a(97475),c=a(70579);const{useForm:v,Item:h}=t.A,m=(e,l)=>{let{renderConfig:a,onValueChange:v}=e;const{t:m}=(0,d.Bd)(),p=(0,n.useMemo)((()=>a.map((e=>({label:e.name,value:e.code})))),[a]),b=(0,n.useMemo)((()=>Object.entries(r.Rg).map((e=>{let[l,a]=e;return{label:l,value:a}}))),[r.Rg]);return(0,c.jsxs)(t.A,{ref:l,labelCol:{span:8},wrapperCol:{span:16},initialValues:{WaveControlPattern:r.Rg.\u4f4d\u79fb,WaveControlMode:r.uO.\u659c\u6ce2},requiredMark:!1,onValuesChange:(e,l)=>{v(l)},children:[(0,c.jsx)(h,{label:m("\u63a7\u5236\u65b9\u5f0f"),name:"WaveControlPattern",rules:[{required:!0}],children:(0,c.jsx)(i.A,{options:null===b||void 0===b?void 0:b.map((e=>({...e,label:m(e.label)})))})}),(0,c.jsx)(h,{label:m("\u63a7\u5236\u6a21\u5f0f"),name:"WaveControlMode",rules:[{required:!0}],children:(0,c.jsx)(i.A,{options:null===p||void 0===p?void 0:p.map((e=>({...e,label:m(e.label)})))})}),(0,c.jsx)(h,{shouldUpdate:!0,noStyle:!0,children:e=>{var l;let{getFieldValue:n}=e;const t=n("WaveControlMode"),o=n("WaveControlPattern"),d=null===a||void 0===a||null===(l=a.find((e=>e.code===t)))||void 0===l?void 0:l.params;return null===d||void 0===d?void 0:d.map((e=>{let{name:l,code:a,renderType:t,options:d,targetContorlModeCode:u,dimension:v}=e;const p=u?n(u):o;return(0,c.jsx)(h,{label:m(l),name:a,rules:[{required:!0}],children:t===r.YD.\u9009\u62e9\u5668?(0,c.jsx)(i.A,{options:null===d||void 0===d?void 0:d.map((e=>({...e,label:m(e.label)})))}):(0,c.jsx)(s.A,{dimensionId:null===v||void 0===v?void 0:v[p]})},a)}))}}),(0,c.jsx)(h,{shouldUpdate:!0,noStyle:!0,children:e=>{var l;let{getFieldValue:n}=e;const t=n("WaveControlMode"),i=null===a||void 0===a||null===(l=a.find((e=>e.code===t)))||void 0===l?void 0:l.saveRules;return i?(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)(o.A,{orientation:"left",plain:!0,style:{borderBlockStart:"0 rgba(5, 5, 5, 0.5)"},children:m("\u5b58\u76d8\u7b56\u7565")}),(0,c.jsx)(u.A,{saveRules:i,getFieldValue:n})]}):(0,c.jsx)(c.Fragment,{})}})]})},p=(0,n.forwardRef)(m)},13327:(e,l,a)=>{a.d(l,{A:()=>c});var n=a(65043),t=a(36497),i=a(97914),o=a(80077),d=a(36950),r=a(70579);const{Option:s}=t.A,u=e=>{let{dimensionId:l,unitId:a,handleUnitChange:i,disabled:d}=e;const u=(0,o.d4)((e=>e.global.unitList)),c=(0,n.useMemo)((()=>{var e;return(null===(e=u.find((e=>e.id===l)))||void 0===e?void 0:e.units)||[]}),[u,l]);return(0,r.jsx)(t.A,{value:a,style:{width:120},disabled:d,onChange:e=>{i(e)},children:c.map((e=>{let{id:l,name:a}=e;return(0,r.jsx)(s,{value:l,children:a},l)}))})},c=e=>{var l;let{value:a,onChange:t,dimensionId:s,unitDisabled:c}=e;const v=(0,o.d4)((e=>e.global.unitList)),[h,m]=(0,n.useState)(),[p,b]=(0,n.useState)(null===(l=v.find((e=>e.id===s)))||void 0===l?void 0:l.default_unit_id),f=(0,n.useMemo)((()=>{var e;return null===(e=v.find((e=>e.id===s)))||void 0===e?void 0:e.default_unit_id}),[s,v]);(0,n.useEffect)((()=>{b(f)}),[f]),(0,n.useEffect)((()=>{void 0!==a&&null!==a&&m((0,d.tJ)(Number(a),s,p,f))}),[a]);const g=e=>{if(h){const l=(0,d.tJ)(Number(a),s,e,f);m(l)}b(e)};return(0,r.jsx)(i.A,{style:{width:"100%"},value:h,addonAfter:!!s&&(0,r.jsx)(u,{dimensionId:s,unitId:p,handleUnitChange:g,disabled:c}),onChange:e=>{m(e),t((0,d.tJ)(Number(e),s,f,p))}})}},13830:(e,l,a)=>{a.d(l,{A:()=>f,p:()=>h.ps});var n=a(65043),t=a(16569),i=a(6051),o=a(95206),d=a(81143),r=a(80077),s=a(74117),u=a(88359),c=a(51554),v=a(78178),h=a(56543),m=a(754),p=a(70579);const b=d.Ay.div`
    .bind-input-variable{
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 5px;

        .label{
            margin-right: 10px;
        }
        .bind-value-span{
            word-break: break-all;
        }
        .bind-fun-div{
            white-space: nowrap;
        }
    }
`,f=e=>{let{id:l,value:a,onChange:d,inputVariableType:f,checkFn:g,isSetProgrammableParameters:x=!1}=e;const y=(0,r.wA)(),{t:j}=(0,s.Bd)(),A=(0,n.useRef)(),[C,w]=(0,n.useState)(!1),[_,k]=(0,n.useState)(),[S,I]=(0,n.useState)("add");(0,n.useEffect)((()=>{a&&V(a)}),[a]);const V=e=>{if((null===e||void 0===e?void 0:e.variable_type)!==f)return void d();(0,m.B)("inputVariable","inputVariableMap").has(e.code)||d()},F=e=>{const l=g&&g(e);if(l)return void t.Ay.error(l);const{id:a,code:n,variable_name:i,variable_type:o,name:r}=e;d({id:a,code:n,variable_name:null!==i&&void 0!==i?i:r,variable_type:o,restrict:{variableType:h.oY.\u8f93\u5165\u53d8\u91cf,inputVarType:f}})};return(0,p.jsxs)(p.Fragment,{children:[(0,p.jsx)(b,{children:(0,p.jsxs)("div",{className:"bind-input-variable",children:[(0,p.jsxs)("div",{className:"bind-value-span",children:[j("\u7ed1\u5b9a\u53d8\u91cf"),":",null===a||void 0===a?void 0:a.variable_name]}),(0,p.jsx)("div",{className:"bind-fun-div",children:(0,p.jsxs)(i.A,{children:[(0,p.jsx)(o.Ay,{onClick:()=>{A.current.open({variableType:h.oY.\u8f93\u5165\u53d8\u91cf,inputVarType:f})},children:"\u9009\u62e9"}),a?(0,p.jsxs)(p.Fragment,{children:[(0,p.jsx)(o.Ay,{onClick:()=>{k(null===a||void 0===a?void 0:a.id),I("edit"),w(!0)},children:j("\u7f16\u8f91")}),(0,p.jsx)(o.Ay,{onClick:()=>d(),children:j("\u89e3\u7ed1")})]}):(0,p.jsx)(o.Ay,{onClick:()=>{I("add"),w(!0)},children:j("\u65b0\u5efa")})]})})]})}),(0,p.jsx)(c.A,{ref:A,isSetProgrammableParameters:x,handleSelectedVariable:F}),C&&(0,p.jsx)(v.A,{isSetProgrammableParameters:x,variableType:f,modalIndex:0,editId:_,mode:S,open:C,onOk:async e=>{const l=await y((0,u.w)()),a=null===l||void 0===l?void 0:l.find((l=>l.code===e.code));a&&F(a),w(!1)},onCancel:()=>{w(!1)}})]})}},26660:(e,l,a)=>{a.r(l),a.d(l,{Container:()=>N,default:()=>R});var n=a(65043),t=a(81143),i=a(19853),o=a.n(i),d=a(80231),r=a(97320),s=a(16569),u=a(56434),c=a.n(u),v=a(74125),h=a(67208),m=a(69581),p=a(28116);const b=t.Ay.div`
    display: flex;

    width: 100%;
    height: 100%;
    background: #ffffff;
    overflow: hidden;

    .label{
        display: flex;
        align-items: center;
        overflow: hidden;
        white-space:nowrap;
    }    
`,f="400px",g=(0,t.Ay)(b)`
    display: grid;
    grid-template-columns: repeat(auto-fill, ${f});;
    grid-gap: 10px;
    justify-content: center;

    width: 100%;
    height: 100%;
    overflow-y: auto;
    padding: 10px;

    .card{
        width: ${f};
    }
`;var x=a(6051),y=a(32513),j=a(89073),A=a(5427),C=a(70579);const w=e=>{let{checked:l,order:a,onChange:n}=e;return(0,C.jsx)(x.A,{children:(0,C.jsx)(y.A,{checked:l,onChange:e=>n(e.target.checked),children:`\u6bb5${a}`})})},_=e=>{let{order:l,onCheckedChange:a}=e;return(0,C.jsx)(j.A,{className:"card",title:(0,C.jsx)(w,{checked:!1,order:l,onChange:a})})},k=e=>{let{order:l,onCheckedChange:a,data:t,renderConfig:i,onValueChange:o}=e;const d=(0,n.useRef)();return(0,n.useEffect)((()=>{t&&d.current.setFieldsValue(t)}),[t]),(0,C.jsx)(j.A,{className:"card",title:(0,C.jsx)(w,{checked:!0,order:l,onChange:a}),children:(0,C.jsx)(A.A,{ref:d,renderConfig:i,onValueChange:o})})},S=e=>{var l;let{config:{variable:{value:a}={}}}=e;const t=(0,m.A)(null===a||void 0===a?void 0:a.code),[i,o]=(0,n.useState)([]),d=(0,n.useMemo)((()=>{var e,l;return null!==t&&void 0!==t&&null!==(e=t.custom_array_tab)&&void 0!==e&&e.customWaveform?null===t||void 0===t||null===(l=t.custom_array_tab)||void 0===l?void 0:l.customWaveform:c()(v.TB)}),[null===t||void 0===t?void 0:t.custom_array_tab]);(0,n.useEffect)((()=>{var e,l;null!==t&&void 0!==t&&null!==(e=t.default_val)&&void 0!==e&&e.value&&o(null===t||void 0===t||null===(l=t.default_val)||void 0===l?void 0:l.value)}),[null===t||void 0===t||null===(l=t.default_val)||void 0===l?void 0:l.value]);const r=e=>{u({...t,default_val:{...null===t||void 0===t?void 0:t.default_val,value:e}})},u=async e=>{await(0,h.Tnc)(e)&&(0,p.P)({code:e.code},e)};return(0,C.jsxs)(g,{children:[i.map(((e,l)=>(0,C.jsx)(k,{order:l+1,data:e,renderConfig:d,onCheckedChange:()=>(e=>{const l=i.filter(((l,a)=>a!==e));o(l),r(l)})(l),onValueChange:e=>((e,l)=>{const a=i.map(((a,n)=>n!==l?a:{...e}));o(a),r(a)})(e,l)},l+1))),(0,C.jsx)(_,{order:i.length+1,onCheckedChange:()=>{t?o([...i,{}]):s.Ay.error("\u6ca1\u6709\u7ed1\u5b9a\u53d8\u91cf")}})]})};var I=a(25055),V=a(8918),F=a(74117),T=a(68358),B=a(13830);const{useForm:M,Item:P}=I.A,W=e=>{let{open:l,onClose:a,config:t,setConfig:i}=e;const{t:d}=(0,F.Bd)(),[r]=M();(0,n.useEffect)((()=>{o()(t,r.getFieldsValue())||r.setFieldsValue(t)}),[t]);return(0,C.jsx)(T.A,{open:l,onClose:a,children:(0,C.jsx)(I.A,{form:r,labelCol:{span:6},wrapperCol:{span:18},onValuesChange:(e,l)=>{var a;let n=l;null!==e&&void 0!==e&&null!==(a=e.variable)&&void 0!==a&&a.value&&(n={...n,attr:{...n.attr,label:e.variable.value.variable_name}}),i(n)},children:(0,C.jsx)(V.A,{defaultActiveKey:"attr",items:[{key:"variable",label:d("\u53d8\u91cf"),forceRender:!0,children:(0,C.jsx)(C.Fragment,{children:(0,C.jsx)(P,{label:d("\u503c"),name:["variable","value"],children:(0,C.jsx)(B.A,{inputVariableType:B.p[d("\u81ea\u5b9a\u4e49\u6570\u7ec4")]})})})}]})})})},E={attr:{compWidth:"100%",label:"",labelWidth:"30%",isShowColon:!1,spaceSetween:!0},variable:{value:null,visible:null}},N=t.Ay.div`
    width: 100%;
    
    overflow: hidden;
`,R=e=>{var l;let{item:a,id:t,layoutConfig:i}=e;const{updateLayoutItem:s}=(0,r.A)(),[u,c]=(0,n.useState)(!1),[v,h]=(0,n.useState)(E);(0,n.useEffect)((()=>{try{if(null!==a&&void 0!==a&&a.data_source){const{comp_config:e}=JSON.parse(null===a||void 0===a?void 0:a.data_source);o()(e,v)||h(e)}}catch(e){console.log("err",e)}}),[null===a||void 0===a?void 0:a.data_source]);return(0,C.jsxs)(N,{id:t,compWidth:null===v||void 0===v||null===(l=v.attr)||void 0===l?void 0:l.compWidth,children:[(0,C.jsx)(S,{config:v}),(0,C.jsx)(W,{open:u,onClose:()=>{c(!1),s({layout:i,newItem:{...a,data_source:JSON.stringify({comp_config:v})}})},config:v,setConfig:h}),(0,C.jsx)(d.A,{domId:t,layoutConfig:i,children:(0,C.jsx)("div",{className:"unique-content",onClick:()=>c(!0),children:"\u7f16\u8f91\u81ea\u5b9a\u4e49\u6ce2\u5f62"})})]})}},51554:(e,l,a)=>{a.d(l,{A:()=>g});var n=a(65043),t=a(80077),i=a(16569),o=a(83720),d=a(79806),r=a(74117),s=a(93950),u=a.n(s),c=a(56543),v=a(75440),h=a(29977),m=a(6051),p=a(70579);const b=e=>{let{handleSelected:l,t:a}=e;return[{title:a?a("\u540d\u79f0"):"\u540d\u79f0",dataIndex:"variable_name",key:"variable_name"},{title:a?a("\u6807\u8bc6\u7b26"):"\u6807\u8bc6\u7b26",dataIndex:"code",key:"code"},{title:a?a("\u64cd\u4f5c"):"\u64cd\u4f5c",dataIndex:"code",key:"code",render:(e,a)=>(0,p.jsx)(m.A,{size:"middle",children:(0,p.jsx)("a",{onClick:()=>l(a),children:"\u9009\u62e9"})})}]},f=(e,l)=>{let{handleSelectedVariable:a=e=>console.log(e),isSetProgrammableParameters:s=!1}=e;const m=(0,h.A)(),f=(0,t.d4)((e=>e.template.resultData)),[g,x]=(0,n.useState)(!1),[y,j]=(0,n.useState)(),[A,C]=(0,n.useState)([]),[w,_]=(0,n.useState)([]),{t:k}=(0,r.Bd)(),S=(0,n.useMemo)((()=>m.map((e=>({...e,variable_name:null===e||void 0===e?void 0:e.name})))),[m]),I=(0,n.useMemo)((()=>f.map((e=>({...e,id:e.code})))),[f]);(0,n.useEffect)((()=>{g&&V()}),[g]);const V=()=>{if(y)switch(null===y||void 0===y?void 0:y.variableType){case c.oY.\u8f93\u5165\u53d8\u91cf:{const e=[...S.filter((e=>!(null!==y&&void 0!==y&&y.inputVarType)||e.variable_type===(null===y||void 0===y?void 0:y.inputVarType)))];_(e),C(e);break}case c.oY.\u4fe1\u53f7\u53d8\u91cf:case c.oY.\u7ed3\u679c\u53d8\u91cf:_(I),C(I);break;default:console.log("\u672a\u5904\u7406\u7684\u53d8\u91cf\u7c7b\u578b",null===y||void 0===y?void 0:y.variableType)}};(0,n.useImperativeHandle)(l,(()=>({open:e=>{j(e),x(!0)}})));const F=u()((async e=>{if(e){const l=A.filter((l=>{const a=l.variable_name.toLowerCase(),n=l.code.toLowerCase(),t=e.toLowerCase();return a.includes(t)||n.includes(t)}));_(l)}else _(A)}),200);return(0,p.jsxs)(v.A,{open:g,onCancel:()=>{x(!1)},title:"\u53d8\u91cf\u9009\u62e9",footer:null,children:[(0,p.jsx)(o.A,{allowClear:!0,onChange:e=>F(e.target.value),placeholder:k("\u540d\u79f0/\u5185\u90e8\u540d"),style:{width:"300px",marginBottom:"10px"}}),(0,p.jsx)(d.A,{rowKey:"code",columns:b({handleSelected:e=>{var l;!s||"Array"===(null===e||void 0===e?void 0:e.variable_type)&&"programmableParameters"===(null===e||void 0===e||null===(l=e.custom_array_tab)||void 0===l?void 0:l.useType)?(a(e,y),x(!1)):i.Ay.error("\u8bf7\u9009\u62e9\u81ea\u5b9a\u4e49\u6570\u7ec4\u7528\u9014\u4e3a\u7a0b\u63a7\u53c2\u6570\u7684\u53d8\u91cf")}}),dataSource:w})]})},g=(0,n.forwardRef)(f)},68358:(e,l,a)=>{a.d(l,{A:()=>h});var n=a(65043),t=a(48677),i=a(80077),o=a(14463),d=a(25055),r=a(36282),s=a(96603),u=a(14524),c=a(70579);const v=e=>{let{setting:l,onChange:a}=e;const[t]=d.A.useForm();(0,n.useEffect)((()=>{t.setFieldsValue({...l})}),[l]);return(0,c.jsx)(r.A,{content:(0,c.jsxs)(d.A,{form:t,name:"basic",labelCol:{style:{width:35}},onValuesChange:(e,l)=>{a(l)},children:[(0,c.jsx)(d.A.Item,{label:"\u4f4d\u7f6e",name:"placement",children:(0,c.jsxs)(s.Ay.Group,{size:"small",children:[(0,c.jsx)(s.Ay.Button,{value:"top",children:"\u4e0a"}),(0,c.jsx)(s.Ay.Button,{value:"right",children:"\u53f3"}),(0,c.jsx)(s.Ay.Button,{value:"bottom",children:"\u4e0b"}),(0,c.jsx)(s.Ay.Button,{value:"left",children:"\u5de6"})]})}),(0,c.jsx)(d.A.Item,{label:"\u5c3a\u5bf8",name:"size",children:(0,c.jsxs)(s.Ay.Group,{size:"small",children:[(0,c.jsx)(s.Ay.Button,{value:"default",children:"\u6b63\u5e38"}),(0,c.jsx)(s.Ay.Button,{value:"large",children:"\u5927"})]})})]}),title:"",trigger:"click",placement:"leftTop",children:(0,c.jsx)(u.A,{})})},h=e=>{let{children:l,open:a,onClose:n}=e;const d=(0,i.wA)(),{drawSetting:r}=(0,i.d4)((e=>e.split));return(0,c.jsx)(c.Fragment,{children:a&&(0,c.jsx)(t.A,{open:a,size:null===r||void 0===r?void 0:r.size,placement:null===r||void 0===r?void 0:r.placement,onClose:n,extra:(0,c.jsx)(v,{setting:r,onChange:e=>{d({type:o.cd,param:e})}}),children:l})})}},97320:(e,l,a)=>{a.d(l,{A:()=>r});a(65043);var n=a(80077),t=a(84856),i=a(67208),o=a(14463),d=a(41086);const r=()=>{const e=(0,n.wA)(),{saveLayout:l}=(0,t.A)(),a=async l=>{let{layout:a,newItem:n}=l;const t={...a,children:r(a.children,n)},[s]=await(0,i.PXE)({binder_ids:[null===a||void 0===a?void 0:a.binder_id]});await(0,i.Kv3)({binders:[{...s,layout:(0,d.gT)(t,null===a||void 0===a?void 0:a.binder_id)}]}),e({type:o.EH,param:s.binder_id})},r=(e,l)=>e.map((e=>e.id===l.id?l:e.children&&e.children.length>0?{...e,children:r(e.children,l)}:e)),s=async e=>{let{layout:a,newItem:n}=e;const t={...a,children:r(a.children,n)};await l(t)};return{updateLayoutItem:async e=>{let{layout:l,newItem:n}=e;null!==l&&void 0!==l&&l.binder_id?(console.log("\u754c\u9762\u5185\u5bb9-tab"),await a({layout:l,newItem:n})):(console.log("\u754c\u9762\u5185\u5bb9-\u4e3b\u7a97\u53e3"),await s({layout:l,newItem:n}))}}}}}]);
//# sourceMappingURL=6660.e9033424.chunk.js.map