"use strict";(self.webpackChunkzhongji_experiment_react=self.webpackChunkzhongji_experiment_react||[]).push([[9950],{69950:(e,n,t)=>{t.r(n),t.d(n,{default:()=>y});var i=t(65043),r=t(74117),c=t(80077),d=t(14463),o=t(8237),a=t(20790),s=t(81143);t(68374);const l=s.Ay.div`
    background: #FFFFFF;
    /* border-radius: 8px; */
    border: 1px solid #D7E2FF;
    display: flex;
    align-items: center;
    justify-content:center ;
    height: 100%;
    width: 100%;
    overflow: hidden;
`,p=s.Ay.div`
    position: fixed;
    z-index: 100000;
    user-select: none;
    display: none;
    background: #FFF;
    min-width: 120px;
    padding: 5px;
    border: 1px solid rgba(220 ,220, 220,1);
    box-shadow: 5px 5px 3px -2px rgba(0, 0, 0,0.4);
    font-size: 14px;


`,u=s.Ay.div`
    .unique { 
        border-bottom: 1px solid rgba(220 ,220, 220,1);
        padding: 2px
    }
    .unique-content {
        padding: 2px;
    }
    .unique-content:hover {
        background: rgba(20, 115, 245,0.4)
    }
`;var x=t(70579);const y=e=>{let{id:n,item:t,onClick:s,isContextMenu:y=!0,layoutConfig:m}=e;const h=(0,i.useRef)(),f=(0,c.wA)(),{t:g}=(0,r.Bd)();return(0,x.jsxs)(x.Fragment,{children:[(0,x.jsx)(l,{id:n,onClick:s,onContextMenu:e=>{e.preventDefault(),e.stopPropagation();const n=document.getElementsByName("menu");n&&n.forEach((e=>{e.style.display="none"})),s&&s();let{clientX:t,clientY:i}=e;h.current.style.display="block";const r=window.innerWidth,c=window.innerHeight,d=h.current.offsetWidth,o=h.current.offsetHeight;t=r-t>d?t:t-d,i=c-i>o?i:i-o,h.current.style.top=`${i}px`,h.current.style.left=`${t}px`,document.onclick=()=>{h&&h.current&&h.current.style&&(h.current.style.display="none",document.onclick=null)}},children:g(t.name)},t.id),y&&(0,x.jsx)(a.A,{children:(0,x.jsx)(p,{ref:h,name:"menu",children:(0,x.jsxs)(u,{children:[(0,x.jsx)("div",{className:"unique-content",onClick:()=>{f({type:d.NS,param:m}),f({type:d.ad,param:{type:o.P.CONTENT}})},children:g("\u5185\u5bb9")}),(0,x.jsx)("div",{className:"unique-content",onClick:()=>{f({type:d.ad,param:{type:o.P.DLE}})},children:g("\u5220\u9664")}),(0,x.jsx)("div",{className:"unique-content",onClick:()=>{f({type:d.ad,param:{type:o.P.VERTICAL,directionType:o.fJ.UNSHIFT}})},children:g("\u5411\u5de6\u7ad6\u5207")}),(0,x.jsx)("div",{className:"unique-content",onClick:()=>{f({type:d.ad,param:{type:o.P.VERTICAL,directionType:o.fJ.PUSH}})},children:g("\u5411\u53f3\u7ad6\u5207")}),(0,x.jsx)("div",{className:"unique-content",onClick:()=>{f({type:d.ad,param:{type:o.P.HORIZONTAL,directionType:o.fJ.UNSHIFT}})},children:g("\u5411\u4e0a\u6a2a\u5207")}),(0,x.jsx)("div",{className:"unique-content",onClick:()=>{f({type:d.ad,param:{type:o.P.HORIZONTAL,directionType:o.fJ.PUSH}})},children:g("\u5411\u4e0b\u6a2a\u5207")})]})})})]})}}}]);
//# sourceMappingURL=9950.14f5d3e2.chunk.js.map