using System.Text.Json;
using Consts;
using Logging;
using MQ;
using Scripting;
using SubTaskUtils;

namespace SubTasks.tasks.experimentalActionTask
{

    /**
    打印报告属性

 Run(int ...)
{
    switch(State)
    {
        case  0:
            载入输入打印报告模板（试验模板输入向导设定的）
            载入配套的设定参数（试验结果、打印报告等）
            if（打开试验报告）
            ｛
                发送打开试验报告管理器并显示报告消息；           
            ｝
            if（打印试验报告）
            ｛
                发送打印报告消息；            
            ｝
            关闭子程序
        break;
    
            

    }
}
    */
    public class SubTaskPrintReport : ISubTask
    {
        private string? _processId;
        private string? _subtaskId;
        private SubTaskCmdParams? _subtaskCmdParams;

        public MQSubTaskSub[] subs { set; get; }
        public bool Sub_TASK_MGR_CMD { get; set; } = true;
        public bool Sub_TASK_MGR_CMD_Other { get; set; } = true;
        public bool Sub_TASK_HARDWARE_CMD { get; set; } = false;
        public bool Sub_TASK_HARDWARE_DATA { get; set; } = false;
        public bool Sub_TOPIC_FROM_UI { get; set; } = true;
        public bool Sub_TOPIC_FROM_SCRIPT_CLIENT { get; set; } = false;
        public bool Sub_SelfTopic { get; set; } = false;
        public bool Sub_TOPIC_NOTIFY { get; set; } = false;

        public SubTaskCmdParams ImportParams(string paramatersString)
        {
            CCSSLogger.Logger.Info("ImportPrams" + " :" + paramatersString);
            var x = JsonSerializer.Deserialize<SubTaskCmdParams>(paramatersString)!;
            CCSSLogger.Logger.Info("Json 反序列化" + x);

            return x;
        }

        public JsonElement UIParams()
        {
            throw new NotImplementedException();
        }

        /// 在创建时返回一个 bool值, 代表是否创建成功
        public bool Run(SubTaskCmdParams t)
        {
            _processId = t.ProcessID;
            _subtaskId = t.SubTaskID;
            _subtaskCmdParams = t;

            ISystemBus.SendToUIStatusTopic(UtilsForSubTasks.GenerateStatusUIJson(t.Cmd(), _processId!, _subtaskId!));
            ISystemBus.SendToUICmdTopic(JsonSerializer.Serialize(new UICmdParams(
                _processId,
                _subtaskId,
                "Print",
                JsonDocument.Parse("{}").RootElement)));


            CCSSLogger.Logger.Info("打印报告子任务 启动:" + t);
            Finish(t);
            return true;
        }

        /// 终止执行
        public bool Abort(SubTaskCmdParams t)
        {
            ISystemBus.SendToUIStatusTopic(UtilsForSubTasks.GenerateStatusUIJson(CmdConsts.RCV_ABORT_TASK_CMD, _processId!, _subtaskId!));
            CCSSLogger.Logger.Info("打印报告子任务 终止:" + CmdConsts.RCV_ABORT_TASK_CMD);
            ((ISubTask)this).CleanAllSubs();
            return true;
        }

        /// <summary>
        /// 打印报告子任务: 暂停 通知前端修改状态(不做任何其他修改) 
        /// </summary>
        public bool Pause(SubTaskCmdParams t)
        {
            ISystemBus.SendToUIStatusTopic(UtilsForSubTasks.GenerateStatusUIJson(CmdConsts.RCV_PAUSE_TASK_CMD, _processId!, _subtaskId!));
            CCSSLogger.Logger.Info("打印报告子任务 暂停:" + CmdConsts.RCV_PAUSE_TASK_CMD);
            return true;
        }

        /// <summary>
        /// 打印报告子任务: 恢复 通知前端修改状态(不做任何其他修改) 
        /// </summary>
        public bool Resume(SubTaskCmdParams t)
        {
            ISystemBus.SendToUIStatusTopic(UtilsForSubTasks.GenerateStatusUIJson(CmdConsts.RCV_PAUSE_TASK_CMD, _processId!, _subtaskId!));
            CCSSLogger.Logger.Info("打印报告子任务 恢复:" + CmdConsts.RCV_RESUME_TASK_CMD);
            return true;
        }

        public bool Finish(SubTaskCmdParams t)
        {
            ISystemBus.SendToUIStatusTopic(UtilsForSubTasks.GenerateStatusUIJson(CmdConsts.RCV_FINISH_TASK_CMD, _processId!, _subtaskId!));
            ISystemBus.SendToTaskUpTopic(CmdConsts.SubTaskFinishCmd(t.ClassName!, t.ProcessID!, t.SubTaskID!));
            CCSSLogger.Logger.Info("打印报告子任务 结束:" + CmdConsts.RCV_FINISH_TASK_CMD);
            ((ISubTask)this).CleanAllSubs();
            return true;
        }

        public bool ProcessData(SubTaskCmdParams p)
        {
            throw new NotImplementedException();
        }

        public void ImportHwFuncRet(string paramatersString)
        {
            throw new NotImplementedException();
        }

        public void HandleMsgFromUI(string paramatersString)
        {
        }
        public string[] GetSelfTopic()
        {
            var selfTopics = Array.Empty<string>();
            return selfTopics;
        }

        public void HandleMsgFromVAR(string topic, string paramatersString)
        {
            throw new NotImplementedException();
        }

        public void HandleMsgFromScript(string paramatersString)
        {
            throw new NotImplementedException();
        }

        public bool ReStart(SubTaskCmdParams p)
        {
            throw new NotImplementedException();
        }

        public bool Error(SubTaskCmdParams p)
        {
            throw new NotImplementedException();
        }
        public void HandleNotify(string p, string msg)
        {
            throw new NotImplementedException();
        }
    }
}
