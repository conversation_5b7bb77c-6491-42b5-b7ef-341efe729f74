using Consts;
using Logging;
using MQ;
using NPOI.SS.Formula.Functions;
using ScriptEngine.InputVar.InputVars;
using ScriptEngine.InstantiatedTemplate.Hardware.MappingHardware;
using Scripting;
using SubTaskUtils;
using System.Data;
using System.Reactive.Linq;
using System.Text.Json;
using static Logging.CCSSLogger;
using static Scripting.ITemplate;

namespace SubTasks.tasks.gatherDataTask;

/**
 周期数据采集子任务
高速数据采集控件-过滤点数和周期

public event Hw.OnDataBlockHdlr OnDataBlockEvent;
采集回调数据块

Run（）
｛
switch （State）
｛
    case 0：

            整理所有的通道，按照设定的速率计算每个通道间隔点，遵循以下规则
                A.如果间隔点不能整除，则按照整数计算。
                B.如果设定采集速率大于实际采集速率，则按照实际采集速率
                C.如果采集速率不均匀则只能按照时间间隔就近采集数据。

            把保存数据数组缓冲区设定为环形缓冲区，缓冲区设定为20000点，建立多段数据数据结构
            if（是继续试验）
            ｛

                载入已执行周期数
                载入保存文件位置
            ｝
            elseif（重新开始）
            ｛
                所有的保存数据数组缓冲区写指针=0
                当前周期数=0
            ｝
            采集标志=1；
            State=1；

        break；
    case 1：
        if（采集标志==1）
        ｛
            先按照滤波原则采集数据（间隔点采集或时间间隔就近采集）
            放到环形缓冲区

            if（判断多个周期波周期数是否满足）
            ｛
               if（是缓冲区完整波形）
               ｛
                   数据放进文件保存
               ｝
            ｝
        ｝

        if（暂停试验）
        ｛
            if（停止采集）
              采集标志=0
            elseif（不停止采集）
              采集标志=1

        ｝
        if（恢复试验）
        ｛
            采集标志=1

        ｝
        if(停止试验)
        ｛
            保存缓冲区写指针位置
             保存当前周期数
            保存保存文件位置
            关闭子程序

        ｝

        break；

｝

有一个情况是有问题的。就是碰到意外情况停机时，需要保留最近的固定周期的波形，比如100个周期波形。
另外，如果保留最后100个周期波形，可能有个别点在前面记录了，这个可能会涉及重复点记录问题
}

*/
public record UIParamRec(string ProcessID, string SubtaskID, string ModalType, string ModalContext);

public record FunctionCmd(string FuncName, string? ProcessId, string? SubTaskID, object[] Params);

public record Variable_Circle(string? Code, string? Name, double? Value, double Circle);

public class SubTaskHightGatherData : ISubTask
{
    public MQSubTaskSub[] subs { get; set; }
    public bool Sub_TASK_MGR_CMD { get; set; } = true;
    public bool Sub_TASK_MGR_CMD_Other { get; set; } = true;
    public bool Sub_TASK_HARDWARE_CMD { get; set; } = true;
    public bool Sub_TASK_HARDWARE_DATA { get; set; } = true;
    public bool Sub_TOPIC_FROM_UI { get; set; } = true;
    public bool Sub_TOPIC_FROM_SCRIPT_CLIENT { get; set; } = true;
    public bool Sub_SelfTopic { get; set; } = false;
    public bool Sub_TOPIC_NOTIFY { get; set; } = true;
    private const string ClassName = "SubTaskHightGatherData";
    ////轴ID
    int _deviceid;

    private string? _processID;
    private string? _subtaskID;
    private string? _templateName;
    private string? _sampleInstanceCode;
    private bool _isSaveDb = true;
    
    //环形缓存区的大小,2000
    private int _bufferSize = 2000;

    //目标循环数，周期波的执行频率(设定多少个周期/s,0.1意味着10s中才能执行一个正弦波周期)，需要从输入变量中获取
    private double _circleCmdFrequency;

    // 异常数据采集参数
    private bool _isExceptionData;
    private double _exceptionDataUpperLimit;
    private double _exceptionDataLowerLimit;
    private double _exceptionDataPrecision;

    // 点过滤参数
    private string? _collectMode;
    private double _collectFrequency;
    private int _intervalPoints;

    // 最后收集的周期数
    private ulong _finalCollectedCycles;

    // 前面要收集的周期数
    private ulong _firstCollectedCycles;
    
    // 对数采集
    private bool _logarithmicCollect = false;

    // 间隔采集
    private bool _isIntervalCollected;
    private ulong _intervalCollectedCycles;

    // 指定周期参数
    private bool _isSpecifyCycle;
    private ulong[]? _specifyCycle;

    private ulong _currentCircles;

    private bool _isFinalCollected;
    private bool _isFirstCollected;

    // 采集信号的code
    private string _frequencySignalCode = "signal_pinlv";

    // 异常信号变量的code,用于控制采集的异常信号
    private string _activeCtrlSignalCode = "signal_pos";

    // 周期 信号变量的code,用于周期过滤
    private string _cyclesSignalCode = "signal_cycle";
    BufferInputVar daqBuffer;
    private string? abnormaldaqBufferCode;
    BufferInputVar? abnormaldaqBuffer;
    SubTaskCmdParams _subTaskCmdParams;
    //TODO 目前在信号变量的机制，不符合客户要求的选择轴且直接关联轴下Cycle,Command等信息(已解决有新的需要关联在SetCode()中修改)
    private int _daqRate;
    private string _bindBufferCode;
    private ITemplate _templateInst;
    //存入二维数组
    bool _isDoubleArray;
    string _doubleArrayCode;
    private class UICmdParam
    {
        public List<Variable_Circle>? VarValues { get; set; }
    }

    public SubTaskHightGatherData() { }

    public bool Abort(SubTaskCmdParams Params)
    {
        ISystemBus.SendToUIStatusTopic(GenrateStatusUIJson(CmdConsts.RCV_ABORT_TASK_CMD));

        CCSSLogger.Logger.Info("高速数据采集 终止:" + Params);
        ISystemBus.SendToTaskUpTopic(
            CmdConsts.SubTaskFinishCmd(Params.ClassName!, Params.ProcessID!, Params.SubTaskID!)
        );
        _templateInst!.RemoveDaqHandler(_bindBufferCode + _processID);
        ((ISubTask)this).CleanAllSubs();
        return true;
    }

    public bool Finish(SubTaskCmdParams Params)
    {
        ISystemBus.SendToUIStatusTopic(GenrateStatusUIJson(CmdConsts.RCV_FINISH_TASK_CMD));
        CCSSLogger.Logger.Info("高速数据采集 终止:" + Params);
        ISystemBus.SendToTaskUpTopic(
            CmdConsts.SubTaskFinishCmd(Params.ClassName!, Params.ProcessID!, Params.SubTaskID!)
        );
        ((ISubTask)this).CleanAllSubs();
        return true;
    }

    public string[] GetSelfTopic()
    {
        throw new NotImplementedException();
    }

    public void HandleMsgFromScript(string ParamatersString)
    {
        throw new NotImplementedException();
    }

    public void HandleMsgFromUI(string ParamatersString)
    {
        throw new NotImplementedException();
    }

    public void HandleMsgFromVAR(string topic, string ParamatersString)
    {
        throw new NotImplementedException();
    }

    public void ImportHwFuncRet(string ParamatersString)
    {
        throw new NotImplementedException();
    }

    public SubTaskCmdParams? ImportParams(string ParamatersString)
    {
        Logger.Info("ImportPrams" + " :" + ParamatersString);
        var x = JsonSerializer.Deserialize<SubTaskCmdParams>(ParamatersString);

        return x!;
    }

    public bool Pause(SubTaskCmdParams t)
    {
        ISystemBus.SendToUIStatusTopic(
            UtilsForSubTasks.GenerateStatusUIJson(
                CmdConsts.RCV_PAUSE_TASK_CMD,
                _processID!,
                _subtaskID!
            )
        );
        return true;
    }

    public bool Resume(SubTaskCmdParams t)
    {
        ISystemBus.SendToUIStatusTopic(
            UtilsForSubTasks.GenerateStatusUIJson(
                CmdConsts.RCV_RESUME_TASK_CMD,
                _processID!,
                _subtaskID!
            )
        );
        return true;
    }

    public bool ProcessData(SubTaskCmdParams Params)
    {
        throw new NotImplementedException();
    }

    void SetCode()
    {
        //轴下
        var ZXSignals = _templateInst.SignalVars.Where(x => x.Value.DeviceId == _deviceid && x.Value.SignalValueType == null).Select(x => x.Value).FirstOrDefault();
        if (ZXSignals != null && ZXSignals.DaqRate > 0)
        {
            _daqRate = ZXSignals.DaqRate;
            Logger.Error($"SubTaskHightGatherData子任务：_daqRate: {_daqRate}");
        }
        else
        {
            _daqRate = 10000;
        }
        //轴上信号变量
        var Signals = _templateInst.SignalVars.Where(x => x.Value.DeviceId == _deviceid && x.Value.SignalValueType != null).Select(x => x.Value).ToList();
        foreach (var signal in Signals)
        {
            switch (signal.RelatedInfo)
            {
               
                case "CmdFrequency":
                    _frequencySignalCode = signal.Code;
                    break;
               
                default:
                    break;
            }
        }
    }
    
    public void ReParams(SubTaskCmdParams t)
    {
        _isFirstCollected = UtilsForSubTasks.ReadInputVarProperty<bool>(
          _templateName,
          t.SubTaskParams.GetProperty("schedule")
          .GetProperty("control_input_number_of_first_collection_cycles"),
          "IsCheck"
        );
        _firstCollectedCycles = UtilsForSubTasks.ReadVarValue<ulong>(
            _templateName,
            t.SubTaskParams.GetProperty("schedule")
                .GetProperty("control_input_number_of_first_collection_cycles")
        );
        _isFinalCollected = UtilsForSubTasks.ReadInputVarProperty<bool>(
            _templateName,
            t.SubTaskParams.GetProperty("schedule")
                .GetProperty("control_input_number_of_final_collection_cycles"),
            "IsCheck"
        );
        _finalCollectedCycles = UtilsForSubTasks.ReadVarValue<ulong>(
            _templateName,
            t.SubTaskParams.GetProperty("schedule")
                .GetProperty("control_input_number_of_final_collection_cycles")
        );
        
       
        //指定周期
        _isSpecifyCycle = UtilsForSubTasks.ReadInputVarProperty<bool>(
            _templateName,
            t.SubTaskParams.GetProperty("schedule")
                .GetProperty("control_input_characteristic_period_text"),
            "IsCheck"
        );

        // 具体需要采集的周期 前段录入的时候格式为：12,14,16
        string theCycle = UtilsForSubTasks.ReadVarValue<string>(
            _templateName,
            t.SubTaskParams.GetProperty("schedule")
                .GetProperty("control_input_characteristic_period_text")
        );
        _specifyCycle = string.IsNullOrEmpty(theCycle)
            ? new ulong[0]
            : theCycle.Split(',').Select(i => ulong.Parse(i)).ToArray();

        //固定间隔周期
        _isIntervalCollected = UtilsForSubTasks.ReadInputVarProperty<bool>(
            _templateName,
            t.SubTaskParams.GetProperty("schedule")
                .GetProperty("control_input_interval_specified_period"),
            "IsCheck"
        );
        _intervalCollectedCycles = UtilsForSubTasks.ReadVarValue<UInt64>(
            _templateName,
            t.SubTaskParams.GetProperty("schedule")
                .GetProperty("control_input_interval_specified_period")
        );
        //对数参数
        _logarithmicCollect = UtilsForSubTasks.ReadVarValue<bool>(
            _templateName,
            t.SubTaskParams.GetProperty("schedule").GetProperty("control_input_log")
        );
        
        //异常数据
        _isExceptionData = UtilsForSubTasks.ReadVarValue<bool>(
            _templateName,
            t.SubTaskParams.GetProperty("schedule").GetProperty("control_input_abnormal")
        );
        _exceptionDataUpperLimit = UtilsForSubTasks.ReadVarValue<double>(
            _templateName,
            t.SubTaskParams.GetProperty("schedule").GetProperty("control_input_meanValue")
        );
        _exceptionDataLowerLimit = UtilsForSubTasks.ReadVarValue<double>(
            _templateName,
            t.SubTaskParams.GetProperty("schedule").GetProperty("control_input_amplitude")
        );
        _exceptionDataPrecision = UtilsForSubTasks.ReadVarValue<double>(
            _templateName,
            t.SubTaskParams.GetProperty("schedule").GetProperty("control_input_accuracy")
        );
        var daqHandlerParam = new DynamicDaqHandlerParameters(
                  ClassName,
                  _templateInst,
                  daqBuffer,
                  _subtaskID,
                  _sampleInstanceCode,
                  _isSaveDb,
                  _isExceptionData,
                  _exceptionDataUpperLimit,
                  _exceptionDataLowerLimit,
                  _exceptionDataPrecision,
                  _collectMode,
                  _collectFrequency,
                  _finalCollectedCycles,
                  _firstCollectedCycles,
                  _isIntervalCollected,
                  _intervalCollectedCycles,
                  _isSpecifyCycle,
                  _specifyCycle,
                  _frequencySignalCode,
                  _daqRate,
                  _activeCtrlSignalCode,
                  _cyclesSignalCode,
                  _logarithmicCollect,
                  _isFinalCollected,
                  _isFirstCollected,
                  _isDoubleArray,
                  _doubleArrayCode,
                  abnormaldaqBuffer
              );

        _templateInst.CreateOrUpdateDynamicDaqHandler(_bindBufferCode, _processID, daqHandlerParam);
    }
    public bool Run(SubTaskCmdParams t)
    {
        _subTaskCmdParams=t;
        //通过参数获取模板ID、子任务ID、当前试样信息、
        _processID = t!.ProcessID!;
        _subtaskID = t!.SubTaskID!;
        _templateName = t.ClassName!;
        //获取脚本中的该模板的实例化的模板对象
        _templateInst = GetTemplateByName(t.ClassName!);
        if (_templateInst is null)
        {
            Finish(t);
            return false;
        }
       _sampleInstanceCode = _templateInst.CurrentInst.Code;
        var subtasksparams = t!.SubTaskParams.GetProperty("schedule");
      
        MappingAxis mappingAxis = _templateInst!.MappingHardware.GetMappingAxis(
          UtilsForSubTasks.ReadVarValue<object[]>(
               t.ClassName!,
              subtasksparams.GetProperty("control_input_deviceid")
          )
        );
        if (mappingAxis is null)
        {
            Finish(t);
            return false;
        }
        _deviceid = mappingAxis.RealIndex;
        SetCode();
        _isSaveDb = (bool)
                UtilsForSubTasks.ReadVarValue<bool>(
                    _templateName,
                    t.SubTaskParams.GetProperty("schedule").GetProperty("control_input_savedb")
                );
        _cyclesSignalCode =  UtilsForSubTasks.ReadVarValue<string>(
            _templateName,
            t.SubTaskParams.GetProperty("schedule")
                .GetProperty("control_input_cyclesSignalCode")
        );
        _collectMode = UtilsForSubTasks.ReadVarValue<string>(
            _templateName,
            t.SubTaskParams.GetProperty("schedule")
                .GetProperty("control_input_sampling_type")
        );

        _collectFrequency = UtilsForSubTasks.ReadVarValue<double>(
            _templateName,
            t.SubTaskParams.GetProperty("schedule")
                .GetProperty("control_input_sampling_rate")
        );

      
        // TODO  需要从实例化硬件中获取信号变量
        _activeCtrlSignalCode = UtilsForSubTasks.ReadVarValue<string>(
            _templateName,
            t.SubTaskParams.GetProperty("schedule").GetProperty("control_input_signalAbnormal")
        );
        //存入二维数组
        _isDoubleArray = UtilsForSubTasks.ReadInputVarProperty<bool>(
            _templateName,
            t.SubTaskParams.GetProperty("schedule")
                .GetProperty("control_input_Array"),
            "IsCheck"
        );
        _doubleArrayCode = UtilsForSubTasks.ReadVarValue<string>(
            _templateName,
            t.SubTaskParams.GetProperty("schedule")
                .GetProperty("control_input_Array")
        );
        // 计算异常的数据的上限值和下限值
        // 前端 传值周期波的最大值_meanValue 和最小值参数  _exceptionDataLowerLimit
        // 算法 异常数据判定方法为：
        // (_exceptionDataUpperLimit - _exceptionDataLowerLimit)*_exceptionDataPrecision = 可以接受的波动幅度
        // 正常的范围应该为  [_exceptionDataLowerLimit - 可以接受的波动幅度  ,_exceptionDataUpperLimit+可以接受的波动幅度]
        var acceptValue =
            (_exceptionDataUpperLimit - _exceptionDataLowerLimit) * _exceptionDataPrecision * 0.01;
        var upLimit = _exceptionDataUpperLimit + acceptValue;
        var downLimit = _exceptionDataLowerLimit - acceptValue;

        //通知前端该子任务启动
        ISystemBus.SendToUIStatusTopic(GenrateStatusUIJson(t.Cmd()!));
        Logger.Info("高速数据采集子任务 启动:" + t);

      
      
        // 绑定Buffer输入变量
        _bindBufferCode = UtilsForSubTasks.ReadVarValue<string>(
            _templateInst,
            t.SubTaskParams.GetProperty("schedule").GetProperty("control_input_bufferCode")
        );
        daqBuffer = _templateInst.GetVarByName<BufferInputVar>(_bindBufferCode);
        if (_isExceptionData&&t.SubTaskParams.GetProperty("schedule").TryGetProperty("control_input_abnormal_buffer", out JsonElement abnormalBufferCodeElement))
        {
            abnormaldaqBufferCode = UtilsForSubTasks.ReadVarValue<string>(
                _templateInst,
               abnormalBufferCodeElement
            );
            abnormaldaqBuffer = _templateInst.GetVarByName<BufferInputVar>(abnormaldaqBufferCode);
        }
        else
        {
            abnormaldaqBufferCode = null;
            abnormaldaqBuffer= null;
        }
        // 是否清空buffer
        string bufferMode = UtilsForSubTasks.ReadVarValue<string>(_templateInst,
        t.SubTaskParams.GetProperty("schedule").GetProperty("control_input_bufferMode"));
        if (bufferMode == "RESTART")
        {
            if (_isDoubleArray == true)
            {
                _templateInst.GetVarByName<DoubleArrayInputVar>(_doubleArrayCode).ClearDoubleArrayData();
            }
            // 清空缓冲区时, 重置数据表, 通知前端重新开始
            daqBuffer.Reset();
            _templateInst!.Db!.RestartBuffer(_sampleInstanceCode!, _bindBufferCode!);
            if (abnormaldaqBufferCode != null)
            {
                abnormaldaqBuffer!.Reset();
                _templateInst!.Db!.RestartBuffer(_sampleInstanceCode!, abnormaldaqBufferCode);
            }
            ISystemBus.SendToUICmdTopic(JsonSerializer.Serialize(new UICmdParams(
                _processID,
                _subtaskID,
                "BufferReset",
                JsonDocument.Parse(JsonSerializer.Serialize(new
                {
                    SampleInstCode = _sampleInstanceCode,
                    BufferCode = _bindBufferCode,
                    BufferMode = bufferMode
                })).RootElement)));
        }
        ReParams(t);
      

        return true;
    }

    public JsonElement UIParams()
    {
        throw new NotImplementedException();
    }

    //组装发送给前端关于子任务状态的参数
    public string GenrateStatusUIJson(string status_str)
    {
        var uicmdParam = new UIStatusParam(status_str);
        // 构建 JSON 字符串
        var jsonStr = JsonSerializer.Serialize(uicmdParam);

        // 通过解析 JSON 字符串创建 JsonDocument 对象
        using JsonDocument document = JsonDocument.Parse(jsonStr);
        // 获取根节点的 JsonElement
        JsonElement root = document.RootElement;

        // 构建 UICmdParams 对象，并将 UIParams 设置为 JsonElement
        var uicmdParams = new UICmdParams(_processID!, _subtaskID!, "taskStatus", root);

        return JsonSerializer.Serialize(uicmdParams);
    }

    public bool ReStart(SubTaskCmdParams Params)
    {
        throw new NotImplementedException();
    }

    public bool Error(SubTaskCmdParams Params)
    {
        throw new NotImplementedException();
    }

    public void HandleNotify(string notifyTitle, string msg)
    {
        if (notifyTitle == "UpdateParam" &&
         msg == _templateName! + "-" + _subtaskID!)
        {
           
            ReParams(_subTaskCmdParams);
            Logger.Error("重新加载参数，并执行任务");
        }
    }
}
