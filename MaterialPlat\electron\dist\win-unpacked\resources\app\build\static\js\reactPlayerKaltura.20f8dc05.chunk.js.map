{"version": 3, "file": "static/js/reactPlayerKaltura.20f8dc05.chunk.js", "mappings": "wHAAA,IA2BoBA,EA3BhBC,EAAWC,OAAOC,OAClBC,EAAYF,OAAOG,eACnBC,EAAmBJ,OAAOK,yBAC1BC,EAAoBN,OAAOO,oBAC3BC,EAAeR,OAAOS,eACtBC,EAAeV,OAAOW,UAAUC,eAMhCC,EAAcA,CAACC,EAAIC,EAAMC,EAAQC,KACnC,GAAIF,GAAwB,kBAATA,GAAqC,oBAATA,EAC7C,IAAK,IAAIG,KAAOZ,EAAkBS,GAC3BL,EAAaS,KAAKL,EAAII,IAAQA,IAAQF,GACzCd,EAAUY,EAAII,EAAK,CAAEE,IAAKA,IAAML,EAAKG,GAAMG,aAAcJ,EAAOb,EAAiBW,EAAMG,KAASD,EAAKI,aAE3G,OAAOP,CAAE,EAWPQ,EAAgBA,CAACC,EAAKL,EAAKM,KAtBTC,EAACF,EAAKL,EAAKM,KAAUN,KAAOK,EAAMrB,EAAUqB,EAAKL,EAAK,CAAEG,YAAY,EAAMK,cAAc,EAAMC,UAAU,EAAMH,UAAWD,EAAIL,GAAOM,CAAK,EAuB7JC,CAAgBF,EAAoB,kBAARL,EAAmBA,EAAM,GAAKA,EAAKM,GACxDA,GAELI,EAAkB,CAAC,EAzBRC,EAACC,EAAQC,KACtB,IAAK,IAAIC,KAAQD,EACf7B,EAAU4B,EAAQE,EAAM,CAAEZ,IAAKW,EAAIC,GAAOX,YAAY,GAAO,EAwBjEQ,CAASD,EAAiB,CACxBK,QAASA,IAAMC,IAEjBC,EAAOC,SATatC,EASU8B,EATFf,EAAYX,EAAU,CAAC,EAAG,aAAc,CAAEsB,OAAO,IAAS1B,IAUtF,IAAIuC,EAlBUC,EAACxC,EAAKyC,EAAYT,KAAYA,EAAgB,MAAPhC,EAAcC,EAASS,EAAaV,IAAQ,CAAC,EAAGe,GAKnG0B,GAAezC,GAAQA,EAAI0C,WAA8EV,EAAjE5B,EAAU4B,EAAQ,UAAW,CAAEN,MAAO1B,EAAKuB,YAAY,IAC/FvB,IAYiBwC,CAAQG,EAAQ,QAC/BC,EAAeD,EAAQ,OACvBE,EAAkBF,EAAQ,OAG9B,MAAMP,UAAgBG,EAAaO,UACjCC,WAAAA,GACEC,SAASC,WACTzB,EAAc0B,KAAM,aAAcN,EAAaO,YAC/C3B,EAAc0B,KAAM,WAAY,MAChC1B,EAAc0B,KAAM,cAAe,MACnC1B,EAAc0B,KAAM,gBAAiB,MACrC1B,EAAc0B,KAAM,QAAQ,KAC1BA,KAAKC,WAAW,OAAO,IAEzB3B,EAAc0B,KAAM,UAAU,KAC5BA,KAAKC,WAAW,SAAS,IAE3B3B,EAAc0B,KAAM,OAAQE,IAC1BF,KAAKE,OAASA,CAAM,GAExB,CACAC,iBAAAA,GACEH,KAAKI,MAAMC,SAAWL,KAAKI,MAAMC,QAAQL,KAC3C,CACAM,IAAAA,CAAKC,IACH,EAAIb,EAAac,QAvBL,2CACG,YAsB+BC,MAAMC,IAC7CV,KAAKE,SAEVF,KAAKW,OAAS,IAAID,EAASE,OAAOZ,KAAKE,QACvCF,KAAKW,OAAOE,GAAG,SAAS,KACtBC,YAAW,KACTd,KAAKW,OAAOI,SAAU,EACtBf,KAAKW,OAAOK,QAAQhB,KAAKI,MAAMa,MAC3BjB,KAAKI,MAAMc,OACblB,KAAKW,OAAOQ,OAEdnB,KAAKoB,aAAapB,KAAKW,OAAQX,KAAKI,OACpCJ,KAAKI,MAAMiB,SAAS,GACnB,IAAI,IACP,GACDrB,KAAKI,MAAMkB,QAChB,CACAF,YAAAA,CAAaT,EAAQP,GACnBO,EAAOE,GAAG,OAAQT,EAAMmB,QACxBZ,EAAOE,GAAG,QAAST,EAAMoB,SACzBb,EAAOE,GAAG,QAAST,EAAMqB,SACzBd,EAAOE,GAAG,QAAST,EAAMkB,SACzBX,EAAOE,GAAG,cAAca,IAA2B,IAA1B,SAAEC,EAAQ,QAAEC,GAASF,EAC5C1B,KAAK2B,SAAWA,EAChB3B,KAAK6B,YAAcD,CAAO,GAE9B,CACAE,IAAAA,GACE9B,KAAKC,WAAW,OAClB,CACA8B,KAAAA,GACE/B,KAAKC,WAAW,QAClB,CACA+B,IAAAA,GACA,CACAC,MAAAA,CAAOL,GAA6B,IAApBM,IAAWnC,UAAAoC,OAAA,QAAAC,IAAArC,UAAA,KAAAA,UAAA,GACzBC,KAAKC,WAAW,iBAAkB2B,GAC7BM,GACHlC,KAAK+B,OAET,CACAM,SAAAA,CAAUC,GACRtC,KAAKC,WAAW,YAAaqC,EAC/B,CACAtB,OAAAA,CAAQC,GACNjB,KAAKC,WAAW,UAAWgB,EAC7B,CACAsB,WAAAA,GACE,OAAOvC,KAAK2B,QACd,CACAa,cAAAA,GACE,OAAOxC,KAAK6B,WACd,CACAY,gBAAAA,GACE,OAAOzC,KAAK0C,aACd,CACAC,MAAAA,GAKE,OAAuBtD,EAAaJ,QAAQ2D,cAC1C,SACA,CACEC,IAAK7C,KAAK6C,IACVC,IAAK9C,KAAKI,MAAMG,IAChBwC,YAAa,IACbC,UAAW,KACXC,MAXU,CACZC,MAAO,OACPC,OAAQ,QAUNC,MAAO,yCACPC,eAAgB,8BAGtB,EAEF/E,EAAcY,EAAS,cAAe,WACtCZ,EAAcY,EAAS,UAAWS,EAAgB2D,QAAQC,Q", "sources": ["../node_modules/react-player/lib/players/Kaltura.js"], "names": ["mod", "__create", "Object", "create", "__defProp", "defineProperty", "__getOwnPropDesc", "getOwnPropertyDescriptor", "__getOwnPropNames", "getOwnPropertyNames", "__getProtoOf", "getPrototypeOf", "__hasOwnProp", "prototype", "hasOwnProperty", "__copyProps", "to", "from", "except", "desc", "key", "call", "get", "enumerable", "__publicField", "obj", "value", "__defNormalProp", "configurable", "writable", "Kaltura_exports", "__export", "target", "all", "name", "default", "<PERSON><PERSON><PERSON>", "module", "exports", "import_react", "__toESM", "isNodeMode", "__esModule", "require", "import_utils", "import_patterns", "Component", "constructor", "super", "arguments", "this", "callPlayer", "iframe", "componentDidMount", "props", "onMount", "load", "url", "getSDK", "then", "playerjs", "player", "Player", "on", "setTimeout", "isReady", "setLoop", "loop", "muted", "mute", "addListeners", "onReady", "onError", "onPlay", "onPause", "onEnded", "_ref", "duration", "seconds", "currentTime", "play", "pause", "stop", "seekTo", "keepPlaying", "length", "undefined", "setVolume", "fraction", "getDuration", "getCurrentTime", "getSecondsLoaded", "secondsLoaded", "render", "createElement", "ref", "src", "frameBorder", "scrolling", "style", "width", "height", "allow", "referrerPolicy", "canPlay", "kaltura"], "sourceRoot": ""}