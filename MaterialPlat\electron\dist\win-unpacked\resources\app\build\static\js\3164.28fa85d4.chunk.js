"use strict";(self.webpackChunkzhongji_experiment_react=self.webpackChunkzhongji_experiment_react||[]).push([[3164],{3164:(e,t,l)=>{l.r(t),l.d(t,{default:()=>Ee});var a=l(65043),n=l(16569),i=l(63942),s=l(93950),o=l.n(s),r=l(19853),d=l.n(r),c=l(56434),u=l.n(c),p=l(80077),m=l(74117),v=l(70588),h=l(65694),x=l(50540),y=l(45333),g=l(36950),f=l(33154),b=l(10866),j=l(80231),A=l(97292),k=l(67208),_=l(51376),w=l(34458),C=l(41753),S=l(97320),E=l(63379),F=l(91465),I=l(81143),O=l(68374);const D=I.Ay.div`
    width: 100%;
    height: 100%;
    background: ${O.o$.splitBack};  
    box-shadow: 0px 0px 10px 0px rgba(50,106,255,0.04);
    /* border-radius: 8px; */
    border: 1px solid #C0D1FF;
    display: flex;
    flex-direction: column;
    /* overflow: auto; */
    .sample-list {
        padding: ${(0,O.D0)("10px")};
        font-weight: 600;
        color: #333333;
        line-height: 20px;
        flex-shrink: 0;
    }
`,N=I.Ay.div`
    .unique { 
        border-bottom: 1px solid rgba(220 ,220, 220,1);
        padding: 2px
    }
    .disabled {
        cursor: no-drop;
    }
    .unique-content {
        padding: 2px;
    }

`;var P=l(6051),R=l(95206),T=l(18650);const L=I.Ay.div`
    padding: ${(0,O.D0)("3px")};
    padding-left: ${(0,O.D0)("13px")};
    background: #FFFFFF;
    box-shadow: 0px 4px 6px 0px rgba(0,22,121,0.17);
    border-radius: 8px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 4vh;
    .img-style {
        width: ${(0,O.D0)("30px")};
        height: ${(0,O.D0)("30px")};
    } 
    .ant-btn.ant-btn-sm {
        border: none !important;
        background: none !important;
        padding: 0;
        padding-right: ${(0,O.D0)("20px")};
        box-shadow: none
    }
`,q=(0,a.createContext)(),B={samples:[],sample_type:"",num_type:"one",num:1,data:[]},$={labelCol:{flex:"300px"},wrapperCol:{flex:"1"}},V="GROUPING",U="sample",z="create",H="update",G=e=>!("children"in e);var M=l(70579);const K=e=>{let{opt:t={},onDisable:l,onDel:a,onAdd:n,readOnly:i,dataSource:s}=e;const{subTaskSample:o}=(0,p.d4)((e=>e.subTask)),r=()=>!(null!==t&&void 0!==t&&t),d=()=>(null===s||void 0===s?void 0:s.flatMap((e=>e.children)).length)>1||(0,w.HN)();return(0,M.jsx)(L,{children:(0,M.jsxs)(P.A,{size:1,children:[(0,M.jsx)(R.Ay,{disabled:i||o,size:"small",onClick:()=>n(V),children:(0,M.jsx)("img",{src:T.kE,alt:"",className:"img-style",title:"\u65b0\u589e\u5206\u7ec4"})}),(0,M.jsx)(R.Ay,{size:"small",onClick:()=>n(U),disabled:i||!("children"in t)&&!("parent_group"in t)||o,children:(0,M.jsx)("img",{src:T.k0,alt:"",className:"img-style",title:"\u65b0\u589e\u8bd5\u6837"})}),(0,M.jsx)(R.Ay,{size:"small",onClick:()=>a(),disabled:i||!d()||r()||o,children:(0,M.jsx)("img",{src:T.En,alt:"",className:"img-style",title:"\u5220\u9664"})}),(0,M.jsx)(R.Ay,{size:"small",onClick:()=>l(),disabled:i||!d()||r()||o||(null===t||void 0===t?void 0:t.disabled),children:(0,M.jsx)("img",{src:T.dH,alt:"",className:"img-style",title:"\u7981\u7528"})})]})})};var X=l(35964),Y=l(83720),J=l(56543);const Z=I.Ay.div`
    padding: ${(0,O.D0)("15px")};
    overflow-y: auto;
    flex: 1;
    min-height: 0;
    .ant-tree {
        background: none;
        .ant-tree-node-content-wrapper.ant-tree-node-selected {
            background-color: rgba(215, 226, 255, 1);
        }
    }

    .background-color-list-item {
        background-color:  #F5F5F5;
    }
    .circle {
        border-radius: 50%;
        width: 1vh;
        height: 1vh;
    }
`,W=I.Ay.div`

    display: flex;
    /* align-items: center; */
    .tree-node-icon {
        margin-right: 0.5vw;
        display: flex;
        align-items: center;
        justify-content:center ;
    }
    .checkbox {
        padding-right: 6px;
    }
    .tree-node-title {
        background-color: ${e=>e.selected?"rgba(66,111,255,0.1)":"transparent"};
        width: 100%;
        display: flex;
        justify-content: space-between;
        .right-layout {
            >img {
                height: 15px;
                width: 15px;
            }
        }
        .disabled {
            cursor: no-drop;
        }
        img {
            margin-left: 5px;
            height: 15px;
            width: 15px;
        }
        .title-content {
            display: flex;
            align-items: center;
            img {
                height: ${(0,O.D0)("14px")};
                width: ${(0,O.D0)("14px")};
            }
        }
    }


`,{TreeNode:Q}=X.A,ee=e=>{let{data:t,icon:l,readOnly:i,selected:s="",menu:o}=e;const[r]=n.Ay.useMessage(),{t:d}=(0,m.Bd)(),{initSampleTree:c}=(0,b.A)(),u=(0,p.d4)((e=>e.subTask.openExperiment)),[v,h]=(0,a.useState)(!1),[x,y]=(0,a.useState)(),g=(0,a.useRef)();(0,a.useEffect)((()=>{var e;v&&(null===(e=g.current)||void 0===e||e.focus())}),[v]),(0,a.useEffect)((()=>{y(t.name)}),[t.name,v]);const f=async()=>{let e;if(void 0!==t.children)e=await(0,k.eMH)({group_name:x,id:t.key});else{const l={...t,name:x,id:t.key};e=await(0,k.jSz)({sample_instance:l})}e&&(c(),r.open({type:"success",content:d("\u64cd\u4f5c\u6210\u529f")}))},j=e=>{let{status:t,disabled:l}=e;if(l)return T.Rx;switch(t){case J.$y.FINISHED:return T.F2;case J.$y.PAUSE:return T.dd;case J.$y.ABORTED:return T.DE;case J.$y.RUNNING:return T.QS;default:return""}},A={"data-nodedata":JSON.stringify(t)};return(0,M.jsxs)(W,{...A,selected:s===(null===t||void 0===t?void 0:t.key),children:[l&&(0,M.jsx)("div",{...A,className:"tree-node-icon",children:l}),(0,M.jsx)("div",{className:"tree-node-title",...A,children:(0,M.jsxs)("div",{className:"title-content",...A,children:[v?(0,M.jsx)(Y.A,{...A,ref:g,style:{width:"60%"},value:x,onChange:e=>{y(e.target.value)},onClick:e=>{e.stopPropagation(),e.preventDefault()},onBlur:()=>{y(t.name),h(!1)},size:"small",maxLength:32}):d(t.name),j(t)&&(0,M.jsx)("img",{...A,src:j(t),alt:""}),!i&&(0,M.jsx)("div",{className:"right-layout",onClick:e=>{e.stopPropagation(),e.preventDefault(),u||(v?f():y(o.name),h(!v))},...A,children:t.key===(null===o||void 0===o?void 0:o.key)?v?(0,M.jsx)("img",{...A,src:T.Iw,alt:""}):(0,M.jsx)("img",{...A,src:T.fu,alt:""}):null})]})})]})},te=e=>{let{dataSource:t=[],handleOpt:l,onHeadOk:i,selectedData:s=[],readOnly:r,multiSample:d=[],onClickChange:c}=e;const u=(0,a.useContext)(q),h=(0,a.useRef)(!1),x=(0,a.useRef)(!1),[y,g]=n.Ay.useMessage(),[f,j]=(0,a.useState)(),{t:A}=(0,m.Bd)(),{initSampleTree:_}=(0,b.A)(),w=(0,p.d4)((e=>e.subTask.openExperiment)),C=(0,a.useRef)();(0,a.useLayoutEffect)((()=>{var e;const t=null===(e=C.current)||void 0===e?void 0:e.firstElementChild;return t&&t.addEventListener("mousemove",E),()=>{t&&t.removeEventListener("mousemove",E)}}),[t]);const S=(0,a.useRef)();(0,v.vC)(["shift","ctrl"],(e=>{const{shiftKey:t,ctrlKey:l}=e;h.current=t,x.current=l,S.current&&clearTimeout(S.current),S.current=setTimeout((()=>{h.current=!1,x.current=!1,S.current=null}),10)}),{keyup:!0,keydown:!0});const E=(0,a.useCallback)(o()((e=>{var t;if(null!==(t=e.target.dataset)&&void 0!==t&&t.nodedata){var l;const t=JSON.parse(null===(l=e.target.dataset)||void 0===l?void 0:l.nodedata);u.menu.current=t,j(t)}else u.menu.current=void 0,j(null)}),100),[]);return(0,M.jsx)(Z,{ref:C,children:t&&t.length>0&&(0,M.jsx)(X.A,{defaultExpandAll:!0,selectedKeys:[...s,...d],blockNode:!0,multiple:!0,disabled:w,draggable:{icon:!1},onDrop:async e=>{var l,a;const n=e.dragNode.key,i=e.node.key;let s,o;t.forEach((e=>{e.children.forEach((e=>{e.key===n&&(s={...e})}))})),t.forEach((e=>{e.children.forEach((t=>{t.key===i&&(o=e)}))}));const r={...s,checked:!1,parent_group:(null===(l=o)||void 0===l?void 0:l.key)||i,id:null===(a=s)||void 0===a?void 0:a.key};if(s){await(0,k.jSz)({sample_instance:r})&&(_(),y.open({type:"success",content:A("\u64cd\u4f5c\u6210\u529f")}))}},onSelect:async(e,a)=>{const{title:{props:{data:n}}}=a.node;let i=[];if(h.current){const e=null===t||void 0===t?void 0:t.flatMap((e=>{var t;return[null===e||void 0===e?void 0:e.key,...null===e||void 0===e||null===(t=e.children)||void 0===t?void 0:t.map((e=>null===e||void 0===e?void 0:e.key))]})),l=null===e||void 0===e?void 0:e.findIndex((e=>e===s[0])),a=null===e||void 0===e?void 0:e.findIndex((e=>e===n.key));i=l<=a?e.slice(l,a+1):e.slice(a,l+1)}x.current&&(i=[...new Set([...s,...d,null===n||void 0===n?void 0:n.key])]),await l(n,i),i.length<=0&&c("checkCurrent"),i.length>0&&c("checkNumber")},children:t.map((e=>{var t;return(0,M.jsx)(Q,{title:(0,M.jsx)(ee,{selected:s[0],onHeadOk:i,readOnly:r,menu:f,data:e}),children:e.children&&(null===(t=e.children)||void 0===t?void 0:t.map((t=>(0,M.jsx)(Q,{parentId:e.key,title:(0,M.jsx)(ee,{selected:s[0],onHeadOk:i,readOnly:r,menu:f,data:t,icon:(0,M.jsx)("div",{className:"circle",style:{background:(t.disabled?"#A9A9A9":t.color)||"#FFF"}})})},t.key))))},e.key)}))})})};var le=l(36497),ae=l(32513),ne=l(25055),ie=l(96603),se=l(97914),oe=l(75440),re=l(4554),de=l(9339),ce=l(54962);const ue=I.Ay.div`
    display: flex;
    justify-content: space-between;
    .left-layout {
        width: 64vw;
        display: flex;
        justify-content: space-between;
        padding: 20px;
        .sample-left {
            width: 40vw;
        } 
        .sample-right { 
            width: 20vw;
        }
    }

    .button-layout {
        width: 8vw;
        display: flex;
        flex-direction:column ;
    }
    /* .table-layout {
        width: 40vw;
    } */

`,pe=I.Ay.div`
    display: flex;
    justify-content: space-between;
    height: 30vh;
    .select-layout {
        display: flex;
        width: 19vw;
        overflow: auto;
        height: 25vh;
        .checkbox-layout {
            display:flex ;
            padding: 20px;
            .ant-checkbox-group {
                flex-direction: column;
            }
            .ant-checkbox-wrapper {
                margin-inline-start: 0;
                margin-bottom: 10px;
            }
        }
    }
    .button-layout {
        width: 8vw;
        display: flex;
        flex-direction: column; 
        .button {
            margin-top: 10px;
        }
    }
`,me=e=>{let{value:t={units_id:"",value:"",name:""},onChange:l,unitData:n={},type:i}=e;const[s,o]=(0,a.useState)(t.value),[r,d]=(0,a.useState)(n.default_unit_id),[c,u]=(0,a.useState)(n.code),p=e=>{null===l||void 0===l||l({value:s,name:c,units_id:r,...e})};return(0,M.jsxs)(P.A,{children:[(0,M.jsx)(Y.A,{value:t.val||s,onChange:e=>{o(e.target.value),p({value:e.target.value})}}),!i&&(0,M.jsx)(le.A,{value:t.unit||r,showSearch:!0,optionFilterProp:"name",style:{width:"5.5vw"},fieldNames:{label:"name",value:"id"},onChange:e=>{const t=n.units.find((t=>t.id===e)).name;d(e),u(t),p({units_id:e,name:t})},options:n.units})]})},ve=e=>{var t;let{value:l=[],onChange:n,data:i,saveSampleAbout:s}=e;const{t:o}=(0,m.Bd)(),[r,d]=(0,a.useState)(l),[c,p]=(0,a.useState)(!1),[v,h]=(0,a.useState)(u()(i)),[x,y]=(0,a.useState)([]);return(0,M.jsxs)(P.A,{children:[(0,M.jsx)(Y.A,{value:null===(t=r.map((e=>e.name)))||void 0===t?void 0:t.join("/"),disabled:!0}),(0,M.jsx)(R.Ay,{onClick:()=>{p(!0)},children:o("\u9009\u62e9")}),(0,M.jsx)(oe.A,{open:c,width:"30vw",footer:null,onCancel:()=>p(!1),title:o("\u8f93\u5165\u4e0e\u8bd5\u6837\u76f8\u5173\u7684\u6570\u636e"),children:(0,M.jsxs)(pe,{children:[(0,M.jsx)(ce.A,{title:o("\u9009\u62e9"),children:(0,M.jsx)("div",{className:"select-layout",children:(0,M.jsx)("div",{className:"checkbox-layout",children:(0,M.jsx)(ae.A.Group,{onChange:e=>{y(e)},value:x,children:v.map((e=>(0,M.jsx)(ae.A,{value:e.id,children:e.isEdit?(0,M.jsx)(Y.A,{defaultValue:e.name,style:{width:"70%"},onChange:t=>((e,t)=>{h(v.map((l=>l.id===t.id?{...l,name:e.target.value}:l)))})(t,e)}):e.name},e.id)))})})})}),(0,M.jsx)("div",{className:"button-layout",children:(0,M.jsxs)(P.A,{direction:"vertical",children:[(0,M.jsx)(re.A,{block:!0,onClick:()=>{const e=v.filter((e=>x.includes(e.id)));d(e),null===n||void 0===n||n(e),s(v),p(!1)},children:o("\u786e\u5b9a")}),(0,M.jsx)(re.A,{block:!0,onClick:()=>{p(!1),h(i),y(l.map((e=>e.id)))},children:o("\u53d6\u6d88")}),(0,M.jsx)(re.A,{block:!0,onClick:(e,t)=>{h([...v,{id:String(v.length+1),name:"",isEdit:!0}])},children:o("\u6dfb\u52a0")}),(0,M.jsx)(re.A,{block:!0,onClick:()=>{h(v.map((e=>x.includes(e.id)?{...e,isEdit:!0}:e)))},children:o("\u7f16\u8f91")}),(0,M.jsx)(re.A,{block:!0,onClick:()=>{h(v.filter((e=>!x.includes(e.id))))},children:o("\u5220\u9664")})]})})]})})]})},he=e=>{let{open:t,handleOpen:l,init:i,sampleList:s=[],unitList:o,onOk:r,sampleAbout:d,saveSampleAbout:c}=e;const{t:p}=(0,m.Bd)(),[v]=ne.A.useForm(),[h,x]=(0,a.useState)("one"),[y,g]=(0,a.useState)([]),[f,b]=(0,a.useState)(),j=()=>{b([]),g([]),x("one")},A=()=>{l(!1),v.resetFields(),j()},k=e=>{const t={};y.forEach((e=>{t[e.parameter_id]={...e}})),b(Array.from({length:e}).map((e=>({...t,id:crypto.randomUUID()}))))};return(0,M.jsx)(oe.A,{open:t,width:"75vw",title:p("\u8bd5\u6837\u8f93\u5165\u63a7\u4ef6"),onCancel:A,footer:null,children:(0,M.jsxs)(ue,{children:[(0,M.jsx)(ce.A,{children:(0,M.jsxs)("div",{className:"left-layout",children:[(0,M.jsx)("div",{className:"sample-left",children:(0,M.jsxs)(ne.A,{...$,labelAlign:"left",form:v,initialValues:{...i},children:[(0,M.jsx)(ne.A.Item,{label:p("\u8f93\u5165\u4e0e\u8bd5\u6837\u76f8\u5173\u7684\u6570\u636e"),name:"samples",children:(0,M.jsx)(ve,{data:d,saveSampleAbout:c,onChange:e=>{const t=e.map((e=>(e.parameter_name=e.name,e.parameter_id=e.id,e.type="else",e)));g([...y.filter((e=>!("id"in e))),...t])}})}),(0,M.jsx)(ne.A.Item,{label:p("\u8bd5\u6837\u5f62\u72b6(\u7528\u4e8e\u8ba1\u7b97\u6a2a\u622a\u9762\u79ef)"),name:"sample_type",rules:[{required:!0}],children:(0,M.jsx)(le.A,{style:{width:"20vw"},showSearch:!0,optionFilterProp:"sample_name",fieldNames:{label:"sample_name",value:"sample_id"},options:s.map((e=>({...e,sample_name:p(e.sample_name)}))),onChange:(e,t)=>{const l=u()(null===t||void 0===t?void 0:t.parameters),a=null===l||void 0===l?void 0:l.map((e=>{var t;return e.name=null===o||void 0===o||null===(t=o.find((t=>t.id===e.dimension_id)))||void 0===t?void 0:t.code,e}));g([...y.filter((e=>"id"in e)),...a])}})}),"one"===h&&(0,M.jsx)(ne.A.Item,{label:p("\u5185\u90e8\u540d\u79f0"),name:"code",rules:[{required:!0}],children:(0,M.jsx)(Y.A,{style:{width:"20vw"}})}),(0,M.jsx)(ne.A.Item,{label:p("\u8bd5\u6837\u6570\u91cf"),name:"num_type",children:(0,M.jsxs)(ie.Ay.Group,{onChange:e=>{x(e.target.value),k(1)},children:[(0,M.jsx)(ie.Ay,{value:"one",children:p("\u5355\u6839")}),(0,M.jsx)(ie.Ay,{value:"more",children:p("\u591a\u6839")})]})}),"one"===h?null===y||void 0===y?void 0:y.map((e=>(0,M.jsx)(ne.A.Item,{label:e.parameter_name,name:e.parameter_id,children:(0,M.jsx)(me,{type:e.type,unitData:null===o||void 0===o?void 0:o.find((t=>t.id===e.dimension_id)),onChange:t=>{g(y.map((l=>l.parameter_id===e.parameter_id?{...l,...t}:l)))}})},e.parameter_id))):(0,M.jsxs)(M.Fragment,{children:[(0,M.jsx)(ne.A.Item,{label:p("\u8bd5\u6837\u6570\u91cf"),name:"num",children:(0,M.jsx)(se.A,{min:1,onChange:k})}),(0,M.jsx)("div",{className:"table-layout",children:(0,M.jsx)(de.A,{dataSource:f,scroll:{x:"25vw",y:"33vh"},columns:(_=y,[{title:"\u5185\u90e8\u540d\u79f0",dataIndex:"value",key:"code",width:"20vw",ellipsis:!0,render:(e,t)=>(0,M.jsx)(ne.A.Item,{label:"",name:"code:"+t.id,children:(0,M.jsx)(Y.A,{onChange:e=>{console.log(f),b(f.map((l=>l.id===t.id?{...l,code:e.target.value}:l)))}})})},...null===_||void 0===_?void 0:_.map((e=>({title:e.parameter_name,dataIndex:"value",key:e.parameter_id,width:"20vw",ellipsis:!0,render:(t,l)=>(0,M.jsx)(ne.A.Item,{label:"",name:e.parameter_id+":"+l.id,children:(0,M.jsx)(me,{type:e.type,unitData:null===o||void 0===o?void 0:o.find((t=>t.id===e.dimension_id)),onChange:t=>{l[e.parameter_id]={...l[e.parameter_id],...t},b(f.map((e=>e.id===l.id?l:e)))}})})})))]),pagination:!1})})]})]})}),(0,M.jsx)("div",{className:"sample-right",children:p("\u56fe\u7247")})]})}),(0,M.jsx)("div",{className:"button-layout",children:(0,M.jsxs)(P.A,{direction:"vertical",children:[(0,M.jsx)(re.A,{block:!0,onClick:()=>(async()=>{const e=await v.validateFields();"one"===e.num_type||0!==f.filter((e=>{var t;return null!==(t=null===e||void 0===e?void 0:e.code)&&void 0!==t&&t})).length?(r({...e,data:"one"===e.num_type?[y.map((t=>({...t,code:e.code})))]:f.map((e=>(delete e.id,Object.values(e).filter((e=>"string"!==typeof e)).map((t=>({...t,code:e.code}))))))}),v.resetFields(),j()):n.Ay.error(p("\u672a\u586b\u5199\u5185\u90e8\u540d\u79f0"))})(),children:p("\u786e\u8ba4")}),(0,M.jsx)(re.A,{block:!0,onClick:()=>A(),children:p("\u53d6\u6d88")})]})})]})});var _},xe=I.Ay.div`
    display: flex;
    justify-content:space-between ;
    .button-layout {
        width: 8vw;
        display: flex;
        flex-direction:column ;
    }
`,ye=e=>{let{data:t,open:l,onOk:a,onCancel:n,type:i}=e;const{t:s}=(0,m.Bd)(),[o]=ne.A.useForm(),r=i===H;return(0,M.jsx)(M.Fragment,{children:(0,M.jsx)(oe.A,{open:l,title:s(r?"\u7f16\u8f91":"\u6dfb\u52a0"),width:"33vw",onCancel:n,footer:null,children:(0,M.jsxs)(xe,{children:[(0,M.jsxs)(ne.A,{form:o,initialValues:r?{...t}:{},labelCol:{span:8},wrapperCol:{span:16},children:[(0,M.jsx)(ne.A.Item,{label:s("\u63cf\u8ff0"),name:"name",rules:[{required:!0}],children:(0,M.jsx)(Y.A,{})}),(!("children"in t)||!r)&&(0,M.jsx)(ne.A.Item,{label:s("\u5185\u90e8\u540d\u79f0"),name:"code",rules:[{required:!0}],children:(0,M.jsx)(Y.A,{disabled:r})})]}),(0,M.jsx)("div",{className:"button-layout",children:(0,M.jsxs)(P.A,{direction:"vertical",children:[(0,M.jsx)(re.A,{block:!0,onClick:()=>{o.validateFields().then((e=>{a({...t,...e,type:i})}))},children:s("\u786e\u8ba4")}),(0,M.jsx)(re.A,{block:!0,onClick:n,children:s("\u53d6\u6d88")})]})})]})})})};var ge=l(8918),fe=l(68358);const be=I.Ay.div`
    .atom-button-attr{
        max-width: 300px;
        .title{
            margin-top: 12px;
            margin-bottom: 12px;
            font-size: 14px;
            padding-left: 21px;
        }
    }
    .ant-space-item{
        .background-layout{
            border: 1px solid rgba(0,0,0,.2);
        }
    }
`,{useForm:je,Item:Ae}=ne.A,ke=e=>{let{open:t,onClose:l,setConfig:n,config:i}=e;const[s]=je();(0,a.useEffect)((()=>{d()(i,s.getFieldsValue())||s.setFieldsValue(i)}),[i]);return(0,M.jsx)(fe.A,{open:t,onClose:l,style:{width:"500px"},children:(0,M.jsx)(be,{children:(0,M.jsx)(ne.A,{form:s,onValuesChange:(e,t)=>{null===n||void 0===n||n(t)},children:(0,M.jsx)(ge.A,{defaultActiveKey:"attr",items:[{key:"event",label:"\u4e8b\u4ef6",forceRender:!0,children:(0,M.jsxs)(M.Fragment,{children:[(0,M.jsx)(Ae,{label:"\u5f53\u524d\u8bd5\u6837\u53d8\u91cf\u53d8\u5316\u65f6\u89e6\u53d1",name:["event","checkCurrent"],children:(0,M.jsx)(E.A,{})}),(0,M.jsx)(Ae,{label:"\u9009\u62e9\u8bd5\u6837\u603b\u6570\u53d8\u5316\u65f6\u89e6\u53d1",name:["event","checkNumber"],children:(0,M.jsx)(E.A,{})}),(0,M.jsx)(Ae,{label:"\u8bd5\u6837\u6570\u91cf\u53d8\u5316\u65f6\u89e6\u53d1",name:["event","number"],children:(0,M.jsx)(E.A,{})})]})}]})})})})};var _e=l(57692),we=l(49612);const Ce=e=>{let{data:t,multiSample:l}=e;const{t:a}=(0,m.Bd)(),s=(0,p.d4)((e=>e.template.exportList)),o=async e=>{if(null!==e&&void 0!==e&&e.code){const h=[...new Set([...t.flatMap((e=>e.children)).filter((e=>l.includes(e.key))).map((e=>e.code)),e.code])];let x;try{try{var s,o,r;const e=await(0,k.OFk)({export_type:"excel",codes:h});x=i.A.info({title:a("\u5bfc\u51faExcel"),content:(0,M.jsxs)("div",{children:[(0,M.jsx)("p",{children:a("\u6b63\u5728\u51c6\u5907\u5bfc\u51fa\u6570\u636e...")}),(0,M.jsx)(_e.A,{percent:0,status:"active"})]}),okButtonProps:{style:{display:"none"}},cancelButtonProps:{style:{display:"none"}},maskClosable:!1}),null===(s=x)||void 0===s||s.update({content:(0,M.jsxs)("div",{children:[(0,M.jsx)("p",{children:a("\u521d\u59cb\u5316Excel\u6587\u4ef6...")}),(0,M.jsx)(_e.A,{percent:10,status:"active"})]})});const t=50,l=await(0,k.Ow3)({TemplateName:e["template-name"],ExportReportFilePath:e["export-file-path"],SampleInsts:e["sample-insts"],ResultVars:e["result-vars"],ResultVarStatisticalPatterns:e["result-var-statistical-patterns"],HistoricalDataParams:e["historical-data-params"],PageSize:t,HistoricalDataPageSize:1e4}),{totalTestResultPages:u,sampleHistoricalDataInfos:p}=l,{totalSampleCount:m}=e,v=u+p.reduce(((e,t)=>e+t.totalPages),0);let y=0;for(let n=0;n<u;n+=1){var d;y+=1;const i=Math.floor(10+y/v*80);null===(d=x)||void 0===d||d.update({content:(0,M.jsxs)("div",{children:[(0,M.jsxs)("p",{children:[a("\u6b63\u5728\u5bfc\u51fa\u6d4b\u8bd5\u7ed3\u679c\u6570\u636e...")," (",n+1,"/",u,")"]}),(0,M.jsx)(_e.A,{percent:i,status:"active"})]})}),await(0,k.s$x)({exportReportFilePath:l.exportReportFilePath,sampleInstCodes:h,pageIndex:n,pageSize:t,resultVars:e["result-vars"],resultVarStatisticalPatterns:e["result-var-statistical-patterns"],templateName:e["template-name"],sampleInsts:e["sample-insts"]})}for(const n of p)for(let t=0;t<n.totalPages;t+=1){var c;y+=1;const i=Math.floor(10+y/v*80);null===(c=x)||void 0===c||c.update({content:(0,M.jsxs)("div",{children:[(0,M.jsxs)("p",{children:[a("\u6b63\u5728\u5bfc\u51fa\u5386\u53f2\u6570\u636e...")," ",n.sampleName," (",t+1,"/",n.totalPages,")"]}),(0,M.jsx)(_e.A,{percent:i,status:"active"})]})}),await(0,k.s$x)({exportReportFilePath:l.exportReportFilePath,templateName:e["template-name"],sampleInsts:e["sample-insts"],sampleInstCodes:h,pageIndex:0,pageSize:50,resultVars:e["result-vars"],resultVarStatisticalPatterns:e["result-var-statistical-patterns"],historicalDataParams:e["historical-data-params"],targetSampleCode:n.sampleCode,historicalDataPageIndex:t,historicalDataPageSize:null})}null===(o=x)||void 0===o||o.update({content:(0,M.jsxs)("div",{children:[(0,M.jsx)("p",{children:a("\u5b8c\u6210\u5bfc\u51fa...")}),(0,M.jsx)(_e.A,{percent:95,status:"active"})]})}),await(0,k.K2k)({exportReportFilePath:l.exportReportFilePath}),null===(r=x)||void 0===r||r.destroy(),n.Ay.success(a("\u5bfc\u51faExcel\u6210\u529f"))}catch(m){var u;null===(u=x)||void 0===u||u.destroy(),console.error("\u5904\u7406 Excel \u65f6\u51fa\u9519:",m)}}catch(v){var p;null===(p=x)||void 0===p||p.destroy(),console.error(v),n.Ay.error(a("\u5bfc\u51faExcel\u5931\u8d25\uff1a")+v.message)}}},r=async e=>{try{await(0,k.GyH)({templateName:(0,g.n1)(),arrayCode:null===e||void 0===e?void 0:e.excel_double_array_code,codes:null===e||void 0===e?void 0:e.excel_double_array_col_code,path:null===e||void 0===e?void 0:e.export_path,fileName:null===e||void 0===e?void 0:e.export_name}),n.Ay.success(a("\u5bfc\u51fa\u4e8c\u7ef4\u6570\u7ec4\u6570\u636eExcel\u6210\u529f"))}catch(t){console.log("error",t),n.Ay.error(a("\u5bfc\u51fa\u4e8c\u7ef4\u6570\u7ec4\u6570\u636eExcel\u5931\u8d25"))}};return{onExcel:async e=>{const t=s.find((e=>e.export_type===we.sd.EXCEL&&e.default_flag));(null===t||void 0===t?void 0:t.excel_data_source_type)!==we.uy.\u4e8c\u7ef4\u6570\u7ec4?o(e):r(t)}}},Se=e=>{let{domId:t,layoutConfig:l,onDisable:n,onExcel:i,onCSV:s,onCopy:o,setTingHandle:r}=e;const d=(0,a.useContext)(q),c=(0,p.d4)((e=>e.subTask.openExperiment)),{t:u}=(0,m.Bd)(),[v,h]=(0,a.useState)(),[x,y]=(0,a.useState)();return(0,M.jsx)(N,{children:(0,M.jsxs)(j.A,{domId:t,onBefore:()=>{d.menu.current&&(y(d.menu.current),h(!0))},layoutConfig:l,onClose:()=>{h(!1),y()},children:[(0,M.jsx)(M.Fragment,{children:(0,M.jsx)("div",{className:"unique-content",onClick:()=>{r()},children:u("\u7f16\u8f91\u89e6\u53d1\u4e8b\u4ef6")})}),v&&(0,M.jsx)(M.Fragment,{children:(0,M.jsx)("div",{className:"unique-content",onClick:()=>{let e=!x.disabled;null!==x&&void 0!==x&&x.code||(e=!(null!==x&&void 0!==x&&x.children.every((e=>e.disabled)))),v&&n(e,x)},children:null!==x&&void 0!==x&&x.code?u(null!==x&&void 0!==x&&x.disabled?"\u89e3\u9664":"\u65e0\u6548"):u(null!==x&&void 0!==x&&x.children.every((e=>e.disabled))?"\u89e3\u9664":"\u65e0\u6548")})}),(0,w.HN)()&&v&&!c&&(0,M.jsxs)(M.Fragment,{children:[(0,M.jsx)("div",{className:"unique-content",onClick:()=>{v&&i(x)},children:u("Excel\u8f93\u51fa")}),(0,M.jsx)("div",{className:"unique-content",onClick:()=>{v&&s(x)},children:u("CSV\u8f93\u51fa")})]})]})})},Ee=e=>{let{item:t,id:l,layoutConfig:s}=e;const[r,c]=n.Ay.useMessage(),{initUnitsData:j}=(0,f.A)(),{initVideoData:w}=(0,C.A)(),{initSampleTree:I,updateOptSample:O,initDefaultSample:N,getSamples:P,initSampleAboutList:R,batchUpdateSample:T}=(0,b.A)(),{t:L}=(0,m.Bd)(),$=(0,p.wA)(),U=(0,p.d4)((e=>e.global.unitList)),X=(0,p.d4)((e=>e.project.sampleData)),Y=(0,p.d4)((e=>e.project.sampleList)),J=(0,p.d4)((e=>e.project.sampleAboutList)),Z=(0,p.d4)((e=>e.project.optSample)),W=(0,p.d4)((e=>e.project.defaultSample)),Q=(0,p.d4)((e=>e.project.multiSample)),ee=(0,p.d4)((e=>e.template.videoList)),[le,ae]=(0,a.useState)([]),[ne,ie]=(0,a.useState)(),[se,oe]=(0,a.useState)([]),[re,de]=(0,a.useState)(!1),[ce,ue]=(0,a.useState)(!1),[pe,me]=(0,a.useState)(z),ve=(0,a.useRef)(),{updateLayoutItem:xe}=(0,S.A)(),{onEvent:ge}=(0,E.s)(),[fe,be]=(0,a.useState)(),[je,Ae]=(0,a.useState)(!1),_e=(0,a.useRef)(!1),we=(0,a.useRef)(!1),{onExcel:Ee}=Ce({data:le,multiSample:Q}),Fe=(0,a.useRef)();(0,v.vC)(["shift","ctrl"],(e=>{const{shiftKey:t,ctrlKey:l}=e;_e.current=t,we.current=l,Fe.current&&clearTimeout(Fe.current),Fe.current=setTimeout((()=>{_e.current=!1,we.current=!1,Fe.current=null}),10)}),{keyup:!0,keydown:!0}),(0,a.useEffect)((()=>{try{if(null!==t&&void 0!==t&&t.data_source){const{comp_config:e}=JSON.parse(null===t||void 0===t?void 0:t.data_source);d()(e,fe)||be(e)}}catch(e){console.log("err",e)}}),[null===t||void 0===t?void 0:t.data_source]),(0,a.useEffect)((()=>{ae(X)}),[X]),(0,a.useEffect)((()=>{Z&&(oe([Z.key]),ie(Z))}),[Z]);const Ie=async e=>{try{await(0,k.hHf)({sample_tree:e})&&await Oe()}catch(t){throw console.log(t),t}},Oe=async()=>{await I(),N(),r.open({type:"success",content:L("\u64cd\u4f5c\u6210\u529f")})},De=(e,t,l)=>e.map((e=>{var a;return e.key===t?l(e):null!==(a=e.children)&&void 0!==a&&a&&(e.children=De(e.children,t,l)),e})),Ne=async function(){let e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0],t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:ne,l=[];G(t)&&(l=[t]),null!==t&&void 0!==t&&t.children&&(null===t||void 0===t?void 0:t.children.length)>0&&(l=[...l,...null===t||void 0===t?void 0:t.children]),Q&&Q.length>0&&(l=[...l,...P().filter((e=>Q.includes(e.key)))]);const a=await T(Array.from(new Map(l.map((e=>[e.id,e]))).values()).map((t=>({...t,disabled:e}))));a&&await Oe()},Pe=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];const t=ee.filter((t=>null===e||void 0===e?void 0:e.includes(t.sample_id)));Re(t)},Re=async e=>{try{await Promise.allSettled(e.map((e=>(0,k.f9d)({video_id:null===e||void 0===e?void 0:e.video_id,video_file:null===e||void 0===e?void 0:e.video_file})))),w()}catch(t){throw console.log(t),t}},Te=o()((async e=>{if(e===V){const e=await(0,k.XKO)({group_name:`${L("\u5206\u7ec4")+(le.length+1)}`}),{count:t}=null===le||void 0===le?void 0:le[0];return await qe((0,g.qD)({type:z,name:`\u8bd5\u6837${t+1}`,code:`1${(0,g.vx)()}`},A.O.SAMPLE),{...le[0],key:e.id}),await Oe(),void Be("number")}if(e!==V&&1===(null===le||void 0===le?void 0:le.length)){const{count:e}=le.find((e=>e.key===le[0].key));return await qe((0,g.qD)({type:z,name:`\u8bd5\u6837${e+1}`,code:`${e+1}${(0,g.vx)()}`},A.O.SAMPLE),le[0]),void Be("number")}if(e!==V){let e=ne;if(G(ne))e={...ne,key:ne.parent_group};else if(!ne)return void n.Ay.error(L("\u8bf7\u9009\u62e9\u4e00\u4e2a\u5206\u7ec4\u6216\u8bd5\u6837"));if(null===W||void 0===W||!W.id)return void n.Ay.error(L("\u672a\u8bbe\u7f6e\u8bd5\u6837\u9ed8\u8ba4\u53c2\u6570"));me(z);const{count:t}=le.find((t=>t.key===e.key));await qe((0,g.qD)({type:z,name:`\u8bd5\u6837${t+1}`,code:`${t+1}${(0,g.vx)()}`},A.O.SAMPLE),e),Be("number")}}),300),Le=(e,t)=>{const{data:l,sample_type:a,samples:n,code:i}=e;return l.map(((e,l)=>({key:crypto.randomUUID(),name:`\u8bd5\u6837${t+l+1}`,data:e,samples:n,code:i||e[0].code,color:(0,g.bE)(),sample_type:a,disabled:!1,new:!0})))},qe=async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:ne;const{type:l,name:a,code:n}=e;if(l===z){const e=crypto.randomUUID(),l={key:e,name:a,code:n,data:(null===W||void 0===W?void 0:W.data)||[],samples:(null===W||void 0===W?void 0:W.samples)||[],color:"#FFFFFF",sample_type:(null===W||void 0===W?void 0:W.sample_type)||"",new:!0,disabled:!1,parent_group:t.key,id:e};ue(!1);await(0,k.yl9)({sample_instance:l})&&await Oe()}else await Ie(De(u()(le),e.key,(t=>{t.name=e.name}))),ie(e),ue(!1)},Be=e=>{const{event:t}=fe||{},{checkCurrent:l,checkNumber:a,number:n}=t||{};"checkCurrent"===e&&ge(l),"checkNumber"===e&&ge(a),"number"===e&&ge(n)};return(0,M.jsxs)(q.Provider,{value:{menu:ve},children:[(0,M.jsxs)(D,{children:[c,(0,M.jsx)("div",{className:"sample-list",children:L("\u8bd5\u6837\u5217\u8868")}),(0,M.jsx)(K,{handleBachAdd:()=>{j(),(async()=>{try{const e=await(0,k.GpS)();e&&$({type:h.OJ,param:e})}catch(e){throw console.log(e),e}})(),de(!0)},opt:ne,onDisable:Ne,onEdit:()=>{me(H),ue(!0)},onDel:async()=>{if(ne){const e=le.filter((e=>[...Q,ne.key].includes(e.key))).flatMap((e=>e.children)).map((e=>e.key)),t=P().filter((e=>[...Q,ne.key].includes(e.key))).flatMap((e=>e.key)),l=[...new Set([...e,...t,ne.key,...Q])];i.A.confirm({title:L("\u786e\u8ba4\u5220\u9664\uff1f"),icon:(0,M.jsx)(_.A,{}),content:(0,M.jsx)("div",{style:{color:"red"},children:L("\u5f53\u524d\u64cd\u4f5c\u76f4\u63a5\u4ece\u6570\u636e\u5e93\u5220\u9664\u6570\u636e\u4e14\u65e0\u6cd5\u6062\u590d\uff0c\u8bf7\u786e\u8ba4\u64cd\u4f5c")}),okText:L("\u786e\u8ba4"),cancelText:L("\u53d6\u6d88"),onOk:async()=>{var e;await(0,k.SbX)({ids:l}),Pe(l),e=Q.filter((e=>!l.includes(e))),$({type:h.Z4,param:e}),await Oe(),w(),Be("number"),$({type:x.MY,param:{UIParams:{shortcutCode:y.JN.\u4fdd\u5b58}}})},onCancel(){}})}},onAdd:Te,dataSource:le}),(0,M.jsx)(te,{dataSource:le,handleOpt:async(e,t)=>{var l,a;let n=e,i=t;var s;if(!G(n))if(_e.current&&(null===(s=n)||void 0===s?void 0:s.key)!==(null===ne||void 0===ne?void 0:ne.key)){var o,r;i=[...new Set([...t,...null===(o=n)||void 0===o||null===(r=o.children)||void 0===r?void 0:r.map((e=>e.key))])]}else{const e=(null===Q||void 0===Q?void 0:Q.length)>0?Q:se;i=[...e]}i&&(null===(l=i)||void 0===l?void 0:l.length)>0&&(null===ne||void 0===ne?void 0:ne.key)===(null===(a=n)||void 0===a?void 0:a.key)&&(i=i.filter((e=>e!==n.key)),i&&i.length>0&&(n=P().find((e=>e.key===i[i.length-1])))),n&&(await O(G(n)?n:Z,i,!0),ie(n),oe([n.key])),$({type:h.Z4,param:i})},multiSample:Q,onHeadOk:qe,selectedData:se,onClickChange:Be}),(0,M.jsx)(he,{open:re,handleOpen:de,sampleList:Y,unitList:U,onOk:e=>{const t=u()(le).map((t=>(t.key===ne.key&&(t.children=[...t.children,...Le(e,t.children.length)]),t)));Ie(t),de(!1)},init:{...B},sampleAbout:J,saveSampleAbout:async e=>{try{await(0,k.WKV)({instance_about_List:e})&&R()}catch(t){throw console.log(t),t}}}),ce&&(0,M.jsx)(ye,{open:ce,data:ne,onCancel:()=>{ue(!1)},type:pe,onOk:qe}),(0,M.jsx)(Se,{domId:l,layoutConfig:s,onDisable:Ne,onExcel:Ee,onCSV:async e=>{if(null!==e&&void 0!==e&&e.code)try{var t,l;const a=await(0,k.Zqy)({sample_code:null===e||void 0===e?void 0:e.code}),i=(null===a||void 0===a||null===(t=a.value)||void 0===t?void 0:t.taskId)||(null===a||void 0===a||null===(l=a.value)||void 0===l?void 0:l.task_id);if(!i)return void n.Ay.error(L("\u542f\u52a8\u5bfc\u51fa\u4efb\u52a1\u5931\u8d25"));const s=$((0,F.J_)("\u5bfc\u51fa\u4e2d..."));n.Ay.success((0,M.jsxs)("div",{children:[(0,M.jsx)("div",{children:L("CSV\u5bfc\u51fa\u4efb\u52a1\u5df2\u542f\u52a8")}),(0,M.jsxs)("div",{style:{fontSize:"12px",color:"#666"},children:["\u4efb\u52a1ID:"," ",i]})]}));const o=async()=>{try{var e,t,l,a;const v=await(0,k.taW)({task_id:i});var r,d;if("Completed"===(null===(e=v.value)||void 0===e||null===(t=e.status)||void 0===t?void 0:t.status))n.Ay.success(L(null===(r=v.value)||void 0===r||null===(d=r.status)||void 0===d?void 0:d.message)),$((0,F.ge)(s));else if("Failed"===(null===(l=v.value)||void 0===l||null===(a=l.status)||void 0===a?void 0:a.status))n.Ay.error(L("CSV\u5bfc\u51fa\u5931\u8d25")),$((0,F.ge)(s));else{var c,u,p,m;setTimeout(o,3e3),$((0,F.dU)(s,`\u5bfc\u51fa\u8fdb\u5ea6\uff1a${null===(c=v.value)||void 0===c||null===(u=c.status)||void 0===u?void 0:u.completedFiles}/${null===(p=v.value)||void 0===p||null===(m=p.status)||void 0===m?void 0:m.totalFiles}`))}}catch(v){console.log("\u68c0\u67e5\u5bfc\u51fa\u72b6\u6001\u5931\u8d25:",v),$((0,F.ge)(s))}};setTimeout(o,3e3)}catch(a){console.error(a),n.Ay.error(L("\u542f\u52a8CSV\u5bfc\u51fa\u5931\u8d25"))}},onCopy:async()=>{if(!ne)return;await(0,k.IVU)({id:ne.id})&&(n.Ay.success("\u590d\u5236\u6210\u529f"),I())},setTingHandle:()=>{Ae(!0)}})]}),(0,M.jsx)(ke,{open:je,onClose:()=>{Ae(!1),xe({layout:s,newItem:{...t,data_source:JSON.stringify({comp_config:fe})}})},setConfig:be,config:fe})]})}},49612:(e,t,l)=>{l.d(t,{Ak:()=>p,E4:()=>m,J3:()=>d,KA:()=>u,Kc:()=>r,PH:()=>n,U6:()=>i,_7:()=>v,ah:()=>h,k9:()=>s,pm:()=>c,sd:()=>a,uy:()=>o,vO:()=>x,zc:()=>y});const a={EXCEL:"excel",PDF:"pdf",CSV:"csv"},n={XLS:"xls",XLSX:"xlsx"},i={PDF:"pdf"},s={CSV:"csv"},o={buffer:"buffer","\u4e8c\u7ef4\u6570\u7ec4":"doubleArray"},r={base:"\u2014\u2014\u2014\u2014\u2014\u2014",dottedLine:"------"},d={"\u6a2a\u6392":"row","\u7ad6\u6392":"column"},c={HEADER:"header",FOOTER:"footer",BODY:"body"},u={HEADER:"1",BODY:"2",FOOTER:"3"},p={"\u5c45\u4e2d":"center","\u5de6\u5bf9\u9f50":"left","\u53f3\u5bf9\u9f50":"right"},m=[{value:8,label:"8"},{value:9,label:"9"},{value:10,label:"10"},{value:11,label:"11"},{value:12,label:"12"},{value:13,label:"13"},{value:14,label:"14"},{value:15,label:"15"},{value:16,label:"16"},{value:17,label:"17"},{value:18,label:"18"},{value:19,label:"19"},{value:20,label:"20"},{value:21,label:"21"},{value:22,label:"22"},{value:23,label:"23"}],v="16",h={"1/10\u9875":"default","1\u9875":"current","\u5f53\u524d1\u9875, \u603b10\u9875":"all"},x=[{layout_type:c.HEADER,id:u.HEADER,title:"\u9875\u7709",children:[]},{layout_type:c.BODY,id:u.BODY,title:"\u4e3b\u4f53",children:[]},{layout_type:c.FOOTER,id:u.FOOTER,title:"\u9875\u811a",children:[]}],y={ID:{name:"id",label:"id",rules:{required:!0,message:"\u8bf7\u8f93\u5165id"}},EXPORT_TYPE:{name:"export_type",label:"\u8f93\u51fa\u7c7b\u578b",rules:{required:!0,message:"\u8bf7\u8f93\u5165\u8f93\u51fa\u7c7b\u578b"}},FILE_TYPE:{name:"file_type",label:"\u6587\u4ef6\u7c7b\u578b",rules:{required:!0,message:"\u8bf7\u8f93\u5165\u6587\u4ef6\u7c7b\u578b"}},EXPORT_NAME:{name:"export_name",label:"\u5bfc\u51fa\u540d\u79f0",rules:{required:!0,message:"\u8bf7\u8f93\u5165\u5bfc\u51fa\u540d\u79f0"}},EXPORT_PATH:{name:"export_path",label:"\u5bfc\u51fa\u8def\u5f84",rules:{required:!0,message:"\u8bf7\u9009\u62e9\u5bfc\u51fa\u8def\u5f84"}},DEFAULT_FLAG:{name:"default_flag",label:"\u9ed8\u8ba4",rules:{required:!0,message:"\u8bf7\u9009\u62e9\u9ed8\u8ba4"}},REMARK:{name:"remark",label:"\u5907\u6ce8",rules:{required:!1,message:"\u8bf7\u8f93\u5165\u5907\u6ce8"}},SIGNAL_CONFIG:{name:"signal_config",label:"\u9009\u62e9\u901a\u9053",rules:{required:!1,message:"\u8bf7\u9009\u62e9\u901a\u9053"}},SIGNAL_CONFIG_X:{name:"signal_config_x",label:"Excel-X\u8f74",rules:{required:!1,message:"\u8bf7\u9009\u62e9x\u8f74\u901a\u9053"}},SIGNAL_CONFIG_Y:{name:"signal_config_y",label:"Excel-Y\u8f74",rules:{required:!1,message:"\u8bf7\u9009\u62e9y\u8f74\u901a\u9053"}},SIGNAL_CONFIG_BUFFER_CODE:{name:"buffer_code",label:"Buffer",rules:{required:!1,message:"\u8bf7\u9009\u62e9Buffer"},extra:"buffer\u5fc5\u987b\u5b58\u5728\u914d\u7f6e\u7684\u4fe1\u53f7\u53d8\u91cf"},INPUT_CONFIG:{name:"input_config",label:"\u9009\u62e9\u53c2\u6570",rules:{required:!1,message:"\u8bf7\u9009\u62e9\u53c2\u6570"},extra:"\u6ce8\u610f\uff1a\u53ea\u80fd\u9009\u62e9\u6587\u672c\u548c\u6570\u5b57\u7c7b\u578b\u7684\u53c2\u6570\u3002"},RESULT_CONFIG:{name:"result_config",label:"\u9009\u62e9\u6d4b\u8bd5\u7ed3\u679c",rules:{required:!1,message:"\u8bf7\u9009\u62e9\u6d4b\u8bd5\u7ed3\u679c"}},STATISTIC_CONFIG:{name:"statistic_config",label:"\u9009\u62e9\u7edf\u8ba1\u7ed3\u679c",rules:{required:!1,message:"\u8bf7\u9009\u62e9\u7edf\u8ba1\u7ed3\u679c"},extra:"\u6ce8\u610f\uff1a\u53ea\u5bf9\u6240\u9009\u7684\u6d4b\u8bd5\u7ed3\u679c\u8fdb\u884c\u7edf\u8ba1\u3002"},PDF_CONFIG:{name:"pdf_config",label:"\u8bf7\u5e03\u5c40pdf",rules:{required:!1,message:"\u8bf7\u9009\u62e9\u7edf\u8ba1\u7ed3\u679c"}},CSV_UIBUFFER:{name:"csv_buffer",label:"UI\u7f13\u51b2\u533a",rules:{required:!0,message:"\u8bf7\u9009\u62e9UI\u7f13\u51b2\u533a"}},CSV_DATABASE:{name:"csv_database",label:"\u6570\u636e\u8868",rules:{required:!0,message:"\u8bf7\u9009\u62e9\u6570\u636e\u8868"}},DATA_SOURCE_TYPE:{name:"excel_data_source_type",label:"\u6570\u636e\u6e90\u7c7b\u578b",rules:{required:!0,message:"\u8bf7\u9009\u62e9\u6570\u636e\u6e90\u7c7b\u578b"}},DOUBLE_ARRAY_CODE:{name:"excel_double_array_code",label:"\u4e8c\u7ef4\u6570\u7ec4",rules:{required:!1,message:"\u8bf7\u9009\u62e9\u4e8c\u7ef4\u6570\u7ec4"}},DOUBLE_ARRAY_COL_CODE:{name:"excel_double_array_col_code",label:"\u4e8c\u7ef4\u6570\u7ec4\u5217",rules:{required:!1,message:"\u8bf7\u9009\u62e9\u4e8c\u7ef4\u6570\u7ec4"}}}},63379:(e,t,l)=>{l.d(t,{A:()=>p,s:()=>u});var a=l(65043),n=l(6051),i=l(95206),s=l(74117),o=l(16090),r=l(67208),d=l(70579);const c=(0,a.lazy)((()=>l.e(7206).then(l.bind(l,97206)))),u=()=>{const{startAction:e}=(0,o.A)(),t=async t=>{t&&await e({action_id:String(t)})},l=async e=>{e&&await(0,r.O5k)({script:e,result_type:"BOOL"})};return{onEvent:e=>{try{if(e){const{action_id:a,execute_type:n,script:i}=e;"action"===n&&t(a),"script"===n&&l(i)}}catch(a){console.log("error",a)}}}},p=e=>{let{id:t,value:l,onChange:o}=e;const{t:r}=(0,s.Bd)(),[u,p]=(0,a.useState)(!1),m=()=>{p(!0)};return(0,d.jsxs)("div",{children:[l?(0,d.jsx)(d.Fragment,{children:(0,d.jsxs)(n.A,{children:[(0,d.jsx)(i.Ay,{onClick:()=>m(),children:r("\u7f16\u8f91")}),(0,d.jsx)(i.Ay,{onClick:()=>o(),children:r("\u5220\u9664")})]})}):(0,d.jsx)(i.Ay,{onClick:()=>m(),children:r("\u70b9\u51fb\u6dfb\u52a0\u4e8b\u4ef6")}),(0,d.jsx)(a.Suspense,{fallback:(0,d.jsx)(d.Fragment,{}),children:u&&(0,d.jsx)(c,{open:u,setOpen:p,event:l,callback:o})})]})}},68358:(e,t,l)=>{l.d(t,{A:()=>m});var a=l(65043),n=l(48677),i=l(80077),s=l(14463),o=l(25055),r=l(36282),d=l(96603),c=l(14524),u=l(70579);const p=e=>{let{setting:t,onChange:l}=e;const[n]=o.A.useForm();(0,a.useEffect)((()=>{n.setFieldsValue({...t})}),[t]);return(0,u.jsx)(r.A,{content:(0,u.jsxs)(o.A,{form:n,name:"basic",labelCol:{style:{width:35}},onValuesChange:(e,t)=>{l(t)},children:[(0,u.jsx)(o.A.Item,{label:"\u4f4d\u7f6e",name:"placement",children:(0,u.jsxs)(d.Ay.Group,{size:"small",children:[(0,u.jsx)(d.Ay.Button,{value:"top",children:"\u4e0a"}),(0,u.jsx)(d.Ay.Button,{value:"right",children:"\u53f3"}),(0,u.jsx)(d.Ay.Button,{value:"bottom",children:"\u4e0b"}),(0,u.jsx)(d.Ay.Button,{value:"left",children:"\u5de6"})]})}),(0,u.jsx)(o.A.Item,{label:"\u5c3a\u5bf8",name:"size",children:(0,u.jsxs)(d.Ay.Group,{size:"small",children:[(0,u.jsx)(d.Ay.Button,{value:"default",children:"\u6b63\u5e38"}),(0,u.jsx)(d.Ay.Button,{value:"large",children:"\u5927"})]})})]}),title:"",trigger:"click",placement:"leftTop",children:(0,u.jsx)(c.A,{})})},m=e=>{let{children:t,open:l,onClose:a}=e;const o=(0,i.wA)(),{drawSetting:r}=(0,i.d4)((e=>e.split));return(0,u.jsx)(u.Fragment,{children:l&&(0,u.jsx)(n.A,{open:l,size:null===r||void 0===r?void 0:r.size,placement:null===r||void 0===r?void 0:r.placement,onClose:a,extra:(0,u.jsx)(p,{setting:r,onChange:e=>{o({type:s.cd,param:e})}}),children:t})})}},97320:(e,t,l)=>{l.d(t,{A:()=>r});l(65043);var a=l(80077),n=l(84856),i=l(67208),s=l(14463),o=l(41086);const r=()=>{const e=(0,a.wA)(),{saveLayout:t}=(0,n.A)(),l=async t=>{let{layout:l,newItem:a}=t;const n={...l,children:r(l.children,a)},[d]=await(0,i.PXE)({binder_ids:[null===l||void 0===l?void 0:l.binder_id]});await(0,i.Kv3)({binders:[{...d,layout:(0,o.gT)(n,null===l||void 0===l?void 0:l.binder_id)}]}),e({type:s.EH,param:d.binder_id})},r=(e,t)=>e.map((e=>e.id===t.id?t:e.children&&e.children.length>0?{...e,children:r(e.children,t)}:e)),d=async e=>{let{layout:l,newItem:a}=e;const n={...l,children:r(l.children,a)};await t(n)};return{updateLayoutItem:async e=>{let{layout:t,newItem:a}=e;null!==t&&void 0!==t&&t.binder_id?(console.log("\u754c\u9762\u5185\u5bb9-tab"),await l({layout:t,newItem:a})):(console.log("\u754c\u9762\u5185\u5bb9-\u4e3b\u7a97\u53e3"),await d({layout:t,newItem:a}))}}}}}]);
//# sourceMappingURL=3164.28fa85d4.chunk.js.map