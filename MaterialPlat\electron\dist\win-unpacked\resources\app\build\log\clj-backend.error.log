2025-09-10 15:32:44,699 [main] ERROR jdbc.audit - 1. Connection.prepareStatement(SELECT
*
FROM t_system_version
WHERE 1 = 1




LIMIT 1;
;) SELECT * FROM t_system_version WHERE 1 = 1 LIMIT 1; ; 
 
org.sqlite.SQLiteException: [SQLITE_ERROR] SQL error or missing database (no such table: t_system_version)
	at org.sqlite.core.DB.newSQLException(DB.java:1030)
	at org.sqlite.core.DB.newSQLException(DB.java:1042)
	at org.sqlite.core.DB.throwex(DB.java:1007)
	at org.sqlite.core.NativeDB.prepare_utf8(Native Method)
	at org.sqlite.core.NativeDB.prepare(NativeDB.java:137)
	at org.sqlite.core.DB.prepare(DB.java:257)
	at org.sqlite.core.CorePreparedStatement.<init>(CorePreparedStatement.java:45)
	at org.sqlite.jdbc3.JDBC3PreparedStatement.<init>(JDBC3PreparedStatement.java:30)
	at org.sqlite.jdbc4.JDBC4PreparedStatement.<init>(JDBC4PreparedStatement.java:25)
	at org.sqlite.jdbc4.JDBC4Connection.prepareStatement(JDBC4Connection.java:35)
	at org.sqlite.jdbc3.JDBC3Connection.prepareStatement(JDBC3Connection.java:241)
	at org.sqlite.jdbc3.JDBC3Connection.prepareStatement(JDBC3Connection.java:205)
	at net.sf.log4jdbc.ConnectionSpy.prepareStatement(ConnectionSpy.java:388)
	at next.jdbc.prepare$create.invokeStatic(prepare.clj:133)
	at next.jdbc.prepare$create.invoke(prepare.clj:83)
	at next.jdbc.result_set$fn__6670.invokeStatic(result_set.clj:882)
	at next.jdbc.result_set$fn__6670.invoke(result_set.clj:851)
	at next.jdbc.protocols$fn__5921$G__5916__5930.invoke(protocols.clj:33)
	at next.jdbc$execute_BANG_.invokeStatic(jdbc.clj:253)
	at next.jdbc$execute_BANG_.invoke(jdbc.clj:237)
	at hugsql.adapter.next_jdbc.HugsqlAdapterNextJdbc.query(next_jdbc.clj:20)
	at hugsql.adapter$fn__4770$G__4741__4775.invoke(adapter.clj:3)
	at hugsql.adapter$fn__4770$G__4740__4781.invoke(adapter.clj:3)
	at clojure.lang.Var.invoke(Var.java:401)
	at hugsql.core$db_fn_STAR_$y__5039.doInvoke(core.clj:472)
	at clojure.lang.RestFn.invoke(RestFn.java:448)
	at hugsql.core$db_fn_STAR_$y__5039.invoke(core.clj:462)
	at conman.core$try_query$fn__6913$fn__6914.invoke(core.clj:32)
	at clj_backend.common.common_db$fn__7527$f__6933__auto____7554.invoke(common_db.clj:6)
	at clj_backend.common.db_utils$fn__8989$fn__8990.invoke(db_utils.clj:248)
	at clj_backend.common.db_utils$fn__8989.invokeStatic(db_utils.clj:247)
	at clj_backend.common.db_utils$fn__8989.invoke(db_utils.clj:244)
	at mount.core$record_BANG_.invokeStatic(core.cljc:74)
	at mount.core$record_BANG_.invoke(core.cljc:73)
	at mount.core$up$fn__7243.invoke(core.cljc:81)
	at mount.core$up.invokeStatic(core.cljc:80)
	at mount.core$up.invoke(core.cljc:78)
	at mount.core$bring.invokeStatic(core.cljc:247)
	at mount.core$bring.invoke(core.cljc:239)
	at mount.core$start.invokeStatic(core.cljc:289)
	at mount.core$start.doInvoke(core.cljc:281)
	at clojure.lang.RestFn.applyTo(RestFn.java:140)
	at clojure.core$apply.invokeStatic(core.clj:667)
	at clojure.core$apply.invoke(core.clj:662)
	at mount.core$start_with_args.invokeStatic(core.cljc:388)
	at mount.core$start_with_args.doInvoke(core.cljc:385)
	at clojure.lang.RestFn.invoke(RestFn.java:463)
	at clj_backend.core$_main.invokeStatic(core.clj:161)
	at clj_backend.core$_main.doInvoke(core.clj:157)
	at clojure.lang.RestFn.applyTo(RestFn.java:140)
	at clj_backend.core.main(Unknown Source)
2025-09-10 15:32:44,701 [main] ERROR jdbc.sqltiming - 1. Connection.prepareStatement(SELECT
*
FROM t_system_version
WHERE 1 = 1




LIMIT 1;
;) FAILED! SELECT * FROM t_system_version WHERE 1 = 1 LIMIT 1; ; 
 {FAILED after -1 msec} 
org.sqlite.SQLiteException: [SQLITE_ERROR] SQL error or missing database (no such table: t_system_version)
	at org.sqlite.core.DB.newSQLException(DB.java:1030)
	at org.sqlite.core.DB.newSQLException(DB.java:1042)
	at org.sqlite.core.DB.throwex(DB.java:1007)
	at org.sqlite.core.NativeDB.prepare_utf8(Native Method)
	at org.sqlite.core.NativeDB.prepare(NativeDB.java:137)
	at org.sqlite.core.DB.prepare(DB.java:257)
	at org.sqlite.core.CorePreparedStatement.<init>(CorePreparedStatement.java:45)
	at org.sqlite.jdbc3.JDBC3PreparedStatement.<init>(JDBC3PreparedStatement.java:30)
	at org.sqlite.jdbc4.JDBC4PreparedStatement.<init>(JDBC4PreparedStatement.java:25)
	at org.sqlite.jdbc4.JDBC4Connection.prepareStatement(JDBC4Connection.java:35)
	at org.sqlite.jdbc3.JDBC3Connection.prepareStatement(JDBC3Connection.java:241)
	at org.sqlite.jdbc3.JDBC3Connection.prepareStatement(JDBC3Connection.java:205)
	at net.sf.log4jdbc.ConnectionSpy.prepareStatement(ConnectionSpy.java:388)
	at next.jdbc.prepare$create.invokeStatic(prepare.clj:133)
	at next.jdbc.prepare$create.invoke(prepare.clj:83)
	at next.jdbc.result_set$fn__6670.invokeStatic(result_set.clj:882)
	at next.jdbc.result_set$fn__6670.invoke(result_set.clj:851)
	at next.jdbc.protocols$fn__5921$G__5916__5930.invoke(protocols.clj:33)
	at next.jdbc$execute_BANG_.invokeStatic(jdbc.clj:253)
	at next.jdbc$execute_BANG_.invoke(jdbc.clj:237)
	at hugsql.adapter.next_jdbc.HugsqlAdapterNextJdbc.query(next_jdbc.clj:20)
	at hugsql.adapter$fn__4770$G__4741__4775.invoke(adapter.clj:3)
	at hugsql.adapter$fn__4770$G__4740__4781.invoke(adapter.clj:3)
	at clojure.lang.Var.invoke(Var.java:401)
	at hugsql.core$db_fn_STAR_$y__5039.doInvoke(core.clj:472)
	at clojure.lang.RestFn.invoke(RestFn.java:448)
	at hugsql.core$db_fn_STAR_$y__5039.invoke(core.clj:462)
	at conman.core$try_query$fn__6913$fn__6914.invoke(core.clj:32)
	at clj_backend.common.common_db$fn__7527$f__6933__auto____7554.invoke(common_db.clj:6)
	at clj_backend.common.db_utils$fn__8989$fn__8990.invoke(db_utils.clj:248)
	at clj_backend.common.db_utils$fn__8989.invokeStatic(db_utils.clj:247)
	at clj_backend.common.db_utils$fn__8989.invoke(db_utils.clj:244)
	at mount.core$record_BANG_.invokeStatic(core.cljc:74)
	at mount.core$record_BANG_.invoke(core.cljc:73)
	at mount.core$up$fn__7243.invoke(core.cljc:81)
	at mount.core$up.invokeStatic(core.cljc:80)
	at mount.core$up.invoke(core.cljc:78)
	at mount.core$bring.invokeStatic(core.cljc:247)
	at mount.core$bring.invoke(core.cljc:239)
	at mount.core$start.invokeStatic(core.cljc:289)
	at mount.core$start.doInvoke(core.cljc:281)
	at clojure.lang.RestFn.applyTo(RestFn.java:140)
	at clojure.core$apply.invokeStatic(core.clj:667)
	at clojure.core$apply.invoke(core.clj:662)
	at mount.core$start_with_args.invokeStatic(core.cljc:388)
	at mount.core$start_with_args.doInvoke(core.cljc:385)
	at clojure.lang.RestFn.invoke(RestFn.java:463)
	at clj_backend.core$_main.invokeStatic(core.clj:161)
	at clj_backend.core$_main.doInvoke(core.clj:157)
	at clojure.lang.RestFn.applyTo(RestFn.java:140)
	at clj_backend.core.main(Unknown Source)
2025-09-10 15:32:44,969 [main] ERROR jdbc.audit - 2. Connection.prepareStatement(SELECT 1 FROM schema_migrations) SELECT 1 FROM schema_migrations 
 
org.sqlite.SQLiteException: [SQLITE_ERROR] SQL error or missing database (no such table: schema_migrations)
	at org.sqlite.core.DB.newSQLException(DB.java:1030)
	at org.sqlite.core.DB.newSQLException(DB.java:1042)
	at org.sqlite.core.DB.throwex(DB.java:1007)
	at org.sqlite.core.NativeDB.prepare_utf8(Native Method)
	at org.sqlite.core.NativeDB.prepare(NativeDB.java:137)
	at org.sqlite.core.DB.prepare(DB.java:257)
	at org.sqlite.core.CorePreparedStatement.<init>(CorePreparedStatement.java:45)
	at org.sqlite.jdbc3.JDBC3PreparedStatement.<init>(JDBC3PreparedStatement.java:30)
	at org.sqlite.jdbc4.JDBC4PreparedStatement.<init>(JDBC4PreparedStatement.java:25)
	at org.sqlite.jdbc4.JDBC4Connection.prepareStatement(JDBC4Connection.java:35)
	at org.sqlite.jdbc3.JDBC3Connection.prepareStatement(JDBC3Connection.java:241)
	at org.sqlite.jdbc3.JDBC3Connection.prepareStatement(JDBC3Connection.java:205)
	at net.sf.log4jdbc.ConnectionSpy.prepareStatement(ConnectionSpy.java:388)
	at next.jdbc.prepare$create.invokeStatic(prepare.clj:133)
	at next.jdbc.prepare$create.invoke(prepare.clj:83)
	at next.jdbc.result_set$fn__6670.invokeStatic(result_set.clj:882)
	at next.jdbc.result_set$fn__6670.invoke(result_set.clj:851)
	at next.jdbc.protocols$fn__5921$G__5916__5930.invoke(protocols.clj:33)
	at next.jdbc$execute_BANG_.invokeStatic(jdbc.clj:253)
	at next.jdbc$execute_BANG_.invoke(jdbc.clj:237)
	at next.jdbc.sql$query.invokeStatic(sql.clj:104)
	at next.jdbc.sql$query.invoke(sql.clj:95)
	at next.jdbc.sql$query.invokeStatic(sql.clj:101)
	at next.jdbc.sql$query.invoke(sql.clj:95)
	at migratus.database$table_exists_QMARK_.invokeStatic(database.clj:189)
	at migratus.database$table_exists_QMARK_.invoke(database.clj:177)
	at migratus.database$init_schema_BANG_.invokeStatic(database.clj:247)
	at migratus.database$init_schema_BANG_.invoke(database.clj:239)
	at migratus.database.Database.connect(database.clj:312)
	at migratus.core$run.invokeStatic(core.clj:53)
	at migratus.core$run.invoke(core.clj:50)
	at migratus.core$reset.invokeStatic(core.clj:167)
	at migratus.core$reset.invoke(core.clj:163)
	at luminus_migrations.core$fn__8879.invokeStatic(core.clj:28)
	at luminus_migrations.core$fn__8879.invoke(core.clj:27)
	at luminus_migrations.core$migrate.invokeStatic(core.clj:98)
	at luminus_migrations.core$migrate.invoke(core.clj:84)
	at clj_backend.core$_main.invokeStatic(core.clj:168)
	at clj_backend.core$_main.doInvoke(core.clj:157)
	at clojure.lang.RestFn.applyTo(RestFn.java:140)
	at clj_backend.core.main(Unknown Source)
2025-09-10 15:32:44,970 [main] ERROR jdbc.sqltiming - 2. Connection.prepareStatement(SELECT 1 FROM schema_migrations) FAILED! SELECT 1 FROM schema_migrations 
 {FAILED after -1 msec} 
org.sqlite.SQLiteException: [SQLITE_ERROR] SQL error or missing database (no such table: schema_migrations)
	at org.sqlite.core.DB.newSQLException(DB.java:1030)
	at org.sqlite.core.DB.newSQLException(DB.java:1042)
	at org.sqlite.core.DB.throwex(DB.java:1007)
	at org.sqlite.core.NativeDB.prepare_utf8(Native Method)
	at org.sqlite.core.NativeDB.prepare(NativeDB.java:137)
	at org.sqlite.core.DB.prepare(DB.java:257)
	at org.sqlite.core.CorePreparedStatement.<init>(CorePreparedStatement.java:45)
	at org.sqlite.jdbc3.JDBC3PreparedStatement.<init>(JDBC3PreparedStatement.java:30)
	at org.sqlite.jdbc4.JDBC4PreparedStatement.<init>(JDBC4PreparedStatement.java:25)
	at org.sqlite.jdbc4.JDBC4Connection.prepareStatement(JDBC4Connection.java:35)
	at org.sqlite.jdbc3.JDBC3Connection.prepareStatement(JDBC3Connection.java:241)
	at org.sqlite.jdbc3.JDBC3Connection.prepareStatement(JDBC3Connection.java:205)
	at net.sf.log4jdbc.ConnectionSpy.prepareStatement(ConnectionSpy.java:388)
	at next.jdbc.prepare$create.invokeStatic(prepare.clj:133)
	at next.jdbc.prepare$create.invoke(prepare.clj:83)
	at next.jdbc.result_set$fn__6670.invokeStatic(result_set.clj:882)
	at next.jdbc.result_set$fn__6670.invoke(result_set.clj:851)
	at next.jdbc.protocols$fn__5921$G__5916__5930.invoke(protocols.clj:33)
	at next.jdbc$execute_BANG_.invokeStatic(jdbc.clj:253)
	at next.jdbc$execute_BANG_.invoke(jdbc.clj:237)
	at next.jdbc.sql$query.invokeStatic(sql.clj:104)
	at next.jdbc.sql$query.invoke(sql.clj:95)
	at next.jdbc.sql$query.invokeStatic(sql.clj:101)
	at next.jdbc.sql$query.invoke(sql.clj:95)
	at migratus.database$table_exists_QMARK_.invokeStatic(database.clj:189)
	at migratus.database$table_exists_QMARK_.invoke(database.clj:177)
	at migratus.database$init_schema_BANG_.invokeStatic(database.clj:247)
	at migratus.database$init_schema_BANG_.invoke(database.clj:239)
	at migratus.database.Database.connect(database.clj:312)
	at migratus.core$run.invokeStatic(core.clj:53)
	at migratus.core$run.invoke(core.clj:50)
	at migratus.core$reset.invokeStatic(core.clj:167)
	at migratus.core$reset.invoke(core.clj:163)
	at luminus_migrations.core$fn__8879.invokeStatic(core.clj:28)
	at luminus_migrations.core$fn__8879.invoke(core.clj:27)
	at luminus_migrations.core$migrate.invokeStatic(core.clj:98)
	at luminus_migrations.core$migrate.invoke(core.clj:84)
	at clj_backend.core$_main.invokeStatic(core.clj:168)
	at clj_backend.core$_main.doInvoke(core.clj:157)
	at clojure.lang.RestFn.applyTo(RestFn.java:140)
	at clj_backend.core.main(Unknown Source)
