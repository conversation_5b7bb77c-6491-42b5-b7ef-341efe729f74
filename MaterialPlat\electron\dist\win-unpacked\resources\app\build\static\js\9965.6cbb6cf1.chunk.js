"use strict";(self.webpackChunkzhongji_experiment_react=self.webpackChunkzhongji_experiment_react||[]).push([[9965],{13830:(e,l,a)=>{a.d(l,{A:()=>x,p:()=>p.ps});var n=a(65043),i=a(16569),t=a(6051),d=a(95206),o=a(81143),r=a(80077),s=a(74117),c=a(88359),u=a(51554),v=a(78178),p=a(56543),h=a(754),m=a(70579);const b=o.Ay.div`
    .bind-input-variable{
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 5px;

        .label{
            margin-right: 10px;
        }
        .bind-value-span{
            word-break: break-all;
        }
        .bind-fun-div{
            white-space: nowrap;
        }
    }
`,x=e=>{let{id:l,value:a,onChange:o,inputVariableType:x,checkFn:y,isSetProgrammableParameters:f=!1}=e;const j=(0,r.wA)(),{t:g}=(0,s.Bd)(),A=(0,n.useRef)(),[w,C]=(0,n.useState)(!1),[_,S]=(0,n.useState)(),[k,V]=(0,n.useState)("add");(0,n.useEffect)((()=>{a&&I(a)}),[a]);const I=e=>{if((null===e||void 0===e?void 0:e.variable_type)!==x)return void o();(0,h.B)("inputVariable","inputVariableMap").has(e.code)||o()},T=e=>{const l=y&&y(e);if(l)return void i.Ay.error(l);const{id:a,code:n,variable_name:t,variable_type:d,name:r}=e;o({id:a,code:n,variable_name:null!==t&&void 0!==t?t:r,variable_type:d,restrict:{variableType:p.oY.\u8f93\u5165\u53d8\u91cf,inputVarType:x}})};return(0,m.jsxs)(m.Fragment,{children:[(0,m.jsx)(b,{children:(0,m.jsxs)("div",{className:"bind-input-variable",children:[(0,m.jsxs)("div",{className:"bind-value-span",children:[g("\u7ed1\u5b9a\u53d8\u91cf"),":",null===a||void 0===a?void 0:a.variable_name]}),(0,m.jsx)("div",{className:"bind-fun-div",children:(0,m.jsxs)(t.A,{children:[(0,m.jsx)(d.Ay,{onClick:()=>{A.current.open({variableType:p.oY.\u8f93\u5165\u53d8\u91cf,inputVarType:x})},children:"\u9009\u62e9"}),a?(0,m.jsxs)(m.Fragment,{children:[(0,m.jsx)(d.Ay,{onClick:()=>{S(null===a||void 0===a?void 0:a.id),V("edit"),C(!0)},children:g("\u7f16\u8f91")}),(0,m.jsx)(d.Ay,{onClick:()=>o(),children:g("\u89e3\u7ed1")})]}):(0,m.jsx)(d.Ay,{onClick:()=>{V("add"),C(!0)},children:g("\u65b0\u5efa")})]})})]})}),(0,m.jsx)(u.A,{ref:A,isSetProgrammableParameters:f,handleSelectedVariable:T}),w&&(0,m.jsx)(v.A,{isSetProgrammableParameters:f,variableType:x,modalIndex:0,editId:_,mode:k,open:w,onOk:async e=>{const l=await j((0,c.w)()),a=null===l||void 0===l?void 0:l.find((l=>l.code===e.code));a&&T(a),C(!1)},onCancel:()=>{C(!1)}})]})}},51554:(e,l,a)=>{a.d(l,{A:()=>y});var n=a(65043),i=a(80077),t=a(16569),d=a(83720),o=a(79806),r=a(74117),s=a(93950),c=a.n(s),u=a(56543),v=a(75440),p=a(29977),h=a(6051),m=a(70579);const b=e=>{let{handleSelected:l,t:a}=e;return[{title:a?a("\u540d\u79f0"):"\u540d\u79f0",dataIndex:"variable_name",key:"variable_name"},{title:a?a("\u6807\u8bc6\u7b26"):"\u6807\u8bc6\u7b26",dataIndex:"code",key:"code"},{title:a?a("\u64cd\u4f5c"):"\u64cd\u4f5c",dataIndex:"code",key:"code",render:(e,a)=>(0,m.jsx)(h.A,{size:"middle",children:(0,m.jsx)("a",{onClick:()=>l(a),children:"\u9009\u62e9"})})}]},x=(e,l)=>{let{handleSelectedVariable:a=e=>console.log(e),isSetProgrammableParameters:s=!1}=e;const h=(0,p.A)(),x=(0,i.d4)((e=>e.template.resultData)),[y,f]=(0,n.useState)(!1),[j,g]=(0,n.useState)(),[A,w]=(0,n.useState)([]),[C,_]=(0,n.useState)([]),{t:S}=(0,r.Bd)(),k=(0,n.useMemo)((()=>h.map((e=>({...e,variable_name:null===e||void 0===e?void 0:e.name})))),[h]),V=(0,n.useMemo)((()=>x.map((e=>({...e,id:e.code})))),[x]);(0,n.useEffect)((()=>{y&&I()}),[y]);const I=()=>{if(j)switch(null===j||void 0===j?void 0:j.variableType){case u.oY.\u8f93\u5165\u53d8\u91cf:{const e=[...k.filter((e=>!(null!==j&&void 0!==j&&j.inputVarType)||e.variable_type===(null===j||void 0===j?void 0:j.inputVarType)))];_(e),w(e);break}case u.oY.\u4fe1\u53f7\u53d8\u91cf:case u.oY.\u7ed3\u679c\u53d8\u91cf:_(V),w(V);break;default:console.log("\u672a\u5904\u7406\u7684\u53d8\u91cf\u7c7b\u578b",null===j||void 0===j?void 0:j.variableType)}};(0,n.useImperativeHandle)(l,(()=>({open:e=>{g(e),f(!0)}})));const T=c()((async e=>{if(e){const l=A.filter((l=>{const a=l.variable_name.toLowerCase(),n=l.code.toLowerCase(),i=e.toLowerCase();return a.includes(i)||n.includes(i)}));_(l)}else _(A)}),200);return(0,m.jsxs)(v.A,{open:y,onCancel:()=>{f(!1)},title:"\u53d8\u91cf\u9009\u62e9",footer:null,children:[(0,m.jsx)(d.A,{allowClear:!0,onChange:e=>T(e.target.value),placeholder:S("\u540d\u79f0/\u5185\u90e8\u540d"),style:{width:"300px",marginBottom:"10px"}}),(0,m.jsx)(o.A,{rowKey:"code",columns:b({handleSelected:e=>{var l;!s||"Array"===(null===e||void 0===e?void 0:e.variable_type)&&"programmableParameters"===(null===e||void 0===e||null===(l=e.custom_array_tab)||void 0===l?void 0:l.useType)?(a(e,j),f(!1)):t.Ay.error("\u8bf7\u9009\u62e9\u81ea\u5b9a\u4e49\u6570\u7ec4\u7528\u9014\u4e3a\u7a0b\u63a7\u53c2\u6570\u7684\u53d8\u91cf")}}),dataSource:C})]})},y=(0,n.forwardRef)(x)},58243:(e,l,a)=>{a.d(l,{A:()=>p});var n=a(65043),i=a(6051),t=a(95206),d=a(81143),o=a(51554),r=a(65175),s=a(16133),c=a(56543),u=a(70579);const v=d.Ay.div`
    .bind-result-variable{
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 5px;
        .label{
            margin-right: 10px;
        }
    }
`,p=e=>{let{id:l,value:a,onChange:d}=e;const{initResultData:p}=(0,s.A)(),h=(0,n.useRef)(),[m,b]=(0,n.useState)(!1),[x,y]=(0,n.useState)(),f=e=>{const{result_variable_id:l,code:a,variable_name:n,variable_type:i}=e;d({id:l,code:a,variable_name:n,variable_type:i,restrict:{variableType:c.oY.\u7ed3\u679c\u53d8\u91cf}})};return(0,u.jsxs)(u.Fragment,{children:[(0,u.jsx)(v,{children:(0,u.jsxs)("div",{className:"bind-result-variable",children:[(0,u.jsxs)("span",{className:"label",children:["\u7ed1\u5b9a\u7ed3\u679c\u53d8\u91cf:",null===a||void 0===a?void 0:a.variable_name]}),(0,u.jsx)("div",{children:(0,u.jsxs)(i.A,{children:[(0,u.jsx)(t.Ay,{onClick:()=>{h.current.open({variableType:c.oY.\u7ed3\u679c\u53d8\u91cf})},children:"\u9009\u62e9"}),a?(0,u.jsx)(t.Ay,{onClick:()=>{y(null===a||void 0===a?void 0:a.id),b(!0)},children:"\u7f16\u8f91"}):(0,u.jsx)(t.Ay,{onClick:()=>{b(!0)},children:"\u65b0\u5efa"})]})})]})}),(0,u.jsx)(o.A,{ref:h,handleSelectedVariable:f}),(0,u.jsx)(r.A,{open:m,setOpen:b,editId:x,handleOk:e=>{p(),f(e)}})]})}},68358:(e,l,a)=>{a.d(l,{A:()=>p});var n=a(65043),i=a(48677),t=a(80077),d=a(14463),o=a(25055),r=a(36282),s=a(96603),c=a(14524),u=a(70579);const v=e=>{let{setting:l,onChange:a}=e;const[i]=o.A.useForm();(0,n.useEffect)((()=>{i.setFieldsValue({...l})}),[l]);return(0,u.jsx)(r.A,{content:(0,u.jsxs)(o.A,{form:i,name:"basic",labelCol:{style:{width:35}},onValuesChange:(e,l)=>{a(l)},children:[(0,u.jsx)(o.A.Item,{label:"\u4f4d\u7f6e",name:"placement",children:(0,u.jsxs)(s.Ay.Group,{size:"small",children:[(0,u.jsx)(s.Ay.Button,{value:"top",children:"\u4e0a"}),(0,u.jsx)(s.Ay.Button,{value:"right",children:"\u53f3"}),(0,u.jsx)(s.Ay.Button,{value:"bottom",children:"\u4e0b"}),(0,u.jsx)(s.Ay.Button,{value:"left",children:"\u5de6"})]})}),(0,u.jsx)(o.A.Item,{label:"\u5c3a\u5bf8",name:"size",children:(0,u.jsxs)(s.Ay.Group,{size:"small",children:[(0,u.jsx)(s.Ay.Button,{value:"default",children:"\u6b63\u5e38"}),(0,u.jsx)(s.Ay.Button,{value:"large",children:"\u5927"})]})})]}),title:"",trigger:"click",placement:"leftTop",children:(0,u.jsx)(c.A,{})})},p=e=>{let{children:l,open:a,onClose:n}=e;const o=(0,t.wA)(),{drawSetting:r}=(0,t.d4)((e=>e.split));return(0,u.jsx)(u.Fragment,{children:a&&(0,u.jsx)(i.A,{open:a,size:null===r||void 0===r?void 0:r.size,placement:null===r||void 0===r?void 0:r.placement,onClose:n,extra:(0,u.jsx)(v,{setting:r,onChange:e=>{o({type:d.cd,param:e})}}),children:l})})}},79965:(e,l,a)=>{a.r(l),a.d(l,{Container:()=>T,default:()=>F});var n=a(65043),i=a(81143),t=a(19853),d=a.n(t),o=a(80231),r=a(97320),s=a(69581),c=a(71424),u=a(80077),v=a(36950);const p=()=>{const e=(0,u.d4)((e=>e.template.resultData)),l=(0,u.d4)((e=>e.global.unitList));return(0,n.useCallback)((a=>{let{code:n,value:i,showUnit:t}=a;try{var d;const{format_type:a,dimension_id:c,unit_id:u,format_info:p}=null!==(d=e.find((e=>e.code===n)))&&void 0!==d?d:{};let h=i;if("number"===typeof h&&(h=(0,v.jq)(a,(0,v.tJ)(h,c,u),(0,v._q)(a,p))),t&&c&&u){var o,r,s;h=`${h} ${null===l||void 0===l||null===(o=l.find((e=>(null===e||void 0===e?void 0:e.id)===c)))||void 0===o||null===(r=o.units)||void 0===r||null===(s=r.find((e=>(null===e||void 0===e?void 0:e.id)===u)))||void 0===s?void 0:s.name}`}return h}catch(c){return console.log("err",c),i}}),[e,l])};var h=a(70579);const m=e=>{let{item:l={},labelWidth:a,label:i,isShowColon:t,spaceSetween:d}=e;const o=p(),[r,s]=(0,n.useState)("--"),c=null===l||void 0===l?void 0:l.resultCode,u=null===l||void 0===l?void 0:l.resultValue;(0,n.useEffect)((()=>{c&&u&&s(o({code:c,value:u,showUnit:!0}))}),[c,u,o]);return(0,h.jsxs)("div",{className:"singleResult",style:{justifyContent:d?"space-between":"normal"},children:[(0,h.jsx)("div",{className:"label",style:{width:a||""},children:(()=>{var e;return`\n            ${i||""}\n            ${null!==(e=null===l||void 0===l?void 0:l.resultParam)&&void 0!==e?e:""}\n            ${t?"\uff1a":""}\n        `})()}),(0,h.jsx)("div",{className:"value_box",children:r})]})},b=i.Ay.div`
    flex-direction: column;
    gap: 20px;

    .singleResult{
        display: flex;
    }  

    display: flex;

    width: 100%;
    height: 100%;
    background: #ffffff;
    overflow: hidden;

    .label{
        display: flex;
        align-items: center;
        overflow: hidden;
        white-space:nowrap;
    }    
`,x=e=>{let{config:{attr:{compWidth:l,labelWidth:a,label:n,spaceSetween:i,isShowColon:t,gap:d}={},variable:{value:o,visible:r}={}}={}}=e;const u=(0,s.A)(null===o||void 0===o?void 0:o.code),v=(0,c.A)(null===r||void 0===r?void 0:r.code,!0);return(0,h.jsx)(h.Fragment,{children:v&&(0,h.jsx)(b,{style:{gap:d},children:(()=>{var e,l,d,o;return u?(null===u||void 0===u||null===(e=u.default_val)||void 0===e||null===(l=e.value)||void 0===l?void 0:l.length)<=0?"\u53d8\u91cf\u672a\u8bbe\u7f6e\u8ba1\u7b97\u503c":null===u||void 0===u||null===(d=u.default_val)||void 0===d||null===(o=d.value)||void 0===o?void 0:o.map(((e,l)=>(0,h.jsx)(m,{item:e,labelWidth:a,label:n,isShowColon:t,spaceSetween:i},l))):"\u672a\u7ed1\u5b9a\u53d8\u91cf"})()})})};var y=a(25055),f=a(8918),j=a(83720),g=a(12624),A=a(74117),w=a(68358),C=a(63189),_=a(13830);a(58243);const{useForm:S,Item:k}=y.A,V=e=>{let{open:l,onClose:a,config:i,setConfig:t}=e;const{t:o}=(0,A.Bd)(),[r]=S();(0,n.useEffect)((()=>{d()(i,r.getFieldsValue())||r.setFieldsValue(i)}),[i]);return(0,h.jsx)(w.A,{open:l,onClose:a,children:(0,h.jsx)(y.A,{form:r,labelCol:{span:6},wrapperCol:{span:18},onValuesChange:(e,l)=>{var a;let n=l;null!==e&&void 0!==e&&null!==(a=e.variable)&&void 0!==a&&a.value&&(n={...n,attr:{...n.attr,label:e.variable.value.variable_name}}),t(n)},children:(0,h.jsx)(f.A,{defaultActiveKey:"attr",items:[{key:"attr",label:o("\u5c5e\u6027"),forceRender:!0,children:(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)(k,{label:o("\u7ec4\u4ef6\u5bbd\u5ea6"),name:["attr","compWidth"],children:(0,h.jsx)(C.A,{addonAfter:{defaultValue:"px",options:[{label:"px",value:"px"},{label:"%",value:"%"}]}})}),(0,h.jsx)(k,{label:o("\u6807\u7b7e\u6587\u672c"),name:["attr","label"],children:(0,h.jsx)(j.A,{})}),(0,h.jsx)(k,{label:o("\u6807\u7b7e\u5bbd\u5ea6"),name:["attr","labelWidth"],children:(0,h.jsx)(C.A,{addonAfter:{defaultValue:"px",options:[{label:"px",value:"px"},{label:"%",value:"%"}]}})}),(0,h.jsx)(k,{label:o("\u95f4\u8ddd"),name:["attr","gap"],children:(0,h.jsx)(C.A,{addonAfter:"px"})}),(0,h.jsx)(k,{label:o("\u663e\u793a\u5192\u53f7"),name:["attr","isShowColon"],valuePropName:"checked",children:(0,h.jsx)(g.A,{})}),(0,h.jsx)(k,{label:o("\u4e24\u7aef\u663e\u793a"),name:["attr","spaceSetween"],valuePropName:"checked",children:(0,h.jsx)(g.A,{})})]})},{key:"variable",label:o("\u53d8\u91cf"),forceRender:!0,children:(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)(k,{label:o("\u503c"),name:["variable","value"],children:(0,h.jsx)(_.A,{inputVariableType:_.p[o("\u81ea\u5b9a\u4e49\u6570\u7ec4")]})}),(0,h.jsx)(k,{label:o("\u53ef\u89c1\u6027"),name:["variable","visible"],children:(0,h.jsx)(_.A,{inputVariableType:_.p[o("\u5e03\u5c14\u578b")]})})]})}]})})})},I={attr:{compWidth:"100%",label:"",labelWidth:"30%",gap:"8px",isShowColon:!1,spaceSetween:!0},variable:{value:null,visible:null}},T=i.Ay.div`
    width: ${e=>{let{compWidth:l}=e;return null!==l&&void 0!==l?l:"100%"}};
    
    overflow: hidden;
`,F=e=>{var l;let{item:a,id:i,layoutConfig:t}=e;const{updateLayoutItem:s}=(0,r.A)(),[c,u]=(0,n.useState)(!1),[v,p]=(0,n.useState)(I);(0,n.useEffect)((()=>{try{if(null!==a&&void 0!==a&&a.data_source){const{comp_config:e}=JSON.parse(null===a||void 0===a?void 0:a.data_source);d()(e,v)||p(e)}}catch(e){console.log("err",e)}}),[null===a||void 0===a?void 0:a.data_source]);return(0,h.jsxs)(T,{id:i,compWidth:null===v||void 0===v||null===(l=v.attr)||void 0===l?void 0:l.compWidth,children:[(0,h.jsx)(x,{config:v}),(0,h.jsx)(V,{open:c,onClose:()=>{u(!1),s({layout:t,newItem:{...a,data_source:JSON.stringify({comp_config:v})}})},config:v,setConfig:p}),(0,h.jsx)(o.A,{domId:i,layoutConfig:t,children:(0,h.jsx)("div",{className:"unique-content",onClick:()=>u(!0),children:"\u7f16\u8f91\u7ed3\u679c\u53d8\u91cf\u6570\u7ec4\u5c55\u793a"})})]})}},97320:(e,l,a)=>{a.d(l,{A:()=>r});a(65043);var n=a(80077),i=a(84856),t=a(67208),d=a(14463),o=a(41086);const r=()=>{const e=(0,n.wA)(),{saveLayout:l}=(0,i.A)(),a=async l=>{let{layout:a,newItem:n}=l;const i={...a,children:r(a.children,n)},[s]=await(0,t.PXE)({binder_ids:[null===a||void 0===a?void 0:a.binder_id]});await(0,t.Kv3)({binders:[{...s,layout:(0,o.gT)(i,null===a||void 0===a?void 0:a.binder_id)}]}),e({type:d.EH,param:s.binder_id})},r=(e,l)=>e.map((e=>e.id===l.id?l:e.children&&e.children.length>0?{...e,children:r(e.children,l)}:e)),s=async e=>{let{layout:a,newItem:n}=e;const i={...a,children:r(a.children,n)};await l(i)};return{updateLayoutItem:async e=>{let{layout:l,newItem:n}=e;null!==l&&void 0!==l&&l.binder_id?(console.log("\u754c\u9762\u5185\u5bb9-tab"),await a({layout:l,newItem:n})):(console.log("\u754c\u9762\u5185\u5bb9-\u4e3b\u7a97\u53e3"),await s({layout:l,newItem:n}))}}}}}]);
//# sourceMappingURL=9965.6cbb6cf1.chunk.js.map