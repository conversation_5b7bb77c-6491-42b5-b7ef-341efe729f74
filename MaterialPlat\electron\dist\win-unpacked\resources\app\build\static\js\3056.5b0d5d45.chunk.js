"use strict";(self.webpackChunkzhongji_experiment_react=self.webpackChunkzhongji_experiment_react||[]).push([[3056],{3056:(e,t,n)=>{n.r(t),n.d(t,{Container:()=>x,default:()=>w});var i=n(65043),a=n(81143),l=n(19853),o=n.n(l),r=n(80231),s=n(97320),c=n(56543),d=n(71424),u=n(16569);var p=n(22),b=n(67208),m=n(63379),v=n(69581),y=n(28116),_=n(70579);const h=e=>{let{config:{attr:{compWidth:t,labelWidth:n,label:a,labelItalic:l,labelBold:o,contentItalic:r,contentBold:s}={},variable:{value:h,visible:f,disabled:g}={},event:{change:x}={}}={},inputVariableType:w}=e;const{onEvent:C}=(0,m.s)(),T=(0,v.A)(null===h||void 0===h?void 0:h.code,function(){let{variable_type:e}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return{name:"",code:"",group_category:"",description:"",id:"",variable_type:null!==e&&void 0!==e?e:"Number",default_val:{value:0,isConstant:0,value_type:"string",unitType:null,unit:null},createdBy:"",f1_index:"",pic:"",is_enable:0,is_feature:0,is_overall:0,is_fx:0,number_tab:{format:{numberRequire:"any",formatType:"auto",afterPoint:null,beforePoint:null,significantDigits:1,amendmentInterval:0,pointPosition:0,roundMode:0,threshold1:1,threshold2:1,roundType1:1,roundType2:1,roundType3:1},channel:{channelType:"\u65e0",channel:"\u65e0",isUserConversion:!1,lockChannels:[]},multipleMeasurements:{measurementCounts:1,measurementType:"min"},unit:{unitType:"\u65e0",unit:"\u65e0",isUserConversion:!1,lockChannels:[]}},reasonable_val_tab:{reasonableType:"empty",values:[0,0],defaultVal:10,minParam:10,maxParam:10,isToResultList:!1},button_variable_tab:{isEnable:!1,content:"",position:"left",actionId:"",function:"f(x)",pic:"",buttonType:"action",script:""},button_tab:{isEnable:!1,content:"",position:"left",actionId:"",function:"",source:"action_lib",pic:"",type:"action",method:"post"},program_tab:{numericFormat:"",unit:"",isVisible:"",isDisabled:"",mode:"",isCheck:""},text_tab:{content:"",format:"single",return_type:c.Jt.BOOL,canUseText:!1},select_tab:{selection:"list_single",group_id:"",selectLayer:c.Q1.\u8f74,items:[],format:"",comment:""},two_digit_array_tab:{rowCounts:1,columnCount:1,rowHeaderPlace:!0,columnHeaderPlace:!0,isRowType:!1,rowDefinition:[{title:"\u884c1",type:"text",options:[]}],columnDefinition:[{title:"\u52171",type:"text",options:[]}],columnData:[[""]]},custom_array_tab:{useType:"followComp"},control_tab:{type:"not_custom",dialog_type:"variable",control_name:"",code:"",default_name:"",related_variables:[],title:"",variables:[],signals:[],is_daq:!1},buffer_tab:{buffer_type:"ArrayQueue",size:1,size_expand:"expand",signals:[]},label_tab:{format:"",content:"",fontSize:12,fore:""},picture_tab:{src:"",showName:!1,path:"",name:""},related_var_tab:{vars:[]},double_array_tab:{rowNumber:0,columns:[]},double_array_list_tab:{dataSourceCode:"",number:0}}}({variable_type:w})),j=(0,i.useMemo)((()=>({...T,name:null!==a&&void 0!==a?a:T.name})),[T,a]),A=(0,d.A)(null===f||void 0===f?void 0:f.code,!0),k=(0,d.A)(null===g||void 0===g?void 0:g.code,!0);return(0,_.jsx)(_.Fragment,{children:A&&(0,_.jsx)(p.A,{variable:j,disabled:!k,onChange:async e=>{if(null===h||void 0===h||!h.code)return void u.Ay.error("\u672a\u7ed1\u5b9a\u53d8\u91cf");const t={...e,name:T.name};await(0,b.Tnc)(t)&&((0,y.P)({code:t.code},t),C(x))},labelItalic:l,labelBold:o,contentItalic:r,contentBold:s})})},f=e=>{let{label:t=""}=e;return{attr:{compWidth:"100%",compHeight:"40px",label:t,labelWidth:"30%",size:"middle",isShowColon:!1,readonly:!1},variable:{value:null,visible:null,disabled:null},event:{change:null}}},g=(0,i.lazy)((()=>Promise.all([n.e(5960),n.e(1750)]).then(n.bind(n,1750)))),x=a.Ay.div`
    width: ${e=>{let{compWidth:t}=e;return null!==t&&void 0!==t?t:"100%"}};
    height: ${e=>{let{compHeight:t}=e;return null!==t&&void 0!==t?t:"40px"}};
    background: #fff;
    
    overflow: hidden;
`,w=e=>{var t,n,a,l;let{item:u,id:p,layoutConfig:b,inputVariableType:m=c.ps.\u6587\u672c,atomTypeName:v="\u6587\u672c\u8f93\u5165\u6846"}=e;const{updateLayoutItem:y}=(0,s.A)(),[w,C]=(0,i.useState)(!1),[T,j]=(0,i.useState)();(0,i.useEffect)((()=>{j(f({label:v}))}),[v]),(0,i.useEffect)((()=>{try{if(null!==u&&void 0!==u&&u.data_source){const{comp_config:e}=JSON.parse(null===u||void 0===u?void 0:u.data_source);o()(e,T)||j(e)}else j(f({label:v}))}catch(e){console.log("err",e)}}),[null===u||void 0===u?void 0:u.data_source]);const A=(0,d.A)(null===T||void 0===T||null===(t=T.variable)||void 0===t||null===(n=t.visible)||void 0===n?void 0:n.code,!0);return(0,_.jsxs)(x,{id:p,compWidth:null===T||void 0===T||null===(a=T.attr)||void 0===a?void 0:a.compWidth,compHeight:null===T||void 0===T||null===(l=T.attr)||void 0===l?void 0:l.compHeight,style:{display:A?"block":"none"},children:[T&&(0,_.jsx)(h,{config:T,inputVariableType:m}),(0,_.jsx)(i.Suspense,{fallback:(0,_.jsx)(_.Fragment,{}),children:w&&(0,_.jsx)(g,{open:w,onClose:()=>{C(!1),y({layout:b,newItem:{...u,data_source:JSON.stringify({comp_config:T})}})},config:T,setConfig:j,inputVariableType:m})}),(0,_.jsx)(r.A,{domId:p,layoutConfig:b,children:(0,_.jsx)("div",{className:"unique-content",onClick:()=>C(!0),children:`\u7f16\u8f91${v}`})})]})}},63379:(e,t,n)=>{n.d(t,{A:()=>p,s:()=>u});var i=n(65043),a=n(6051),l=n(95206),o=n(74117),r=n(16090),s=n(67208),c=n(70579);const d=(0,i.lazy)((()=>n.e(7206).then(n.bind(n,97206)))),u=()=>{const{startAction:e}=(0,r.A)(),t=async t=>{t&&await e({action_id:String(t)})},n=async e=>{e&&await(0,s.O5k)({script:e,result_type:"BOOL"})};return{onEvent:e=>{try{if(e){const{action_id:i,execute_type:a,script:l}=e;"action"===a&&t(i),"script"===a&&n(l)}}catch(i){console.log("error",i)}}}},p=e=>{let{id:t,value:n,onChange:r}=e;const{t:s}=(0,o.Bd)(),[u,p]=(0,i.useState)(!1),b=()=>{p(!0)};return(0,c.jsxs)("div",{children:[n?(0,c.jsx)(c.Fragment,{children:(0,c.jsxs)(a.A,{children:[(0,c.jsx)(l.Ay,{onClick:()=>b(),children:s("\u7f16\u8f91")}),(0,c.jsx)(l.Ay,{onClick:()=>r(),children:s("\u5220\u9664")})]})}):(0,c.jsx)(l.Ay,{onClick:()=>b(),children:s("\u70b9\u51fb\u6dfb\u52a0\u4e8b\u4ef6")}),(0,c.jsx)(i.Suspense,{fallback:(0,c.jsx)(c.Fragment,{}),children:u&&(0,c.jsx)(d,{open:u,setOpen:p,event:n,callback:r})})]})}},97320:(e,t,n)=>{n.d(t,{A:()=>s});n(65043);var i=n(80077),a=n(84856),l=n(67208),o=n(14463),r=n(41086);const s=()=>{const e=(0,i.wA)(),{saveLayout:t}=(0,a.A)(),n=async t=>{let{layout:n,newItem:i}=t;const a={...n,children:s(n.children,i)},[c]=await(0,l.PXE)({binder_ids:[null===n||void 0===n?void 0:n.binder_id]});await(0,l.Kv3)({binders:[{...c,layout:(0,r.gT)(a,null===n||void 0===n?void 0:n.binder_id)}]}),e({type:o.EH,param:c.binder_id})},s=(e,t)=>e.map((e=>e.id===t.id?t:e.children&&e.children.length>0?{...e,children:s(e.children,t)}:e)),c=async e=>{let{layout:n,newItem:i}=e;const a={...n,children:s(n.children,i)};await t(a)};return{updateLayoutItem:async e=>{let{layout:t,newItem:i}=e;null!==t&&void 0!==t&&t.binder_id?(console.log("\u754c\u9762\u5185\u5bb9-tab"),await n({layout:t,newItem:i})):(console.log("\u754c\u9762\u5185\u5bb9-\u4e3b\u7a97\u53e3"),await c({layout:t,newItem:i}))}}}}}]);
//# sourceMappingURL=3056.5b0d5d45.chunk.js.map