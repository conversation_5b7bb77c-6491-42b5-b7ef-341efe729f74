using System.Reactive.Linq;
using System.Text.Json;
using Buffers;
using Consts;
using Logging;
using Microsoft.Data.Sqlite;
using MQ;
using ScriptEngine.InputVar;
using ScriptEngine.InputVar.InputVars;
using static Logging.CCSSLogger;
using static Scripting.ITemplate;
using SignalExample;
using SubTaskUtils;
using Scripting;
using ScriptEngine.InstantiatedTemplate.SignalVar;
using ScriptEngine.InstantiatedTemplate.Hardware.MappingHardware;

namespace SubTasks.tasks.gatherDataTask
{
    /**
    峰谷值采集

 public event Hw.OnDataBlockHdlr OnDataBlockEvent;
采集回调数据块

 Run（）
｛
    
    switch （State）
    ｛
        case 0：
                            
                把保存数据数组缓冲区设定为线性缓冲区，缓冲区设定为20000点，并建立多段数据结构
                if（是继续试验）
                ｛
                    
                    保存数据数组缓冲区写指针不变
                ｝
                elseif（重新开始）
                ｛
                    所有的保存数据数组缓冲区写指针=0
                ｝
                采集标志=1；
                State=1；
            
            break；
        case 1：
            
            if（采集标志==1）
            ｛
                按照多个滤波原则，采集多组数据放进一个峰谷值数据缓冲区中，并记录周期数
                if（放到缓冲区）
                ｛
                    数据放进缓冲区
                ｝
                if（放到文件）
                ｛
                    数据放进文件
                ｝
            ｝
            if（缓冲区不够）
            ｛
                扩大线性缓冲区长度，数据不能丢失
            ｝
            if（暂停试验）
            ｛
                if（停止采集）
                  采集标志=0
                elseif（不停止采集）
                  采集标志=1

            ｝
            if（恢复试验）
            ｛
                采集标志=1

            ｝
            if(停止试验)
            ｛
                保存缓冲区写指针位置
                关闭子程序
                
            ｝

            break；

    ｝
}


    1. 每个周期的峰值谷值的是一样的，所以在每个周期中取一个就行
    2. 峰值谷值放在MaxSensor[]  MinSensor[]
    3. 周期变换，峰值谷值才会变化


    */
    public class SubTaskPeakGatherData : ISubTask
    {
        public MQSubTaskSub[] subs { set; get; }
        public bool Sub_TASK_MGR_CMD { get; set; } = true;
        public bool Sub_TASK_MGR_CMD_Other { get; set; } = true;
        public bool Sub_TASK_HARDWARE_CMD { get; set; } = true;
        public bool Sub_TASK_HARDWARE_DATA { get; set; } = true;
        public bool Sub_TOPIC_FROM_UI { get; set; } = true;
        public bool Sub_TOPIC_FROM_SCRIPT_CLIENT { get; set; } = true;
        public bool Sub_SelfTopic { get; set; } = false;
        public bool Sub_TOPIC_NOTIFY { get; set; } = true;
        private const string ClassName = "SubTaskPeakGatherData";
        ////轴ID
        int _deviceid;

        private string? _processID;
        private string? _subtaskID;
        private string? _templateName;
        private string? _sampleInstanceCode;

        private ulong _finalCollectedCycles;
        private ulong _firstCollectedCycles;
        private bool _logarithmicCollect = false;

        private bool _isIntervalCollected;
        private ulong _intervalCollectedCycles;

        private bool _isSpecifyCycle;
        private ulong[]? _specifyCycle;

        private bool _isFinalCollected;
        private bool _isFirstCollected;

        private bool _saveDB = true;
        // 周期 信号变量的code,用于周期过滤
        private string _cyclesSignalCode = "signal_cycle";
        private ITemplate? _templateInst;
        private string? _bindBuffercode;
        //存入二维数组
        bool _isDoubleArray;
        string _doubleArrayCode;
        private class UICmdParam
        {
            public List<Variable_Circle>? VarValues { get; set; }
        }

        public bool Abort(SubTaskCmdParams Params)
        {
            ISystemBus.SendToUIStatusTopic(GenrateStatusUIJson(CmdConsts.RCV_ABORT_TASK_CMD));

            CCSSLogger.Logger.Info("峰谷值数据采集 终止:" + Params);
            ISystemBus.SendToTaskUpTopic(
                CmdConsts.SubTaskFinishCmd(Params.ClassName!, Params.ProcessID!, Params.SubTaskID!)
            );
            _templateInst!.RemoveDaqHandler(_bindBuffercode! + _processID);
            ((ISubTask)this).CleanAllSubs();
            return true;
        }

        public bool Finish(SubTaskCmdParams Params)
        {
            ISystemBus.SendToUIStatusTopic(GenrateStatusUIJson(CmdConsts.RCV_ABORT_TASK_CMD));

            CCSSLogger.Logger.Info("峰谷值数据采集 Finish:" + Params);
            ISystemBus.SendToTaskUpTopic(
                CmdConsts.SubTaskFinishCmd(Params.ClassName!, Params.ProcessID!, Params.SubTaskID!)
            );
            ((ISubTask)this).CleanAllSubs();
            return true;
        }

        public string[] GetSelfTopic()
        {
            throw new NotImplementedException();
        }

        public void HandleMsgFromScript(string ParamatersString)
        {
            throw new NotImplementedException();
        }

        public void HandleMsgFromUI(string ParamatersString)
        {
            throw new NotImplementedException();
        }

        public void HandleMsgFromVAR(string topic, string ParamatersString)
        {
            throw new NotImplementedException();
        }

        public void ImportHwFuncRet(string ParamatersString)
        {
            throw new NotImplementedException();
        }

        public SubTaskCmdParams? ImportParams(string ParamatersString)
        {
            Logger.Info("ImportPrams" + " :" + ParamatersString);
            var x = JsonSerializer.Deserialize<SubTaskCmdParams>(ParamatersString);

            return x;
        }

        public bool Pause(SubTaskCmdParams Params)
        {
            ISystemBus.SendToUIStatusTopic(
                UtilsForSubTasks.GenerateStatusUIJson(
                    CmdConsts.RCV_PAUSE_TASK_CMD,
                    _processID!,
                    _subtaskID!
                )
            );

            return true;
        }

        public bool ProcessData(SubTaskCmdParams Params)
        {
            throw new NotImplementedException();
        }

        public bool Resume(SubTaskCmdParams Params)
        {
            ISystemBus.SendToUIStatusTopic(
                UtilsForSubTasks.GenerateStatusUIJson(
                    CmdConsts.RCV_RESUME_TASK_CMD,
                    _processID!,
                    _subtaskID!
                )
            );
            _saveDB = true;

            return true;
        }

        //组装发送给前端关于子任务状态的参数
        public string GenrateStatusUIJson(string status_str)
        {
            var uicmdParam = new UIStatusParam(status_str);
            // 构建 JSON 字符串
            var jsonStr = JsonSerializer.Serialize(uicmdParam);

            // 通过解析 JSON 字符串创建 JsonDocument 对象
            using JsonDocument document = JsonDocument.Parse(jsonStr);
            // 获取根节点的 JsonElement
            JsonElement root = document.RootElement;

            // 构建 UICmdParams 对象，并将 UIParams 设置为 JsonElement
            var uicmdParams = new UICmdParams(_processID!, _subtaskID!, "taskStatus", root);

            return JsonSerializer.Serialize(uicmdParams);
        }
        void SetCode()
        {
            //轴上信号变量
            var Signals = _templateInst.SignalVars.Where(x => x.Value.DeviceId == _deviceid && x.Value.SignalValueType != null).Select(x => x.Value).ToList();
            foreach (var signal in Signals)
            {
                switch (signal.RelatedInfo)
                {
                    //case "Cycles":
                    //    _cyclesSignalCode = signal.Code;
                    //    break;

                    default:
                        break;
                }
            }
        }
        BufferInputVar daqBuffer;
        SubTaskCmdParams _subTaskCmdParams;
        public void ReParams(SubTaskCmdParams t)
        {
            _isFirstCollected = UtilsForSubTasks.ReadInputVarProperty<bool>(
                           _templateName,
                           t.SubTaskParams.GetProperty("schedule")
                               .GetProperty("control_input_number_of_first_collection_cycles"),
                           "IsCheck"
                       );
            _firstCollectedCycles = UtilsForSubTasks.ReadVarValue<ulong>(
                _templateName,
                t.SubTaskParams.GetProperty("schedule")
                    .GetProperty("control_input_number_of_first_collection_cycles")
            );
            _isFinalCollected = UtilsForSubTasks.ReadInputVarProperty<bool>(
                _templateName,
                t.SubTaskParams.GetProperty("schedule")
                    .GetProperty("control_input_number_of_final_collection_cycles"),
                "IsCheck"
            );
            _finalCollectedCycles = UtilsForSubTasks.ReadVarValue<ulong>(
                _templateName,
                t.SubTaskParams.GetProperty("schedule")
                    .GetProperty("control_input_number_of_final_collection_cycles")
            );

            //指定周期
            _isSpecifyCycle = UtilsForSubTasks.ReadInputVarProperty<bool>(
                _templateName,
                t.SubTaskParams.GetProperty("schedule")
                    .GetProperty("control_input_characteristic_period_text"),
                "IsCheck"
            );
            string _thecycle = UtilsForSubTasks.ReadVarValue<string>(
                _templateName,
                t.SubTaskParams.GetProperty("schedule")
                    .GetProperty("control_input_characteristic_period_text")
            );
            _specifyCycle = string.IsNullOrEmpty(_thecycle)
                ? new ulong[0]
                : _thecycle.Split(',').Select(i => ulong.Parse(i)).ToArray();

            //固定间隔周期

            _isIntervalCollected = UtilsForSubTasks.ReadInputVarProperty<bool>(
                _templateName,
                t.SubTaskParams.GetProperty("schedule")
                    .GetProperty("control_input_interval_specified_period"),
                "IsCheck"
            );
            _intervalCollectedCycles = UtilsForSubTasks.ReadVarValue<ulong>(
                _templateName,
                t.SubTaskParams.GetProperty("schedule")
                    .GetProperty("control_input_interval_specified_period")
            );
            var daqHandlerParam = new DynamicDaqHandlerParameters(
               ClassName,
               _templateInst,
               daqBuffer,
               _subtaskID,
               _sampleInstanceCode,
               _saveDB,
               false,
               double.NaN,
               double.NaN,
               double.NaN,
               string.Empty,
               double.NaN,
               _finalCollectedCycles,
               _firstCollectedCycles,
               _isIntervalCollected,
               _intervalCollectedCycles,
               _isSpecifyCycle,
               _specifyCycle,
               string.Empty,
               0,
               string.Empty,
              _cyclesSignalCode,
               _logarithmicCollect,
               _isFinalCollected,
               _isFirstCollected,
               _isDoubleArray,
               _doubleArrayCode,
               null
           );
            _templateInst.CreateOrUpdateDynamicDaqHandler(_bindBuffercode, _processID, daqHandlerParam);
        }
        public bool Run(SubTaskCmdParams t)
        {
            _subTaskCmdParams = t;
            //通过参数获取模板ID、子任务ID、当前试样信息、
            _processID = t!.ProcessID!;
            _subtaskID = t!.SubTaskID!;
            _templateName = t.ClassName!;
            _saveDB = (bool)
                UtilsForSubTasks.ReadVarValue<bool>(
                    _templateName,
                    t.SubTaskParams.GetProperty("schedule").GetProperty("control_input_savedb")
                );
            //获取脚本中的该模板的实例化的模板对象
            _templateInst = GetTemplateByName(t.ClassName!);
            if (_templateInst is null)
            {
                Finish(t);
                return false;
            }
            _sampleInstanceCode = _templateInst.CurrentInst.Code;
            var subtasksparams = t!.SubTaskParams.GetProperty("schedule");

            MappingAxis mappingAxis = _templateInst!.MappingHardware.GetMappingAxis(
              UtilsForSubTasks.ReadVarValue<object[]>(
            t.ClassName!,
             subtasksparams.GetProperty("control_input_device")
              )
          );
            if (mappingAxis is null)
            {
                return false;
            }
            _deviceid = mappingAxis.RealIndex;
            //轴上同步，暂时没有需要的
            // SetCode();
            _cyclesSignalCode = UtilsForSubTasks.ReadVarValue<string>(
              _templateName,
              t.SubTaskParams.GetProperty("schedule")
              .GetProperty("control_input_cyclesSignalCode")
             );

            //对数参数
            _logarithmicCollect = UtilsForSubTasks.ReadVarValue<bool>(
                _templateName,
                t.SubTaskParams.GetProperty("schedule").GetProperty("control_input_log")
            );
            //存入二维数组
            _isDoubleArray = UtilsForSubTasks.ReadInputVarProperty<bool>(
           _templateName,
           t.SubTaskParams.GetProperty("schedule")
               .GetProperty("control_input_Array"),
           "IsCheck"
              );
            _doubleArrayCode = UtilsForSubTasks.ReadVarValue<string>(
                _templateName,
                t.SubTaskParams.GetProperty("schedule")
                    .GetProperty("control_input_Array")
            );
            //通知前端该子任务启动
            ISystemBus.SendToUIStatusTopic(GenrateStatusUIJson(t.Cmd()!));
            Logger.Info(" 峰谷值采集  Run:" + t);

           
            _bindBuffercode = UtilsForSubTasks.ReadVarValue<string>(
                _templateInst,
                t.SubTaskParams.GetProperty("schedule").GetProperty("control_input_bufferCode")
            );
            daqBuffer = _templateInst.GetVarByName<BufferInputVar>(_bindBuffercode);
            // 是否清空buffer
            string bufferMode = UtilsForSubTasks.ReadVarValue<string>(_templateInst,
                t.SubTaskParams.GetProperty("schedule").GetProperty("control_input_bufferMode"));
            if (bufferMode == "RESTART")
            {
                if (_isDoubleArray == true)
                {
                    _templateInst.GetVarByName<DoubleArrayInputVar>(_doubleArrayCode).ClearDoubleArrayData();
                }
                // 清空缓冲区时, 重置数据表, 通知前端重新开始
                daqBuffer.Reset();
                _templateInst!.Db!.RestartBuffer(_sampleInstanceCode!, _bindBuffercode!);
                ISystemBus.SendToUICmdTopic(JsonSerializer.Serialize(new UICmdParams(
                    _processID,
                    _subtaskID,
                    "BufferReset",
                    JsonDocument.Parse(JsonSerializer.Serialize(new
                    {
                        SampleInstCode = _sampleInstanceCode,
                        BufferCode = _bindBuffercode,
                        BufferMode = bufferMode
                    })).RootElement)));
            }

            ReParams(t);
           
            return true;
        }

        public JsonElement UIParams()
        {
            throw new NotImplementedException();
        }



        public bool ReStart(SubTaskCmdParams Params)
        {
            throw new NotImplementedException();
        }

        public bool Error(SubTaskCmdParams Params)
        {
            throw new NotImplementedException();
        }

        public void HandleNotify(string notifyTitle, string msg)
        {

            if (notifyTitle == "UpdateParam" &&
                msg == _templateName! + "-" + _subtaskID!)
            {
                ReParams(_subTaskCmdParams);
                Logger.Error("重新加载参数，并执行任务");
            }
        }
    }
}
