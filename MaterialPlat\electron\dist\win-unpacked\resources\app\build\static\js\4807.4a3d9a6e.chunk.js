"use strict";(self.webpackChunkzhongji_experiment_react=self.webpackChunkzhongji_experiment_react||[]).push([[4807],{13830:(e,l,n)=>{n.d(l,{A:()=>f,p:()=>m.ps});var t=n(65043),i=n(16569),a=n(6051),d=n(95206),o=n(81143),r=n(80077),s=n(74117),u=n(88359),c=n(51554),v=n(78178),m=n(56543),h=n(754),p=n(70579);const y=o.Ay.div`
    .bind-input-variable{
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 5px;

        .label{
            margin-right: 10px;
        }
        .bind-value-span{
            word-break: break-all;
        }
        .bind-fun-div{
            white-space: nowrap;
        }
    }
`,f=e=>{let{id:l,value:n,onChange:o,inputVariableType:f,checkFn:x,isSetProgrammableParameters:b=!1}=e;const j=(0,r.wA)(),{t:g}=(0,s.Bd)(),A=(0,t.useRef)(),[C,w]=(0,t.useState)(!1),[k,_]=(0,t.useState)(),[I,T]=(0,t.useState)("add");(0,t.useEffect)((()=>{n&&S(n)}),[n]);const S=e=>{if((null===e||void 0===e?void 0:e.variable_type)!==f)return void o();(0,h.B)("inputVariable","inputVariableMap").has(e.code)||o()},F=e=>{const l=x&&x(e);if(l)return void i.Ay.error(l);const{id:n,code:t,variable_name:a,variable_type:d,name:r}=e;o({id:n,code:t,variable_name:null!==a&&void 0!==a?a:r,variable_type:d,restrict:{variableType:m.oY.\u8f93\u5165\u53d8\u91cf,inputVarType:f}})};return(0,p.jsxs)(p.Fragment,{children:[(0,p.jsx)(y,{children:(0,p.jsxs)("div",{className:"bind-input-variable",children:[(0,p.jsxs)("div",{className:"bind-value-span",children:[g("\u7ed1\u5b9a\u53d8\u91cf"),":",null===n||void 0===n?void 0:n.variable_name]}),(0,p.jsx)("div",{className:"bind-fun-div",children:(0,p.jsxs)(a.A,{children:[(0,p.jsx)(d.Ay,{onClick:()=>{A.current.open({variableType:m.oY.\u8f93\u5165\u53d8\u91cf,inputVarType:f})},children:"\u9009\u62e9"}),n?(0,p.jsxs)(p.Fragment,{children:[(0,p.jsx)(d.Ay,{onClick:()=>{_(null===n||void 0===n?void 0:n.id),T("edit"),w(!0)},children:g("\u7f16\u8f91")}),(0,p.jsx)(d.Ay,{onClick:()=>o(),children:g("\u89e3\u7ed1")})]}):(0,p.jsx)(d.Ay,{onClick:()=>{T("add"),w(!0)},children:g("\u65b0\u5efa")})]})})]})}),(0,p.jsx)(c.A,{ref:A,isSetProgrammableParameters:b,handleSelectedVariable:F}),C&&(0,p.jsx)(v.A,{isSetProgrammableParameters:b,variableType:f,modalIndex:0,editId:k,mode:I,open:C,onOk:async e=>{const l=await j((0,u.w)()),n=null===l||void 0===l?void 0:l.find((l=>l.code===e.code));n&&F(n),w(!1)},onCancel:()=>{w(!1)}})]})}},34807:(e,l,n)=>{n.r(l),n.d(l,{Container:()=>K,default:()=>W});var t=n(65043),i=n(81143),a=n(19853),d=n.n(a),o=n(80231),r=n(97320),s=n(67208),u=n(69581),c=n(28116);const v=i.Ay.div`
    display: flex;

    width: 100%;
    height: 100%;
    background: #ffffff;
    overflow: hidden;

    .label{
        display: flex;
        align-items: center;
        overflow: hidden;
        white-space:nowrap;
    }    
`,m=(0,i.Ay)(v)`
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    
    .ant-input-number-input{
        text-align: center;
    }

    .titleCell {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 10px;
        .ant-select{
            min-width: 50px;
        }
    }
`;var h=n(83720),p=n(97914),y=n(25055),f=n(74117),x=n(80077),b=n(36950),j=n(9339);const g={attr:{compWidth:"100%",label:"",labelWidth:"30%",isShowColon:!1,spaceSetween:!0},variable:{value:null,visible:null}},A="text",C="number";var w=n(36497),k=n(70579);const _=e=>{var l,n;let{title:i,dimensionId:a,unitId:d,handleUnitChange:o}=e;if(!a)return i;const r=(0,x.d4)((e=>e.global.unitList)),[s,u]=(0,t.useState)(!1),c=(0,t.useRef)(),v=(0,t.useMemo)((()=>{var e,l;return null!==(e=null===(l=r.find((e=>e.id===a)))||void 0===l?void 0:l.units)&&void 0!==e?e:[]}),[a,r]),m=null!==d&&void 0!==d?d:null===(l=r.find((e=>e.id===a)))||void 0===l?void 0:l.default_unit_id,h=null===(n=v.find((e=>e.id===m)))||void 0===n?void 0:n.name;(0,t.useEffect)((()=>{s&&c.current.focus()}),[s]);return(0,k.jsxs)("div",{className:"titleCell",children:[(0,k.jsx)("span",{children:i}),s?(0,k.jsx)(w.A,{value:m,ref:c,fieldNames:{label:"name",value:"id"},options:v,onChange:e=>{o(e)},onBlur:()=>{u(!1)}}):(0,k.jsx)("div",{onClick:()=>{u(!0)},children:`(${h})`})]})},I=e=>{switch(e){case A:return(0,k.jsx)(h.A,{bordered:!1,style:{textAlign:"center"}});case C:return(0,k.jsx)(p.A,{bordered:!1,style:{textAlign:"center"}});default:return(0,k.jsx)(k.Fragment,{})}},T=e=>{let{dataColumnsConfig:l,variable:n,handleUpdateVariable:i}=e;const{t:a}=(0,f.Bd)(),d=(0,x.d4)((e=>e.global.unitList)),[o,r]=(0,t.useState)([]),[s,u]=(0,t.useState)([]),[c]=y.A.useForm(),[v,m]=(0,t.useState)({}),h=async(e,l)=>{await A(l),m({...v,[e]:l})},{columns:p,config:g}=(0,t.useMemo)((()=>{const e=null===l||void 0===l?void 0:l.map((e=>{if(e.dataType===C&&e.dimensionId){var l;const n=d.find((l=>l.id===e.dimensionId)),t=e.id in v&&null!==n&&void 0!==n&&null!==(l=n.units)&&void 0!==l&&l.some((l=>l.id===v[e.id]))?v[e.id]:null===n||void 0===n?void 0:n.default_unit_id;return{...e,unitId:t}}return e})),n=(e=>{let{config:l,handleColumnUnitChange:n}=e;return l?l.map((e=>{let{id:l,title:t,dataType:i,key:a,dimensionId:d,unitId:o}=e;return{title:(0,k.jsx)(_,{title:t,dimensionId:d,unitId:o,handleUnitChange:e=>n(l,e)}),dataIndex:a,key:a,align:"center",render:(e,l)=>(0,k.jsx)(y.A.Item,{name:`${l.key}_${a}`,style:{margin:0},children:I(i)})}})):[]})({config:e,handleColumnUnitChange:h});return{config:e,columns:n}}),[l,v]);(0,t.useEffect)((()=>{var e;n&&Array.isArray(null===n||void 0===n||null===(e=n.default_val)||void 0===e?void 0:e.value)&&w()}),[n]);const A=async e=>{const l=await c.validateFields(),n=Object.fromEntries(Object.entries(l).map((l=>{let[n,t]=l;const[i,a]=n.split("_"),o=g.find((e=>e.key===a));var r;return o&&o.dataType===C&&null!==o&&void 0!==o&&o.dimensionId&&null!==o&&void 0!==o&&o.unitId?[n,(0,b.tJ)(t,o.dimensionId,e,(null===o||void 0===o?void 0:o.unitId)||(null===(r=d.find((e=>e.id===o.dimensionId)))||void 0===r?void 0:r.default_unit_id))]:[n,t]})));c.setFieldsValue(n)},w=()=>{var e,l,t;const i=null===n||void 0===n||null===(e=n.default_val)||void 0===e?void 0:e.value.reduce(((e,l)=>{let{key:n,...t}=l;return{...e,...Object.fromEntries(Object.entries(t).map((e=>{let[l,t]=e;return[(i=n,a=l,`${i}_${a}`),t];var i,a})))}}),{});c.setFieldsValue(i),r(null===n||void 0===n||null===(l=n.default_val)||void 0===l?void 0:l.value.map((e=>e.key))),T(null===n||void 0===n||null===(t=n.default_val)||void 0===t?void 0:t.value)},T=e=>{if(l){const n=l.reduce(((e,l,n)=>({...e,[l.key]:null})),{key:(new Date).getTime()});u([...e,n])}},S={selectedRowKeys:o,onChange:e=>{var l;const n=s.filter(((l,n)=>e.includes(l.key)||n===s.length-1));r(e),e.includes(null===(l=n[n.length-1])||void 0===l?void 0:l.key)?(T(n),F(!1,e)):F(!0,e)}},F=async function(e){let l=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];const t=await c.validateFields(),a=s.filter((n=>!e||l.some((e=>e===n.key)))).map((e=>{const{key:l}=e;return{...Object.fromEntries(Object.entries(t).map((e=>{let[n,t]=e;const[i,a]=n.split("_");if(i!==l.toString())return!1;const o=g.find((e=>e.key===a));return o&&o.dataType===C&&null!==o&&void 0!==o&&o.dimensionId&&null!==o&&void 0!==o&&o.unitId?[a,(0,b.tJ)(t,o.dimensionId,d.find((e=>e.id===o.dimensionId)).default_unit_id,o.unitId)]:[a,t]})).filter((e=>e))),key:l}})),o={...n,default_val:{...n.default_val,value:a}};i(o)};return(0,k.jsx)(y.A,{form:c,component:!1,style:{width:"100%"},validateTrigger:"onBlur",onValuesChange:e=>{Object.keys(e).some((e=>{const[l]=e.split("_");return!o.includes(Number(l))}))||F(!0,o)},children:(0,k.jsx)(j.A,{bordered:!0,rowSelection:S,columns:p,dataSource:s,style:{width:"100%",border:"1px solid rgb(204,204,204)",borderRadius:"8px"}})})};var S=n(79806);const F=e=>{let{config:l=[],value:n=[]}=e;const{columns:t=[],data:i=[]}=((e,l)=>{var n;const t=e.find((e=>e.isHorizontalTitle)),i=e.filter((e=>!e.isHorizontalTitle)),a=null===i||void 0===i?void 0:i.map((e=>{const n={};return l.forEach((l=>{const i=l[t.key];n[i]=l[e.key]})),{[t.key]:e.key,...n}}));return{columns:null!==a&&void 0!==a&&a[0]?null===(n=Object.entries(null===a||void 0===a?void 0:a[0]))||void 0===n?void 0:n.map((e=>{let[l,n]=e;return{title:l,dataIndex:l,key:l}})):[],data:a}})(l,n);return(0,k.jsx)(S.A,{columns:t,dataSource:i,pagination:!1})},V=e=>{var l;let{config:{attr:{isHorizontal:n,dataColumnsConfig:t}={},variable:{value:i}={}}={}}=e;const a=(0,u.A)(null===i||void 0===i?void 0:i.code);return(0,k.jsx)(m,{children:n?(0,k.jsx)(F,{config:t,value:null===a||void 0===a||null===(l=a.default_val)||void 0===l?void 0:l.value}):(0,k.jsx)(T,{dataColumnsConfig:t,variable:a,handleUpdateVariable:e=>(async e=>{await(0,s.Tnc)(e)&&(0,c.P)({code:e.code},e)})(e)})})};var z=n(8918),E=n(12624),O=n(68358),B=n(13830),H=n(16569),P=n(63942),N=n(88483),L=n(6051);const R=e=>{let{handleEdit:l,handleDel:n,handleTitleChecked:t}=e;return[{title:"\u6807\u9898",dataIndex:"title",key:"id"},{title:"\u6570\u636e\u7c7b\u578b",dataIndex:"dataType",key:"id"},{title:"\u6a2a\u5411\u6807\u9898",dataIndex:"isHorizontalTitle",key:"id",render:(e,l)=>(0,k.jsx)(E.A,{checked:e,onChange:e=>t(e,l)})},{title:"\u64cd\u4f5c",render:(e,t)=>(0,k.jsxs)(L.A,{size:"middle",children:[(0,k.jsx)("a",{onClick:()=>l(t),children:"\u7f16\u8f91"}),(0,k.jsx)("a",{onClick:()=>n(t),children:"\u5220\u9664"})]})}]},U={"\u6587\u672c":"text","\u6570\u5b57":"number"},{Item:D}=y.A,M=e=>{let{open:l,value:n,onOk:i,onCancel:a}=e;const d=(0,t.useRef)();(0,t.useEffect)((()=>{var e,l;n?null===(e=d.current)||void 0===e||e.setFieldsValue(n):null===(l=d.current)||void 0===l||l.resetFields()}),[n]);return(0,k.jsx)(P.A,{title:"\u6570\u636e\u5217\u8bbe\u7f6e",open:l,onOk:async()=>{var e;const l=await(null===(e=d.current)||void 0===e?void 0:e.validateFields());i(l)},onCancel:a,children:(0,k.jsxs)(y.A,{ref:d,labelCol:{style:{width:"147px"}},children:[(0,k.jsx)(D,{label:"\u5217\u6807\u9898",name:"title",children:(0,k.jsx)(h.A,{style:{width:"70%"}})}),(0,k.jsx)(D,{label:"\u6570\u636e\u7c7b\u578b",name:"dataType",rules:[{required:!0}],children:(0,k.jsx)(w.A,{style:{width:"70%"},options:Object.entries(U).map((e=>{let[l,n]=e;return{label:l,value:n}}))})}),(0,k.jsx)(D,{noStyle:!0,shouldUpdate:(e,l)=>e.dataType!==l.dataType,children:e=>{let{getFieldValue:l}=e;return l("dataType")===U.\u6570\u5b57?(0,k.jsx)(D,{label:"\u91cf\u7eb2",name:"dimensionId",rules:[{required:!0}],children:(0,k.jsx)(N.A,{})}):(0,k.jsx)(k.Fragment,{})}}),(0,k.jsx)(D,{label:"\u6570\u636e\u5b57\u6bb5",name:"key",rules:[{required:!0}],children:(0,k.jsx)(h.A,{style:{width:"70%"}})})]})})},Y=e=>{let{id:l,value:n=[],onChange:i}=e;const[a,d]=(0,t.useState)(!1),[o,r]=(0,t.useState)();return(0,k.jsxs)(k.Fragment,{children:[(0,k.jsx)(S.A,{columns:R({handleEdit:e=>{r(e),d(!0)},handleDel:e=>{const l=null===n||void 0===n?void 0:n.filter((l=>l.id!==e.id));null!==l&&void 0!==l&&l.some((e=>e.isHorizontalTitle))?i(l):i(l.map(((e,l)=>0===l?{...e,isHorizontalTitle:!0}:e)))},handleTitleChecked:(e,l)=>{e?i(n.map((e=>e.isHorizontalTitle?{...e,isHorizontalTitle:!1}:e.id===l.id?{...e,isHorizontalTitle:!0}:e))):H.Ay.error("\u5fc5\u987b\u9700\u8981\u4e00\u4e2a\u6a2a\u5411\u6807\u9898")}}),dataSource:n,pagination:!1}),(0,k.jsx)("a",{onClick:()=>{r(),d(!0)},children:"\u6dfb\u52a0\u4e00\u5217"}),a?(0,k.jsx)(M,{open:a,value:o,onOk:e=>{i(o?n.map((l=>l.id===o.id?{...o,...e}:l)):[...n,{...e,isHorizontalTitle:!n.some((e=>e.isHorizontalTitle)),id:(new Date).getTime()}]),d(!1)},onCancel:()=>{d(!1)}}):null]})},{useForm:$,Item:q}=y.A,J=e=>{let{open:l,onClose:n,config:i,setConfig:a}=e;const{t:o}=(0,f.Bd)(),[r]=$();(0,t.useEffect)((()=>{d()(i,r.getFieldsValue())||r.setFieldsValue(i)}),[i]);return(0,k.jsx)(O.A,{open:l,onClose:n,children:(0,k.jsx)(y.A,{form:r,labelCol:{span:6},wrapperCol:{span:18},onValuesChange:(e,l)=>{var n;let t=l;null!==e&&void 0!==e&&null!==(n=e.variable)&&void 0!==n&&n.value&&(t={...t,attr:{...t.attr,label:e.variable.value.variable_name}}),a(t)},children:(0,k.jsx)(z.A,{defaultActiveKey:"attr",items:[{key:"attr",label:o("\u5c5e\u6027"),forceRender:!0,children:(0,k.jsxs)(k.Fragment,{children:[(0,k.jsx)(q,{label:o("\u6570\u636e\u5217"),name:["attr","dataColumnsConfig"],children:(0,k.jsx)(Y,{})}),(0,k.jsx)(q,{label:o("\u662f\u5426\u6a2a\u5411"),name:["attr","isHorizontal"],valuePropName:"checked",children:(0,k.jsx)(E.A,{})})]})},{key:"variable",label:o("\u53d8\u91cf"),forceRender:!0,children:(0,k.jsx)(k.Fragment,{children:(0,k.jsx)(q,{label:o("\u503c"),name:["variable","value"],children:(0,k.jsx)(B.A,{inputVariableType:B.p[o("\u81ea\u5b9a\u4e49\u6570\u7ec4")]})})})}]})})})},K=i.Ay.div`
    width: 100%;

    overflow: hidden;
`,W=e=>{var l;let{item:n,id:i,layoutConfig:a}=e;const{updateLayoutItem:s}=(0,r.A)(),[u,c]=(0,t.useState)(!1),[v,m]=(0,t.useState)(g);(0,t.useEffect)((()=>{try{if(null!==n&&void 0!==n&&n.data_source){const{comp_config:e}=JSON.parse(null===n||void 0===n?void 0:n.data_source);e&&!d()(e,v)&&m(e)}}catch(e){console.log("err",e)}}),[null===n||void 0===n?void 0:n.data_source]);return(0,k.jsxs)(K,{id:i,compWidth:null===v||void 0===v||null===(l=v.attr)||void 0===l?void 0:l.compWidth,children:[(0,k.jsx)(V,{config:v}),(0,k.jsx)(J,{open:u,onClose:()=>{c(!1),s({layout:a,newItem:{...n,data_source:JSON.stringify({comp_config:v})}})},config:v,setConfig:m}),(0,k.jsx)(o.A,{domId:i,layoutConfig:a,children:(0,k.jsx)("div",{className:"unique-content",onClick:()=>c(!0),children:"\u7f16\u8f91\u6570\u636e\u91c7\u96c6\u8868\u683c"})})]})}},51554:(e,l,n)=>{n.d(l,{A:()=>x});var t=n(65043),i=n(80077),a=n(16569),d=n(83720),o=n(79806),r=n(74117),s=n(93950),u=n.n(s),c=n(56543),v=n(75440),m=n(29977),h=n(6051),p=n(70579);const y=e=>{let{handleSelected:l,t:n}=e;return[{title:n?n("\u540d\u79f0"):"\u540d\u79f0",dataIndex:"variable_name",key:"variable_name"},{title:n?n("\u6807\u8bc6\u7b26"):"\u6807\u8bc6\u7b26",dataIndex:"code",key:"code"},{title:n?n("\u64cd\u4f5c"):"\u64cd\u4f5c",dataIndex:"code",key:"code",render:(e,n)=>(0,p.jsx)(h.A,{size:"middle",children:(0,p.jsx)("a",{onClick:()=>l(n),children:"\u9009\u62e9"})})}]},f=(e,l)=>{let{handleSelectedVariable:n=e=>console.log(e),isSetProgrammableParameters:s=!1}=e;const h=(0,m.A)(),f=(0,i.d4)((e=>e.template.resultData)),[x,b]=(0,t.useState)(!1),[j,g]=(0,t.useState)(),[A,C]=(0,t.useState)([]),[w,k]=(0,t.useState)([]),{t:_}=(0,r.Bd)(),I=(0,t.useMemo)((()=>h.map((e=>({...e,variable_name:null===e||void 0===e?void 0:e.name})))),[h]),T=(0,t.useMemo)((()=>f.map((e=>({...e,id:e.code})))),[f]);(0,t.useEffect)((()=>{x&&S()}),[x]);const S=()=>{if(j)switch(null===j||void 0===j?void 0:j.variableType){case c.oY.\u8f93\u5165\u53d8\u91cf:{const e=[...I.filter((e=>!(null!==j&&void 0!==j&&j.inputVarType)||e.variable_type===(null===j||void 0===j?void 0:j.inputVarType)))];k(e),C(e);break}case c.oY.\u4fe1\u53f7\u53d8\u91cf:case c.oY.\u7ed3\u679c\u53d8\u91cf:k(T),C(T);break;default:console.log("\u672a\u5904\u7406\u7684\u53d8\u91cf\u7c7b\u578b",null===j||void 0===j?void 0:j.variableType)}};(0,t.useImperativeHandle)(l,(()=>({open:e=>{g(e),b(!0)}})));const F=u()((async e=>{if(e){const l=A.filter((l=>{const n=l.variable_name.toLowerCase(),t=l.code.toLowerCase(),i=e.toLowerCase();return n.includes(i)||t.includes(i)}));k(l)}else k(A)}),200);return(0,p.jsxs)(v.A,{open:x,onCancel:()=>{b(!1)},title:"\u53d8\u91cf\u9009\u62e9",footer:null,children:[(0,p.jsx)(d.A,{allowClear:!0,onChange:e=>F(e.target.value),placeholder:_("\u540d\u79f0/\u5185\u90e8\u540d"),style:{width:"300px",marginBottom:"10px"}}),(0,p.jsx)(o.A,{rowKey:"code",columns:y({handleSelected:e=>{var l;!s||"Array"===(null===e||void 0===e?void 0:e.variable_type)&&"programmableParameters"===(null===e||void 0===e||null===(l=e.custom_array_tab)||void 0===l?void 0:l.useType)?(n(e,j),b(!1)):a.Ay.error("\u8bf7\u9009\u62e9\u81ea\u5b9a\u4e49\u6570\u7ec4\u7528\u9014\u4e3a\u7a0b\u63a7\u53c2\u6570\u7684\u53d8\u91cf")}}),dataSource:w})]})},x=(0,t.forwardRef)(f)},68358:(e,l,n)=>{n.d(l,{A:()=>m});var t=n(65043),i=n(48677),a=n(80077),d=n(14463),o=n(25055),r=n(36282),s=n(96603),u=n(14524),c=n(70579);const v=e=>{let{setting:l,onChange:n}=e;const[i]=o.A.useForm();(0,t.useEffect)((()=>{i.setFieldsValue({...l})}),[l]);return(0,c.jsx)(r.A,{content:(0,c.jsxs)(o.A,{form:i,name:"basic",labelCol:{style:{width:35}},onValuesChange:(e,l)=>{n(l)},children:[(0,c.jsx)(o.A.Item,{label:"\u4f4d\u7f6e",name:"placement",children:(0,c.jsxs)(s.Ay.Group,{size:"small",children:[(0,c.jsx)(s.Ay.Button,{value:"top",children:"\u4e0a"}),(0,c.jsx)(s.Ay.Button,{value:"right",children:"\u53f3"}),(0,c.jsx)(s.Ay.Button,{value:"bottom",children:"\u4e0b"}),(0,c.jsx)(s.Ay.Button,{value:"left",children:"\u5de6"})]})}),(0,c.jsx)(o.A.Item,{label:"\u5c3a\u5bf8",name:"size",children:(0,c.jsxs)(s.Ay.Group,{size:"small",children:[(0,c.jsx)(s.Ay.Button,{value:"default",children:"\u6b63\u5e38"}),(0,c.jsx)(s.Ay.Button,{value:"large",children:"\u5927"})]})})]}),title:"",trigger:"click",placement:"leftTop",children:(0,c.jsx)(u.A,{})})},m=e=>{let{children:l,open:n,onClose:t}=e;const o=(0,a.wA)(),{drawSetting:r}=(0,a.d4)((e=>e.split));return(0,c.jsx)(c.Fragment,{children:n&&(0,c.jsx)(i.A,{open:n,size:null===r||void 0===r?void 0:r.size,placement:null===r||void 0===r?void 0:r.placement,onClose:t,extra:(0,c.jsx)(v,{setting:r,onChange:e=>{o({type:d.cd,param:e})}}),children:l})})}},97320:(e,l,n)=>{n.d(l,{A:()=>r});n(65043);var t=n(80077),i=n(84856),a=n(67208),d=n(14463),o=n(41086);const r=()=>{const e=(0,t.wA)(),{saveLayout:l}=(0,i.A)(),n=async l=>{let{layout:n,newItem:t}=l;const i={...n,children:r(n.children,t)},[s]=await(0,a.PXE)({binder_ids:[null===n||void 0===n?void 0:n.binder_id]});await(0,a.Kv3)({binders:[{...s,layout:(0,o.gT)(i,null===n||void 0===n?void 0:n.binder_id)}]}),e({type:d.EH,param:s.binder_id})},r=(e,l)=>e.map((e=>e.id===l.id?l:e.children&&e.children.length>0?{...e,children:r(e.children,l)}:e)),s=async e=>{let{layout:n,newItem:t}=e;const i={...n,children:r(n.children,t)};await l(i)};return{updateLayoutItem:async e=>{let{layout:l,newItem:t}=e;null!==l&&void 0!==l&&l.binder_id?(console.log("\u754c\u9762\u5185\u5bb9-tab"),await n({layout:l,newItem:t})):(console.log("\u754c\u9762\u5185\u5bb9-\u4e3b\u7a97\u53e3"),await s({layout:l,newItem:t}))}}}}}]);
//# sourceMappingURL=4807.4a3d9a6e.chunk.js.map