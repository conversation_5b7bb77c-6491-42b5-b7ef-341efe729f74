using System.Diagnostics;
using System.Text.Json;
using Consts;
using Logging;
using MQ;
using Scripting;
using SubTaskUtils;

namespace SubTasks.tasks.userInteractionTask;

/// <summary>
/// UI操作子任务:
///     操作          key             uiCmd
///     保存          save            Save
///     另存为        saveAs          SaveAs
///     模版配置      settings        OpenSetting
///     退出          quit            Quit
///     关闭          close           Close
/// </summary>
public class SubtaskUIOperations: ISubTask
{
    private string? _processId;
    private string? _subtaskId;
    private SubTaskCmdParams? _subtaskCmdParams;

    public MQSubTaskSub[] subs { set; get; }
    public bool Sub_TASK_MGR_CMD { get; set; } = true;
    public bool Sub_TASK_MGR_CMD_Other { get; set; } = true;
    public bool Sub_TASK_HARDWARE_CMD { get; set; } = false;
    public bool Sub_TASK_HARDWARE_DATA { get; set; } = false;
    public bool Sub_TOPIC_FROM_UI { get; set; } = true;
    public bool Sub_TOPIC_FROM_SCRIPT_CLIENT { get; set; } = false;
    public bool Sub_SelfTopic { get; set; } = false;
    public bool Sub_TOPIC_NOTIFY { get; set; } = false;

    public SubTaskCmdParams ImportParams(string paramatersString)
    {
        CCSSLogger.Logger.Info("ImportPrams" + " :" + paramatersString);
        var x = JsonSerializer.Deserialize<SubTaskCmdParams>(paramatersString)!;
        CCSSLogger.Logger.Info("Json 反序列化" + x);

        return x;
    }

    public JsonElement UIParams()
    {
        throw new NotImplementedException();
    }

    /// 在创建时返回一个 bool值, 代表是否创建成功
    public bool Run(SubTaskCmdParams t)
    {
        _processId = t.ProcessID;
        _subtaskId = t.SubTaskID;
        _subtaskCmdParams = t;
        
        ISystemBus.SendToUIStatusTopic(UtilsForSubTasks.GenerateStatusUIJson(t.Cmd(), _processId!, _subtaskId!));
        var templateInst = ITemplate.GetTemplateByName(t.ClassName!);
        // 获取命令key
        string cmdKey = UtilsForSubTasks.ReadVarValue<string>(templateInst, t.SubTaskParams.GetProperty("schedule").GetProperty("control_input_cmd"));
        ISystemBus.SendToUICmdTopic(JsonSerializer.Serialize(new UICmdParams(
            _processId, 
            _subtaskId,
            cmdKey switch
            {
                "save" => "Save",
                "saveAs" => "SaveAs",
                "settings" => "OpenSettings",
                "quit" => "Quit",
                "close" => "Close",
                _ => "Quit"
            },
            JsonDocument.Parse("{}").RootElement)));
        
       
        CCSSLogger.Logger.Info("输入变量子任务 启动:" + t);
        Finish(t);
        return true;
    }

    /// 终止执行
    public bool Abort(SubTaskCmdParams t)
    {
        ISystemBus.SendToUIStatusTopic(UtilsForSubTasks.GenerateStatusUIJson(CmdConsts.RCV_ABORT_TASK_CMD, _processId!, _subtaskId!));
        CCSSLogger.Logger.Info("输入变量子任务 终止:" + CmdConsts.RCV_ABORT_TASK_CMD);
        // TODO: 通知前端关闭对话框
        ((ISubTask)this).CleanAllSubs();
        return true;
    }

    /// <summary>
    /// 输入变量子任务: 暂停 通知前端修改状态(不做任何其他修改) 
    /// </summary>
    public bool Pause(SubTaskCmdParams t)
    {
        ISystemBus.SendToUIStatusTopic(UtilsForSubTasks.GenerateStatusUIJson(CmdConsts.RCV_PAUSE_TASK_CMD, _processId!, _subtaskId!));
        CCSSLogger.Logger.Info("输入变量子任务 暂停:" + CmdConsts.RCV_PAUSE_TASK_CMD);
        return true;
    }
    
    /// <summary>
    /// 输入变量子任务: 恢复 通知前端修改状态(不做任何其他修改) 
    /// </summary>
    public bool Resume(SubTaskCmdParams t)
    {
        ISystemBus.SendToUIStatusTopic(UtilsForSubTasks.GenerateStatusUIJson(CmdConsts.RCV_PAUSE_TASK_CMD, _processId!, _subtaskId!));
        CCSSLogger.Logger.Info("输入变量子任务 恢复:" + CmdConsts.RCV_RESUME_TASK_CMD);
        return true;
    }

    public bool Finish(SubTaskCmdParams t)
    {
        ISystemBus.SendToUIStatusTopic(UtilsForSubTasks.GenerateStatusUIJson(CmdConsts.RCV_FINISH_TASK_CMD, _processId!, _subtaskId!));
        ISystemBus.SendToTaskUpTopic(CmdConsts.SubTaskFinishCmd(t.ClassName!, t.ProcessID!, t.SubTaskID!));
        CCSSLogger.Logger.Info("输入变量子任务 结束:" + CmdConsts.RCV_FINISH_TASK_CMD);
        ((ISubTask)this).CleanAllSubs();
        return true;
    }

    public bool ProcessData(SubTaskCmdParams p)
    {
        throw new NotImplementedException();
    }

    public void ImportHwFuncRet(string paramatersString)
    {
        throw new NotImplementedException();
    }

    public void HandleMsgFromUI(string paramatersString)
    {
    }
    public string[] GetSelfTopic()
    {
        var selfTopics = Array.Empty<string>();
        return selfTopics;
    }

    public void HandleMsgFromVAR(string topic, string paramatersString)
    {
        throw new NotImplementedException();
    }

    public void HandleMsgFromScript(string paramatersString)
    {
        throw new NotImplementedException();
    }

    public bool ReStart(SubTaskCmdParams p)
    {
        throw new NotImplementedException();
    }

    public bool Error(SubTaskCmdParams p)
    {
        throw new NotImplementedException();
    }
    public void HandleNotify(string p, string msg)
    {
        throw new NotImplementedException();
    }
}