2025-09-10 14:00:38,797 [qtp1639139601-284] ERROR clj-backend.middleware.exception - 服务器错误 
clojure.lang.ExceptionInfo: 服务器错误
	at clj_backend.common.biz_error$backend_throw.invokeStatic(biz_error.clj:15)
	at clj_backend.common.biz_error$backend_throw.invoke(biz_error.clj:11)
	at clj_backend.common.biz_error$throw_error.invokeStatic(biz_error.clj:59)
	at clj_backend.common.biz_error$throw_error.invoke(biz_error.clj:32)
	at clj_backend.modules.script.service$handle_result.invokeStatic(service.clj:831)
	at clj_backend.modules.script.service$handle_result.invoke(service.clj:826)
	at clj_backend.modules.script.service$class_def.invokeStatic(service.clj:873)
	at clj_backend.modules.script.service$class_def.invoke(service.clj:853)
	at clj_backend.modules.script.service$class_def.invokeStatic(service.clj:859)
	at clj_backend.modules.script.service$class_def.invoke(service.clj:853)
	at clj_backend.modules.project.project_service$open_project$fn__47969$fn__47970.invoke(project_service.clj:341)
	at next.jdbc.transaction$transact_STAR_.invokeStatic(transaction.clj:72)
	at next.jdbc.transaction$transact_STAR_.invoke(transaction.clj:51)
	at next.jdbc.transaction$eval4714$fn__4715.invoke(transaction.clj:121)
	at next.jdbc.protocols$eval3706$fn__3707$G__3697__3716.invoke(protocols.clj:57)
	at next.jdbc$transact.invokeStatic(jdbc.clj:381)
	at next.jdbc$transact.invoke(jdbc.clj:373)
	at clj_backend.modules.project.project_service$open_project$fn__47969.invoke(project_service.clj:339)
	at next.jdbc.transaction$transact_STAR_.invokeStatic(transaction.clj:72)
	at next.jdbc.transaction$transact_STAR_.invoke(transaction.clj:51)
	at next.jdbc.transaction$eval4714$fn__4715.invoke(transaction.clj:121)
	at next.jdbc.protocols$eval3706$fn__3707$G__3697__3716.invoke(protocols.clj:57)
	at next.jdbc$transact.invokeStatic(jdbc.clj:381)
	at next.jdbc$transact.invoke(jdbc.clj:373)
	at clj_backend.modules.project.project_service$open_project.invokeStatic(project_service.clj:336)
	at clj_backend.modules.project.project_service$open_project.invoke(project_service.clj:328)
	at clj_backend.modules.project.project_routes$routes$fn__48144.invoke(project_routes.clj:72)
	at clj_backend.common.trial$trial_middleware$fn__42635.invoke(trial.clj:73)
	at reitit.ring.coercion$fn__52489$fn__52491$fn__52492.invoke(coercion.cljc:40)
	at reitit.ring.coercion$fn__52512$fn__52514$fn__52515.invoke(coercion.cljc:80)
	at reitit.ring.middleware.exception$wrap$fn__49468$fn__49469.invoke(exception.clj:49)
	at clj_backend.middleware.logger$logger_middleware$fn__52707.invoke(logger.clj:18)
	at muuntaja.middleware$wrap_format_request$fn__52630.invoke(middleware.clj:114)
	at muuntaja.middleware$wrap_format_response$fn__52634.invoke(middleware.clj:132)
	at muuntaja.middleware$wrap_format_negotiate$fn__52627.invoke(middleware.clj:96)
	at ring.middleware.params$wrap_params$fn__18645.invoke(params.clj:67)
	at reitit.ring$ring_handler$fn__12807.invoke(ring.cljc:329)
	at clojure.lang.AFn.applyToHelper(AFn.java:154)
	at clojure.lang.AFn.applyTo(AFn.java:144)
	at clojure.lang.AFunction$1.doInvoke(AFunction.java:33)
	at clojure.lang.RestFn.invoke(RestFn.java:411)
	at clojure.lang.Var.invoke(Var.java:386)
	at ring.middleware.reload$wrap_reload$fn__16585.invoke(reload.clj:39)
	at selmer.middleware$wrap_error_page$fn__16600.invoke(middleware.clj:18)
	at prone.middleware$wrap_exceptions$fn__16842.invoke(middleware.clj:169)
	at ring.middleware.flash$wrap_flash$fn__16963.invoke(flash.clj:39)
	at ring.middleware.session$wrap_session$fn__18174.invoke(session.clj:108)
	at ring.middleware.cors$handle_cors.invokeStatic(cors.cljc:175)
	at ring.middleware.cors$handle_cors.invoke(cors.cljc:167)
	at ring.middleware.cors$wrap_cors$fn__16942.invoke(cors.cljc:205)
	at ring.middleware.keyword_params$wrap_keyword_params$fn__18265.invoke(keyword_params.clj:53)
	at ring.middleware.nested_params$wrap_nested_params$fn__18323.invoke(nested_params.clj:89)
	at ring.middleware.multipart_params$wrap_multipart_params$fn__18621.invoke(multipart_params.clj:173)
	at ring.middleware.params$wrap_params$fn__18645.invoke(params.clj:67)
	at ring.middleware.cookies$wrap_cookies$fn__18053.invoke(cookies.clj:175)
	at ring.middleware.absolute_redirects$wrap_absolute_redirects$fn__18816.invoke(absolute_redirects.clj:47)
	at ring.middleware.resource$wrap_resource_prefer_resources$fn__18681.invoke(resource.clj:25)
	at ring.middleware.content_type$wrap_content_type$fn__18764.invoke(content_type.clj:34)
	at ring.middleware.default_charset$wrap_default_charset$fn__18788.invoke(default_charset.clj:31)
	at ring.middleware.not_modified$wrap_not_modified$fn__18745.invoke(not_modified.clj:61)
	at ring.middleware.x_headers$wrap_x_header$fn__18205.invoke(x_headers.clj:22)
	at ring.middleware.x_headers$wrap_x_header$fn__18205.invoke(x_headers.clj:22)
	at ring.middleware.x_headers$wrap_x_header$fn__18205.invoke(x_headers.clj:22)
	at ring.adapter.jetty9$proxy_handler$fn__58690.invoke(jetty9.clj:75)
	at ring.adapter.jetty9.proxy$org.eclipse.jetty.servlet.ServletHandler$ff19274a.doHandle(Unknown Source)
	at org.eclipse.jetty.server.handler.ScopedHandler.nextHandle(ScopedHandler.java:221)
	at org.eclipse.jetty.server.handler.ContextHandler.doHandle(ContextHandler.java:1378)
	at org.eclipse.jetty.server.handler.ScopedHandler.nextScope(ScopedHandler.java:176)
	at org.eclipse.jetty.servlet.ServletHandler.doScope(ServletHandler.java:463)
	at ring.adapter.jetty9.proxy$org.eclipse.jetty.servlet.ServletHandler$ff19274a.doScope(Unknown Source)
	at org.eclipse.jetty.server.handler.ScopedHandler.nextScope(ScopedHandler.java:174)
	at org.eclipse.jetty.server.handler.ContextHandler.doScope(ContextHandler.java:1300)
	at org.eclipse.jetty.server.handler.ScopedHandler.handle(ScopedHandler.java:129)
	at org.eclipse.jetty.server.handler.HandlerWrapper.handle(HandlerWrapper.java:122)
	at org.eclipse.jetty.server.Server.handle(Server.java:562)
	at org.eclipse.jetty.server.HttpChannel.lambda$handle$0(HttpChannel.java:418)
	at org.eclipse.jetty.server.HttpChannel.dispatch(HttpChannel.java:675)
	at org.eclipse.jetty.server.HttpChannel.handle(HttpChannel.java:410)
	at org.eclipse.jetty.server.HttpConnection.onFillable(HttpConnection.java:282)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:319)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:100)
	at org.eclipse.jetty.io.SocketChannelEndPoint$1.run(SocketChannelEndPoint.java:101)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.runTask(AdaptiveExecutionStrategy.java:412)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.consumeTask(AdaptiveExecutionStrategy.java:381)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.tryProduce(AdaptiveExecutionStrategy.java:268)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.lambda$new$0(AdaptiveExecutionStrategy.java:138)
	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:407)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:894)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1038)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-09-10 14:00:42,680 [qtp1639139601-242] ERROR clj-backend.middleware.exception - 服务器错误 
clojure.lang.ExceptionInfo: 服务器错误
	at clj_backend.common.biz_error$backend_throw.invokeStatic(biz_error.clj:15)
	at clj_backend.common.biz_error$backend_throw.invoke(biz_error.clj:11)
	at clj_backend.common.biz_error$throw_error.invokeStatic(biz_error.clj:59)
	at clj_backend.common.biz_error$throw_error.invoke(biz_error.clj:32)
	at clj_backend.modules.script.service$handle_result.invokeStatic(service.clj:831)
	at clj_backend.modules.script.service$handle_result.invoke(service.clj:826)
	at clj_backend.modules.script.service$class_def.invokeStatic(service.clj:873)
	at clj_backend.modules.script.service$class_def.invoke(service.clj:853)
	at clj_backend.modules.script.service$class_def.invokeStatic(service.clj:859)
	at clj_backend.modules.script.service$class_def.invoke(service.clj:853)
	at clj_backend.modules.project.project_service$open_project$fn__47969$fn__47970.invoke(project_service.clj:341)
	at next.jdbc.transaction$transact_STAR_.invokeStatic(transaction.clj:72)
	at next.jdbc.transaction$transact_STAR_.invoke(transaction.clj:51)
	at next.jdbc.transaction$eval4714$fn__4715.invoke(transaction.clj:121)
	at next.jdbc.protocols$eval3706$fn__3707$G__3697__3716.invoke(protocols.clj:57)
	at next.jdbc$transact.invokeStatic(jdbc.clj:381)
	at next.jdbc$transact.invoke(jdbc.clj:373)
	at clj_backend.modules.project.project_service$open_project$fn__47969.invoke(project_service.clj:339)
	at next.jdbc.transaction$transact_STAR_.invokeStatic(transaction.clj:72)
	at next.jdbc.transaction$transact_STAR_.invoke(transaction.clj:51)
	at next.jdbc.transaction$eval4714$fn__4715.invoke(transaction.clj:121)
	at next.jdbc.protocols$eval3706$fn__3707$G__3697__3716.invoke(protocols.clj:57)
	at next.jdbc$transact.invokeStatic(jdbc.clj:381)
	at next.jdbc$transact.invoke(jdbc.clj:373)
	at clj_backend.modules.project.project_service$open_project.invokeStatic(project_service.clj:336)
	at clj_backend.modules.project.project_service$open_project.invoke(project_service.clj:328)
	at clj_backend.modules.project.project_routes$routes$fn__48144.invoke(project_routes.clj:72)
	at clj_backend.common.trial$trial_middleware$fn__42635.invoke(trial.clj:73)
	at reitit.ring.coercion$fn__52489$fn__52491$fn__52492.invoke(coercion.cljc:40)
	at reitit.ring.coercion$fn__52512$fn__52514$fn__52515.invoke(coercion.cljc:80)
	at reitit.ring.middleware.exception$wrap$fn__49468$fn__49469.invoke(exception.clj:49)
	at clj_backend.middleware.logger$logger_middleware$fn__52707.invoke(logger.clj:18)
	at muuntaja.middleware$wrap_format_request$fn__52630.invoke(middleware.clj:114)
	at muuntaja.middleware$wrap_format_response$fn__52634.invoke(middleware.clj:132)
	at muuntaja.middleware$wrap_format_negotiate$fn__52627.invoke(middleware.clj:96)
	at ring.middleware.params$wrap_params$fn__18645.invoke(params.clj:67)
	at reitit.ring$ring_handler$fn__12807.invoke(ring.cljc:329)
	at clojure.lang.AFn.applyToHelper(AFn.java:154)
	at clojure.lang.AFn.applyTo(AFn.java:144)
	at clojure.lang.AFunction$1.doInvoke(AFunction.java:33)
	at clojure.lang.RestFn.invoke(RestFn.java:411)
	at clojure.lang.Var.invoke(Var.java:386)
	at ring.middleware.reload$wrap_reload$fn__16585.invoke(reload.clj:39)
	at selmer.middleware$wrap_error_page$fn__16600.invoke(middleware.clj:18)
	at prone.middleware$wrap_exceptions$fn__16842.invoke(middleware.clj:169)
	at ring.middleware.flash$wrap_flash$fn__16963.invoke(flash.clj:39)
	at ring.middleware.session$wrap_session$fn__18174.invoke(session.clj:108)
	at ring.middleware.cors$handle_cors.invokeStatic(cors.cljc:175)
	at ring.middleware.cors$handle_cors.invoke(cors.cljc:167)
	at ring.middleware.cors$wrap_cors$fn__16942.invoke(cors.cljc:205)
	at ring.middleware.keyword_params$wrap_keyword_params$fn__18265.invoke(keyword_params.clj:53)
	at ring.middleware.nested_params$wrap_nested_params$fn__18323.invoke(nested_params.clj:89)
	at ring.middleware.multipart_params$wrap_multipart_params$fn__18621.invoke(multipart_params.clj:173)
	at ring.middleware.params$wrap_params$fn__18645.invoke(params.clj:67)
	at ring.middleware.cookies$wrap_cookies$fn__18053.invoke(cookies.clj:175)
	at ring.middleware.absolute_redirects$wrap_absolute_redirects$fn__18816.invoke(absolute_redirects.clj:47)
	at ring.middleware.resource$wrap_resource_prefer_resources$fn__18681.invoke(resource.clj:25)
	at ring.middleware.content_type$wrap_content_type$fn__18764.invoke(content_type.clj:34)
	at ring.middleware.default_charset$wrap_default_charset$fn__18788.invoke(default_charset.clj:31)
	at ring.middleware.not_modified$wrap_not_modified$fn__18745.invoke(not_modified.clj:61)
	at ring.middleware.x_headers$wrap_x_header$fn__18205.invoke(x_headers.clj:22)
	at ring.middleware.x_headers$wrap_x_header$fn__18205.invoke(x_headers.clj:22)
	at ring.middleware.x_headers$wrap_x_header$fn__18205.invoke(x_headers.clj:22)
	at ring.adapter.jetty9$proxy_handler$fn__58690.invoke(jetty9.clj:75)
	at ring.adapter.jetty9.proxy$org.eclipse.jetty.servlet.ServletHandler$ff19274a.doHandle(Unknown Source)
	at org.eclipse.jetty.server.handler.ScopedHandler.nextHandle(ScopedHandler.java:221)
	at org.eclipse.jetty.server.handler.ContextHandler.doHandle(ContextHandler.java:1378)
	at org.eclipse.jetty.server.handler.ScopedHandler.nextScope(ScopedHandler.java:176)
	at org.eclipse.jetty.servlet.ServletHandler.doScope(ServletHandler.java:463)
	at ring.adapter.jetty9.proxy$org.eclipse.jetty.servlet.ServletHandler$ff19274a.doScope(Unknown Source)
	at org.eclipse.jetty.server.handler.ScopedHandler.nextScope(ScopedHandler.java:174)
	at org.eclipse.jetty.server.handler.ContextHandler.doScope(ContextHandler.java:1300)
	at org.eclipse.jetty.server.handler.ScopedHandler.handle(ScopedHandler.java:129)
	at org.eclipse.jetty.server.handler.HandlerWrapper.handle(HandlerWrapper.java:122)
	at org.eclipse.jetty.server.Server.handle(Server.java:562)
	at org.eclipse.jetty.server.HttpChannel.lambda$handle$0(HttpChannel.java:418)
	at org.eclipse.jetty.server.HttpChannel.dispatch(HttpChannel.java:675)
	at org.eclipse.jetty.server.HttpChannel.handle(HttpChannel.java:410)
	at org.eclipse.jetty.server.HttpConnection.onFillable(HttpConnection.java:282)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:319)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:100)
	at org.eclipse.jetty.io.SocketChannelEndPoint$1.run(SocketChannelEndPoint.java:101)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.runTask(AdaptiveExecutionStrategy.java:412)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.consumeTask(AdaptiveExecutionStrategy.java:381)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.tryProduce(AdaptiveExecutionStrategy.java:268)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.lambda$new$0(AdaptiveExecutionStrategy.java:138)
	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:407)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:894)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1038)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-09-10 14:01:22,325 [qtp1639139601-208] ERROR clj-backend.middleware.exception - 服务器错误 
clojure.lang.ExceptionInfo: 服务器错误
	at clj_backend.common.biz_error$backend_throw.invokeStatic(biz_error.clj:15)
	at clj_backend.common.biz_error$backend_throw.invoke(biz_error.clj:11)
	at clj_backend.common.biz_error$throw_error.invokeStatic(biz_error.clj:59)
	at clj_backend.common.biz_error$throw_error.invoke(biz_error.clj:32)
	at clj_backend.modules.script.service$handle_result.invokeStatic(service.clj:831)
	at clj_backend.modules.script.service$handle_result.invoke(service.clj:826)
	at clj_backend.modules.script.service$class_def.invokeStatic(service.clj:873)
	at clj_backend.modules.script.service$class_def.invoke(service.clj:853)
	at clj_backend.modules.script.service$class_def.invokeStatic(service.clj:859)
	at clj_backend.modules.script.service$class_def.invoke(service.clj:853)
	at clj_backend.modules.project.project_service$open_project$fn__47969$fn__47970.invoke(project_service.clj:341)
	at next.jdbc.transaction$transact_STAR_.invokeStatic(transaction.clj:72)
	at next.jdbc.transaction$transact_STAR_.invoke(transaction.clj:51)
	at next.jdbc.transaction$eval4714$fn__4715.invoke(transaction.clj:121)
	at next.jdbc.protocols$eval3706$fn__3707$G__3697__3716.invoke(protocols.clj:57)
	at next.jdbc$transact.invokeStatic(jdbc.clj:381)
	at next.jdbc$transact.invoke(jdbc.clj:373)
	at clj_backend.modules.project.project_service$open_project$fn__47969.invoke(project_service.clj:339)
	at next.jdbc.transaction$transact_STAR_.invokeStatic(transaction.clj:72)
	at next.jdbc.transaction$transact_STAR_.invoke(transaction.clj:51)
	at next.jdbc.transaction$eval4714$fn__4715.invoke(transaction.clj:121)
	at next.jdbc.protocols$eval3706$fn__3707$G__3697__3716.invoke(protocols.clj:57)
	at next.jdbc$transact.invokeStatic(jdbc.clj:381)
	at next.jdbc$transact.invoke(jdbc.clj:373)
	at clj_backend.modules.project.project_service$open_project.invokeStatic(project_service.clj:336)
	at clj_backend.modules.project.project_service$open_project.invoke(project_service.clj:328)
	at clj_backend.modules.project.project_routes$routes$fn__48144.invoke(project_routes.clj:72)
	at clj_backend.common.trial$trial_middleware$fn__42635.invoke(trial.clj:73)
	at reitit.ring.coercion$fn__52489$fn__52491$fn__52492.invoke(coercion.cljc:40)
	at reitit.ring.coercion$fn__52512$fn__52514$fn__52515.invoke(coercion.cljc:80)
	at reitit.ring.middleware.exception$wrap$fn__49468$fn__49469.invoke(exception.clj:49)
	at clj_backend.middleware.logger$logger_middleware$fn__52707.invoke(logger.clj:18)
	at muuntaja.middleware$wrap_format_request$fn__52630.invoke(middleware.clj:114)
	at muuntaja.middleware$wrap_format_response$fn__52634.invoke(middleware.clj:132)
	at muuntaja.middleware$wrap_format_negotiate$fn__52627.invoke(middleware.clj:96)
	at ring.middleware.params$wrap_params$fn__18645.invoke(params.clj:67)
	at reitit.ring$ring_handler$fn__12807.invoke(ring.cljc:329)
	at clojure.lang.AFn.applyToHelper(AFn.java:154)
	at clojure.lang.AFn.applyTo(AFn.java:144)
	at clojure.lang.AFunction$1.doInvoke(AFunction.java:33)
	at clojure.lang.RestFn.invoke(RestFn.java:411)
	at clojure.lang.Var.invoke(Var.java:386)
	at ring.middleware.reload$wrap_reload$fn__16585.invoke(reload.clj:39)
	at selmer.middleware$wrap_error_page$fn__16600.invoke(middleware.clj:18)
	at prone.middleware$wrap_exceptions$fn__16842.invoke(middleware.clj:169)
	at ring.middleware.flash$wrap_flash$fn__16963.invoke(flash.clj:39)
	at ring.middleware.session$wrap_session$fn__18174.invoke(session.clj:108)
	at ring.middleware.cors$handle_cors.invokeStatic(cors.cljc:175)
	at ring.middleware.cors$handle_cors.invoke(cors.cljc:167)
	at ring.middleware.cors$wrap_cors$fn__16942.invoke(cors.cljc:205)
	at ring.middleware.keyword_params$wrap_keyword_params$fn__18265.invoke(keyword_params.clj:53)
	at ring.middleware.nested_params$wrap_nested_params$fn__18323.invoke(nested_params.clj:89)
	at ring.middleware.multipart_params$wrap_multipart_params$fn__18621.invoke(multipart_params.clj:173)
	at ring.middleware.params$wrap_params$fn__18645.invoke(params.clj:67)
	at ring.middleware.cookies$wrap_cookies$fn__18053.invoke(cookies.clj:175)
	at ring.middleware.absolute_redirects$wrap_absolute_redirects$fn__18816.invoke(absolute_redirects.clj:47)
	at ring.middleware.resource$wrap_resource_prefer_resources$fn__18681.invoke(resource.clj:25)
	at ring.middleware.content_type$wrap_content_type$fn__18764.invoke(content_type.clj:34)
	at ring.middleware.default_charset$wrap_default_charset$fn__18788.invoke(default_charset.clj:31)
	at ring.middleware.not_modified$wrap_not_modified$fn__18745.invoke(not_modified.clj:61)
	at ring.middleware.x_headers$wrap_x_header$fn__18205.invoke(x_headers.clj:22)
	at ring.middleware.x_headers$wrap_x_header$fn__18205.invoke(x_headers.clj:22)
	at ring.middleware.x_headers$wrap_x_header$fn__18205.invoke(x_headers.clj:22)
	at ring.adapter.jetty9$proxy_handler$fn__58690.invoke(jetty9.clj:75)
	at ring.adapter.jetty9.proxy$org.eclipse.jetty.servlet.ServletHandler$ff19274a.doHandle(Unknown Source)
	at org.eclipse.jetty.server.handler.ScopedHandler.nextHandle(ScopedHandler.java:221)
	at org.eclipse.jetty.server.handler.ContextHandler.doHandle(ContextHandler.java:1378)
	at org.eclipse.jetty.server.handler.ScopedHandler.nextScope(ScopedHandler.java:176)
	at org.eclipse.jetty.servlet.ServletHandler.doScope(ServletHandler.java:463)
	at ring.adapter.jetty9.proxy$org.eclipse.jetty.servlet.ServletHandler$ff19274a.doScope(Unknown Source)
	at org.eclipse.jetty.server.handler.ScopedHandler.nextScope(ScopedHandler.java:174)
	at org.eclipse.jetty.server.handler.ContextHandler.doScope(ContextHandler.java:1300)
	at org.eclipse.jetty.server.handler.ScopedHandler.handle(ScopedHandler.java:129)
	at org.eclipse.jetty.server.handler.HandlerWrapper.handle(HandlerWrapper.java:122)
	at org.eclipse.jetty.server.Server.handle(Server.java:562)
	at org.eclipse.jetty.server.HttpChannel.lambda$handle$0(HttpChannel.java:418)
	at org.eclipse.jetty.server.HttpChannel.dispatch(HttpChannel.java:675)
	at org.eclipse.jetty.server.HttpChannel.handle(HttpChannel.java:410)
	at org.eclipse.jetty.server.HttpConnection.onFillable(HttpConnection.java:282)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:319)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:100)
	at org.eclipse.jetty.io.SocketChannelEndPoint$1.run(SocketChannelEndPoint.java:101)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.runTask(AdaptiveExecutionStrategy.java:412)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.consumeTask(AdaptiveExecutionStrategy.java:381)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.tryProduce(AdaptiveExecutionStrategy.java:268)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.lambda$new$0(AdaptiveExecutionStrategy.java:138)
	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:407)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:894)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1038)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-09-10 14:01:42,072 [qtp1639139601-293] ERROR clj-backend.middleware.exception - 服务器错误 
clojure.lang.ExceptionInfo: 服务器错误
	at clj_backend.common.biz_error$backend_throw.invokeStatic(biz_error.clj:15)
	at clj_backend.common.biz_error$backend_throw.invoke(biz_error.clj:11)
	at clj_backend.common.biz_error$throw_error.invokeStatic(biz_error.clj:59)
	at clj_backend.common.biz_error$throw_error.invoke(biz_error.clj:32)
	at clj_backend.modules.script.service$handle_result.invokeStatic(service.clj:831)
	at clj_backend.modules.script.service$handle_result.invoke(service.clj:826)
	at clj_backend.modules.script.service$class_def.invokeStatic(service.clj:873)
	at clj_backend.modules.script.service$class_def.invoke(service.clj:853)
	at clj_backend.modules.script.service$class_def.invokeStatic(service.clj:859)
	at clj_backend.modules.script.service$class_def.invoke(service.clj:853)
	at clj_backend.modules.project.project_service$open_project$fn__47969$fn__47970.invoke(project_service.clj:341)
	at next.jdbc.transaction$transact_STAR_.invokeStatic(transaction.clj:72)
	at next.jdbc.transaction$transact_STAR_.invoke(transaction.clj:51)
	at next.jdbc.transaction$eval4714$fn__4715.invoke(transaction.clj:121)
	at next.jdbc.protocols$eval3706$fn__3707$G__3697__3716.invoke(protocols.clj:57)
	at next.jdbc$transact.invokeStatic(jdbc.clj:381)
	at next.jdbc$transact.invoke(jdbc.clj:373)
	at clj_backend.modules.project.project_service$open_project$fn__47969.invoke(project_service.clj:339)
	at next.jdbc.transaction$transact_STAR_.invokeStatic(transaction.clj:72)
	at next.jdbc.transaction$transact_STAR_.invoke(transaction.clj:51)
	at next.jdbc.transaction$eval4714$fn__4715.invoke(transaction.clj:121)
	at next.jdbc.protocols$eval3706$fn__3707$G__3697__3716.invoke(protocols.clj:57)
	at next.jdbc$transact.invokeStatic(jdbc.clj:381)
	at next.jdbc$transact.invoke(jdbc.clj:373)
	at clj_backend.modules.project.project_service$open_project.invokeStatic(project_service.clj:336)
	at clj_backend.modules.project.project_service$open_project.invoke(project_service.clj:328)
	at clj_backend.modules.project.project_routes$routes$fn__48144.invoke(project_routes.clj:72)
	at clj_backend.common.trial$trial_middleware$fn__42635.invoke(trial.clj:73)
	at reitit.ring.coercion$fn__52489$fn__52491$fn__52492.invoke(coercion.cljc:40)
	at reitit.ring.coercion$fn__52512$fn__52514$fn__52515.invoke(coercion.cljc:80)
	at reitit.ring.middleware.exception$wrap$fn__49468$fn__49469.invoke(exception.clj:49)
	at clj_backend.middleware.logger$logger_middleware$fn__52707.invoke(logger.clj:18)
	at muuntaja.middleware$wrap_format_request$fn__52630.invoke(middleware.clj:114)
	at muuntaja.middleware$wrap_format_response$fn__52634.invoke(middleware.clj:132)
	at muuntaja.middleware$wrap_format_negotiate$fn__52627.invoke(middleware.clj:96)
	at ring.middleware.params$wrap_params$fn__18645.invoke(params.clj:67)
	at reitit.ring$ring_handler$fn__12807.invoke(ring.cljc:329)
	at clojure.lang.AFn.applyToHelper(AFn.java:154)
	at clojure.lang.AFn.applyTo(AFn.java:144)
	at clojure.lang.AFunction$1.doInvoke(AFunction.java:33)
	at clojure.lang.RestFn.invoke(RestFn.java:411)
	at clojure.lang.Var.invoke(Var.java:386)
	at ring.middleware.reload$wrap_reload$fn__16585.invoke(reload.clj:39)
	at selmer.middleware$wrap_error_page$fn__16600.invoke(middleware.clj:18)
	at prone.middleware$wrap_exceptions$fn__16842.invoke(middleware.clj:169)
	at ring.middleware.flash$wrap_flash$fn__16963.invoke(flash.clj:39)
	at ring.middleware.session$wrap_session$fn__18174.invoke(session.clj:108)
	at ring.middleware.cors$handle_cors.invokeStatic(cors.cljc:175)
	at ring.middleware.cors$handle_cors.invoke(cors.cljc:167)
	at ring.middleware.cors$wrap_cors$fn__16942.invoke(cors.cljc:205)
	at ring.middleware.keyword_params$wrap_keyword_params$fn__18265.invoke(keyword_params.clj:53)
	at ring.middleware.nested_params$wrap_nested_params$fn__18323.invoke(nested_params.clj:89)
	at ring.middleware.multipart_params$wrap_multipart_params$fn__18621.invoke(multipart_params.clj:173)
	at ring.middleware.params$wrap_params$fn__18645.invoke(params.clj:67)
	at ring.middleware.cookies$wrap_cookies$fn__18053.invoke(cookies.clj:175)
	at ring.middleware.absolute_redirects$wrap_absolute_redirects$fn__18816.invoke(absolute_redirects.clj:47)
	at ring.middleware.resource$wrap_resource_prefer_resources$fn__18681.invoke(resource.clj:25)
	at ring.middleware.content_type$wrap_content_type$fn__18764.invoke(content_type.clj:34)
	at ring.middleware.default_charset$wrap_default_charset$fn__18788.invoke(default_charset.clj:31)
	at ring.middleware.not_modified$wrap_not_modified$fn__18745.invoke(not_modified.clj:61)
	at ring.middleware.x_headers$wrap_x_header$fn__18205.invoke(x_headers.clj:22)
	at ring.middleware.x_headers$wrap_x_header$fn__18205.invoke(x_headers.clj:22)
	at ring.middleware.x_headers$wrap_x_header$fn__18205.invoke(x_headers.clj:22)
	at ring.adapter.jetty9$proxy_handler$fn__58690.invoke(jetty9.clj:75)
	at ring.adapter.jetty9.proxy$org.eclipse.jetty.servlet.ServletHandler$ff19274a.doHandle(Unknown Source)
	at org.eclipse.jetty.server.handler.ScopedHandler.nextHandle(ScopedHandler.java:221)
	at org.eclipse.jetty.server.handler.ContextHandler.doHandle(ContextHandler.java:1378)
	at org.eclipse.jetty.server.handler.ScopedHandler.nextScope(ScopedHandler.java:176)
	at org.eclipse.jetty.servlet.ServletHandler.doScope(ServletHandler.java:463)
	at ring.adapter.jetty9.proxy$org.eclipse.jetty.servlet.ServletHandler$ff19274a.doScope(Unknown Source)
	at org.eclipse.jetty.server.handler.ScopedHandler.nextScope(ScopedHandler.java:174)
	at org.eclipse.jetty.server.handler.ContextHandler.doScope(ContextHandler.java:1300)
	at org.eclipse.jetty.server.handler.ScopedHandler.handle(ScopedHandler.java:129)
	at org.eclipse.jetty.server.handler.HandlerWrapper.handle(HandlerWrapper.java:122)
	at org.eclipse.jetty.server.Server.handle(Server.java:562)
	at org.eclipse.jetty.server.HttpChannel.lambda$handle$0(HttpChannel.java:418)
	at org.eclipse.jetty.server.HttpChannel.dispatch(HttpChannel.java:675)
	at org.eclipse.jetty.server.HttpChannel.handle(HttpChannel.java:410)
	at org.eclipse.jetty.server.HttpConnection.onFillable(HttpConnection.java:282)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:319)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:100)
	at org.eclipse.jetty.io.SocketChannelEndPoint$1.run(SocketChannelEndPoint.java:101)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.runTask(AdaptiveExecutionStrategy.java:412)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.consumeTask(AdaptiveExecutionStrategy.java:381)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.tryProduce(AdaptiveExecutionStrategy.java:268)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.lambda$new$0(AdaptiveExecutionStrategy.java:138)
	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:407)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:894)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1038)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-09-10 14:02:12,373 [qtp1639139601-284] ERROR clj-backend.middleware.exception - 服务器错误 
clojure.lang.ExceptionInfo: 服务器错误
	at clj_backend.common.biz_error$backend_throw.invokeStatic(biz_error.clj:15)
	at clj_backend.common.biz_error$backend_throw.invoke(biz_error.clj:11)
	at clj_backend.common.biz_error$throw_error.invokeStatic(biz_error.clj:59)
	at clj_backend.common.biz_error$throw_error.invoke(biz_error.clj:32)
	at clj_backend.modules.script.service$handle_result.invokeStatic(service.clj:831)
	at clj_backend.modules.script.service$handle_result.invoke(service.clj:826)
	at clj_backend.modules.script.service$class_def.invokeStatic(service.clj:873)
	at clj_backend.modules.script.service$class_def.invoke(service.clj:853)
	at clj_backend.modules.script.service$class_def.invokeStatic(service.clj:859)
	at clj_backend.modules.script.service$class_def.invoke(service.clj:853)
	at clj_backend.modules.project.project_service$open_project$fn__47969$fn__47970.invoke(project_service.clj:341)
	at next.jdbc.transaction$transact_STAR_.invokeStatic(transaction.clj:72)
	at next.jdbc.transaction$transact_STAR_.invoke(transaction.clj:51)
	at next.jdbc.transaction$eval4714$fn__4715.invoke(transaction.clj:121)
	at next.jdbc.protocols$eval3706$fn__3707$G__3697__3716.invoke(protocols.clj:57)
	at next.jdbc$transact.invokeStatic(jdbc.clj:381)
	at next.jdbc$transact.invoke(jdbc.clj:373)
	at clj_backend.modules.project.project_service$open_project$fn__47969.invoke(project_service.clj:339)
	at next.jdbc.transaction$transact_STAR_.invokeStatic(transaction.clj:72)
	at next.jdbc.transaction$transact_STAR_.invoke(transaction.clj:51)
	at next.jdbc.transaction$eval4714$fn__4715.invoke(transaction.clj:121)
	at next.jdbc.protocols$eval3706$fn__3707$G__3697__3716.invoke(protocols.clj:57)
	at next.jdbc$transact.invokeStatic(jdbc.clj:381)
	at next.jdbc$transact.invoke(jdbc.clj:373)
	at clj_backend.modules.project.project_service$open_project.invokeStatic(project_service.clj:336)
	at clj_backend.modules.project.project_service$open_project.invoke(project_service.clj:328)
	at clj_backend.modules.project.project_routes$routes$fn__48144.invoke(project_routes.clj:72)
	at clj_backend.common.trial$trial_middleware$fn__42635.invoke(trial.clj:73)
	at reitit.ring.coercion$fn__52489$fn__52491$fn__52492.invoke(coercion.cljc:40)
	at reitit.ring.coercion$fn__52512$fn__52514$fn__52515.invoke(coercion.cljc:80)
	at reitit.ring.middleware.exception$wrap$fn__49468$fn__49469.invoke(exception.clj:49)
	at clj_backend.middleware.logger$logger_middleware$fn__52707.invoke(logger.clj:18)
	at muuntaja.middleware$wrap_format_request$fn__52630.invoke(middleware.clj:114)
	at muuntaja.middleware$wrap_format_response$fn__52634.invoke(middleware.clj:132)
	at muuntaja.middleware$wrap_format_negotiate$fn__52627.invoke(middleware.clj:96)
	at ring.middleware.params$wrap_params$fn__18645.invoke(params.clj:67)
	at reitit.ring$ring_handler$fn__12807.invoke(ring.cljc:329)
	at clojure.lang.AFn.applyToHelper(AFn.java:154)
	at clojure.lang.AFn.applyTo(AFn.java:144)
	at clojure.lang.AFunction$1.doInvoke(AFunction.java:33)
	at clojure.lang.RestFn.invoke(RestFn.java:411)
	at clojure.lang.Var.invoke(Var.java:386)
	at ring.middleware.reload$wrap_reload$fn__16585.invoke(reload.clj:39)
	at selmer.middleware$wrap_error_page$fn__16600.invoke(middleware.clj:18)
	at prone.middleware$wrap_exceptions$fn__16842.invoke(middleware.clj:169)
	at ring.middleware.flash$wrap_flash$fn__16963.invoke(flash.clj:39)
	at ring.middleware.session$wrap_session$fn__18174.invoke(session.clj:108)
	at ring.middleware.cors$handle_cors.invokeStatic(cors.cljc:175)
	at ring.middleware.cors$handle_cors.invoke(cors.cljc:167)
	at ring.middleware.cors$wrap_cors$fn__16942.invoke(cors.cljc:205)
	at ring.middleware.keyword_params$wrap_keyword_params$fn__18265.invoke(keyword_params.clj:53)
	at ring.middleware.nested_params$wrap_nested_params$fn__18323.invoke(nested_params.clj:89)
	at ring.middleware.multipart_params$wrap_multipart_params$fn__18621.invoke(multipart_params.clj:173)
	at ring.middleware.params$wrap_params$fn__18645.invoke(params.clj:67)
	at ring.middleware.cookies$wrap_cookies$fn__18053.invoke(cookies.clj:175)
	at ring.middleware.absolute_redirects$wrap_absolute_redirects$fn__18816.invoke(absolute_redirects.clj:47)
	at ring.middleware.resource$wrap_resource_prefer_resources$fn__18681.invoke(resource.clj:25)
	at ring.middleware.content_type$wrap_content_type$fn__18764.invoke(content_type.clj:34)
	at ring.middleware.default_charset$wrap_default_charset$fn__18788.invoke(default_charset.clj:31)
	at ring.middleware.not_modified$wrap_not_modified$fn__18745.invoke(not_modified.clj:61)
	at ring.middleware.x_headers$wrap_x_header$fn__18205.invoke(x_headers.clj:22)
	at ring.middleware.x_headers$wrap_x_header$fn__18205.invoke(x_headers.clj:22)
	at ring.middleware.x_headers$wrap_x_header$fn__18205.invoke(x_headers.clj:22)
	at ring.adapter.jetty9$proxy_handler$fn__58690.invoke(jetty9.clj:75)
	at ring.adapter.jetty9.proxy$org.eclipse.jetty.servlet.ServletHandler$ff19274a.doHandle(Unknown Source)
	at org.eclipse.jetty.server.handler.ScopedHandler.nextHandle(ScopedHandler.java:221)
	at org.eclipse.jetty.server.handler.ContextHandler.doHandle(ContextHandler.java:1378)
	at org.eclipse.jetty.server.handler.ScopedHandler.nextScope(ScopedHandler.java:176)
	at org.eclipse.jetty.servlet.ServletHandler.doScope(ServletHandler.java:463)
	at ring.adapter.jetty9.proxy$org.eclipse.jetty.servlet.ServletHandler$ff19274a.doScope(Unknown Source)
	at org.eclipse.jetty.server.handler.ScopedHandler.nextScope(ScopedHandler.java:174)
	at org.eclipse.jetty.server.handler.ContextHandler.doScope(ContextHandler.java:1300)
	at org.eclipse.jetty.server.handler.ScopedHandler.handle(ScopedHandler.java:129)
	at org.eclipse.jetty.server.handler.HandlerWrapper.handle(HandlerWrapper.java:122)
	at org.eclipse.jetty.server.Server.handle(Server.java:562)
	at org.eclipse.jetty.server.HttpChannel.lambda$handle$0(HttpChannel.java:418)
	at org.eclipse.jetty.server.HttpChannel.dispatch(HttpChannel.java:675)
	at org.eclipse.jetty.server.HttpChannel.handle(HttpChannel.java:410)
	at org.eclipse.jetty.server.HttpConnection.onFillable(HttpConnection.java:282)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:319)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:100)
	at org.eclipse.jetty.io.SocketChannelEndPoint$1.run(SocketChannelEndPoint.java:101)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.runTask(AdaptiveExecutionStrategy.java:412)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.consumeTask(AdaptiveExecutionStrategy.java:381)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.tryProduce(AdaptiveExecutionStrategy.java:268)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.lambda$new$0(AdaptiveExecutionStrategy.java:138)
	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:407)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:894)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1038)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-09-10 14:02:57,304 [qtp1639139601-325] ERROR clj-backend.middleware.exception - 服务器错误 
clojure.lang.ExceptionInfo: 服务器错误
	at clj_backend.common.biz_error$backend_throw.invokeStatic(biz_error.clj:15)
	at clj_backend.common.biz_error$backend_throw.invoke(biz_error.clj:11)
	at clj_backend.common.biz_error$throw_error.invokeStatic(biz_error.clj:59)
	at clj_backend.common.biz_error$throw_error.invoke(biz_error.clj:32)
	at clj_backend.modules.script.service$handle_result.invokeStatic(service.clj:831)
	at clj_backend.modules.script.service$handle_result.invoke(service.clj:826)
	at clj_backend.modules.script.service$class_def.invokeStatic(service.clj:873)
	at clj_backend.modules.script.service$class_def.invoke(service.clj:853)
	at clj_backend.modules.script.service$class_def.invokeStatic(service.clj:859)
	at clj_backend.modules.script.service$class_def.invoke(service.clj:853)
	at clj_backend.modules.project.project_service$open_project$fn__47969$fn__47970.invoke(project_service.clj:341)
	at next.jdbc.transaction$transact_STAR_.invokeStatic(transaction.clj:72)
	at next.jdbc.transaction$transact_STAR_.invoke(transaction.clj:51)
	at next.jdbc.transaction$eval4714$fn__4715.invoke(transaction.clj:121)
	at next.jdbc.protocols$eval3706$fn__3707$G__3697__3716.invoke(protocols.clj:57)
	at next.jdbc$transact.invokeStatic(jdbc.clj:381)
	at next.jdbc$transact.invoke(jdbc.clj:373)
	at clj_backend.modules.project.project_service$open_project$fn__47969.invoke(project_service.clj:339)
	at next.jdbc.transaction$transact_STAR_.invokeStatic(transaction.clj:72)
	at next.jdbc.transaction$transact_STAR_.invoke(transaction.clj:51)
	at next.jdbc.transaction$eval4714$fn__4715.invoke(transaction.clj:121)
	at next.jdbc.protocols$eval3706$fn__3707$G__3697__3716.invoke(protocols.clj:57)
	at next.jdbc$transact.invokeStatic(jdbc.clj:381)
	at next.jdbc$transact.invoke(jdbc.clj:373)
	at clj_backend.modules.project.project_service$open_project.invokeStatic(project_service.clj:336)
	at clj_backend.modules.project.project_service$open_project.invoke(project_service.clj:328)
	at clj_backend.modules.project.project_routes$routes$fn__48144.invoke(project_routes.clj:72)
	at clj_backend.common.trial$trial_middleware$fn__42635.invoke(trial.clj:73)
	at reitit.ring.coercion$fn__52489$fn__52491$fn__52492.invoke(coercion.cljc:40)
	at reitit.ring.coercion$fn__52512$fn__52514$fn__52515.invoke(coercion.cljc:80)
	at reitit.ring.middleware.exception$wrap$fn__49468$fn__49469.invoke(exception.clj:49)
	at clj_backend.middleware.logger$logger_middleware$fn__52707.invoke(logger.clj:18)
	at muuntaja.middleware$wrap_format_request$fn__52630.invoke(middleware.clj:114)
	at muuntaja.middleware$wrap_format_response$fn__52634.invoke(middleware.clj:132)
	at muuntaja.middleware$wrap_format_negotiate$fn__52627.invoke(middleware.clj:96)
	at ring.middleware.params$wrap_params$fn__18645.invoke(params.clj:67)
	at reitit.ring$ring_handler$fn__12807.invoke(ring.cljc:329)
	at clojure.lang.AFn.applyToHelper(AFn.java:154)
	at clojure.lang.AFn.applyTo(AFn.java:144)
	at clojure.lang.AFunction$1.doInvoke(AFunction.java:33)
	at clojure.lang.RestFn.invoke(RestFn.java:411)
	at clojure.lang.Var.invoke(Var.java:386)
	at ring.middleware.reload$wrap_reload$fn__16585.invoke(reload.clj:39)
	at selmer.middleware$wrap_error_page$fn__16600.invoke(middleware.clj:18)
	at prone.middleware$wrap_exceptions$fn__16842.invoke(middleware.clj:169)
	at ring.middleware.flash$wrap_flash$fn__16963.invoke(flash.clj:39)
	at ring.middleware.session$wrap_session$fn__18174.invoke(session.clj:108)
	at ring.middleware.cors$handle_cors.invokeStatic(cors.cljc:175)
	at ring.middleware.cors$handle_cors.invoke(cors.cljc:167)
	at ring.middleware.cors$wrap_cors$fn__16942.invoke(cors.cljc:205)
	at ring.middleware.keyword_params$wrap_keyword_params$fn__18265.invoke(keyword_params.clj:53)
	at ring.middleware.nested_params$wrap_nested_params$fn__18323.invoke(nested_params.clj:89)
	at ring.middleware.multipart_params$wrap_multipart_params$fn__18621.invoke(multipart_params.clj:173)
	at ring.middleware.params$wrap_params$fn__18645.invoke(params.clj:67)
	at ring.middleware.cookies$wrap_cookies$fn__18053.invoke(cookies.clj:175)
	at ring.middleware.absolute_redirects$wrap_absolute_redirects$fn__18816.invoke(absolute_redirects.clj:47)
	at ring.middleware.resource$wrap_resource_prefer_resources$fn__18681.invoke(resource.clj:25)
	at ring.middleware.content_type$wrap_content_type$fn__18764.invoke(content_type.clj:34)
	at ring.middleware.default_charset$wrap_default_charset$fn__18788.invoke(default_charset.clj:31)
	at ring.middleware.not_modified$wrap_not_modified$fn__18745.invoke(not_modified.clj:61)
	at ring.middleware.x_headers$wrap_x_header$fn__18205.invoke(x_headers.clj:22)
	at ring.middleware.x_headers$wrap_x_header$fn__18205.invoke(x_headers.clj:22)
	at ring.middleware.x_headers$wrap_x_header$fn__18205.invoke(x_headers.clj:22)
	at ring.adapter.jetty9$proxy_handler$fn__58690.invoke(jetty9.clj:75)
	at ring.adapter.jetty9.proxy$org.eclipse.jetty.servlet.ServletHandler$ff19274a.doHandle(Unknown Source)
	at org.eclipse.jetty.server.handler.ScopedHandler.nextHandle(ScopedHandler.java:221)
	at org.eclipse.jetty.server.handler.ContextHandler.doHandle(ContextHandler.java:1378)
	at org.eclipse.jetty.server.handler.ScopedHandler.nextScope(ScopedHandler.java:176)
	at org.eclipse.jetty.servlet.ServletHandler.doScope(ServletHandler.java:463)
	at ring.adapter.jetty9.proxy$org.eclipse.jetty.servlet.ServletHandler$ff19274a.doScope(Unknown Source)
	at org.eclipse.jetty.server.handler.ScopedHandler.nextScope(ScopedHandler.java:174)
	at org.eclipse.jetty.server.handler.ContextHandler.doScope(ContextHandler.java:1300)
	at org.eclipse.jetty.server.handler.ScopedHandler.handle(ScopedHandler.java:129)
	at org.eclipse.jetty.server.handler.HandlerWrapper.handle(HandlerWrapper.java:122)
	at org.eclipse.jetty.server.Server.handle(Server.java:562)
	at org.eclipse.jetty.server.HttpChannel.lambda$handle$0(HttpChannel.java:418)
	at org.eclipse.jetty.server.HttpChannel.dispatch(HttpChannel.java:675)
	at org.eclipse.jetty.server.HttpChannel.handle(HttpChannel.java:410)
	at org.eclipse.jetty.server.HttpConnection.onFillable(HttpConnection.java:282)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:319)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:100)
	at org.eclipse.jetty.io.SocketChannelEndPoint$1.run(SocketChannelEndPoint.java:101)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.runTask(AdaptiveExecutionStrategy.java:412)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.consumeTask(AdaptiveExecutionStrategy.java:381)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.tryProduce(AdaptiveExecutionStrategy.java:268)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.lambda$new$0(AdaptiveExecutionStrategy.java:138)
	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:407)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:894)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1038)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-09-10 14:03:27,016 [qtp1639139601-327] ERROR clj-backend.middleware.exception - 服务器错误 
clojure.lang.ExceptionInfo: 服务器错误
	at clj_backend.common.biz_error$backend_throw.invokeStatic(biz_error.clj:15)
	at clj_backend.common.biz_error$backend_throw.invoke(biz_error.clj:11)
	at clj_backend.common.biz_error$throw_error.invokeStatic(biz_error.clj:59)
	at clj_backend.common.biz_error$throw_error.invoke(biz_error.clj:32)
	at clj_backend.modules.script.service$handle_result.invokeStatic(service.clj:831)
	at clj_backend.modules.script.service$handle_result.invoke(service.clj:826)
	at clj_backend.modules.script.service$class_def.invokeStatic(service.clj:873)
	at clj_backend.modules.script.service$class_def.invoke(service.clj:853)
	at clj_backend.modules.script.service$class_def.invokeStatic(service.clj:859)
	at clj_backend.modules.script.service$class_def.invoke(service.clj:853)
	at clj_backend.modules.project.project_service$open_project$fn__47969$fn__47970.invoke(project_service.clj:341)
	at next.jdbc.transaction$transact_STAR_.invokeStatic(transaction.clj:72)
	at next.jdbc.transaction$transact_STAR_.invoke(transaction.clj:51)
	at next.jdbc.transaction$eval4714$fn__4715.invoke(transaction.clj:121)
	at next.jdbc.protocols$eval3706$fn__3707$G__3697__3716.invoke(protocols.clj:57)
	at next.jdbc$transact.invokeStatic(jdbc.clj:381)
	at next.jdbc$transact.invoke(jdbc.clj:373)
	at clj_backend.modules.project.project_service$open_project$fn__47969.invoke(project_service.clj:339)
	at next.jdbc.transaction$transact_STAR_.invokeStatic(transaction.clj:72)
	at next.jdbc.transaction$transact_STAR_.invoke(transaction.clj:51)
	at next.jdbc.transaction$eval4714$fn__4715.invoke(transaction.clj:121)
	at next.jdbc.protocols$eval3706$fn__3707$G__3697__3716.invoke(protocols.clj:57)
	at next.jdbc$transact.invokeStatic(jdbc.clj:381)
	at next.jdbc$transact.invoke(jdbc.clj:373)
	at clj_backend.modules.project.project_service$open_project.invokeStatic(project_service.clj:336)
	at clj_backend.modules.project.project_service$open_project.invoke(project_service.clj:328)
	at clj_backend.modules.project.project_routes$routes$fn__48144.invoke(project_routes.clj:72)
	at clj_backend.common.trial$trial_middleware$fn__42635.invoke(trial.clj:73)
	at reitit.ring.coercion$fn__52489$fn__52491$fn__52492.invoke(coercion.cljc:40)
	at reitit.ring.coercion$fn__52512$fn__52514$fn__52515.invoke(coercion.cljc:80)
	at reitit.ring.middleware.exception$wrap$fn__49468$fn__49469.invoke(exception.clj:49)
	at clj_backend.middleware.logger$logger_middleware$fn__52707.invoke(logger.clj:18)
	at muuntaja.middleware$wrap_format_request$fn__52630.invoke(middleware.clj:114)
	at muuntaja.middleware$wrap_format_response$fn__52634.invoke(middleware.clj:132)
	at muuntaja.middleware$wrap_format_negotiate$fn__52627.invoke(middleware.clj:96)
	at ring.middleware.params$wrap_params$fn__18645.invoke(params.clj:67)
	at reitit.ring$ring_handler$fn__12807.invoke(ring.cljc:329)
	at clojure.lang.AFn.applyToHelper(AFn.java:154)
	at clojure.lang.AFn.applyTo(AFn.java:144)
	at clojure.lang.AFunction$1.doInvoke(AFunction.java:33)
	at clojure.lang.RestFn.invoke(RestFn.java:411)
	at clojure.lang.Var.invoke(Var.java:386)
	at ring.middleware.reload$wrap_reload$fn__16585.invoke(reload.clj:39)
	at selmer.middleware$wrap_error_page$fn__16600.invoke(middleware.clj:18)
	at prone.middleware$wrap_exceptions$fn__16842.invoke(middleware.clj:169)
	at ring.middleware.flash$wrap_flash$fn__16963.invoke(flash.clj:39)
	at ring.middleware.session$wrap_session$fn__18174.invoke(session.clj:108)
	at ring.middleware.cors$handle_cors.invokeStatic(cors.cljc:175)
	at ring.middleware.cors$handle_cors.invoke(cors.cljc:167)
	at ring.middleware.cors$wrap_cors$fn__16942.invoke(cors.cljc:205)
	at ring.middleware.keyword_params$wrap_keyword_params$fn__18265.invoke(keyword_params.clj:53)
	at ring.middleware.nested_params$wrap_nested_params$fn__18323.invoke(nested_params.clj:89)
	at ring.middleware.multipart_params$wrap_multipart_params$fn__18621.invoke(multipart_params.clj:173)
	at ring.middleware.params$wrap_params$fn__18645.invoke(params.clj:67)
	at ring.middleware.cookies$wrap_cookies$fn__18053.invoke(cookies.clj:175)
	at ring.middleware.absolute_redirects$wrap_absolute_redirects$fn__18816.invoke(absolute_redirects.clj:47)
	at ring.middleware.resource$wrap_resource_prefer_resources$fn__18681.invoke(resource.clj:25)
	at ring.middleware.content_type$wrap_content_type$fn__18764.invoke(content_type.clj:34)
	at ring.middleware.default_charset$wrap_default_charset$fn__18788.invoke(default_charset.clj:31)
	at ring.middleware.not_modified$wrap_not_modified$fn__18745.invoke(not_modified.clj:61)
	at ring.middleware.x_headers$wrap_x_header$fn__18205.invoke(x_headers.clj:22)
	at ring.middleware.x_headers$wrap_x_header$fn__18205.invoke(x_headers.clj:22)
	at ring.middleware.x_headers$wrap_x_header$fn__18205.invoke(x_headers.clj:22)
	at ring.adapter.jetty9$proxy_handler$fn__58690.invoke(jetty9.clj:75)
	at ring.adapter.jetty9.proxy$org.eclipse.jetty.servlet.ServletHandler$ff19274a.doHandle(Unknown Source)
	at org.eclipse.jetty.server.handler.ScopedHandler.nextHandle(ScopedHandler.java:221)
	at org.eclipse.jetty.server.handler.ContextHandler.doHandle(ContextHandler.java:1378)
	at org.eclipse.jetty.server.handler.ScopedHandler.nextScope(ScopedHandler.java:176)
	at org.eclipse.jetty.servlet.ServletHandler.doScope(ServletHandler.java:463)
	at ring.adapter.jetty9.proxy$org.eclipse.jetty.servlet.ServletHandler$ff19274a.doScope(Unknown Source)
	at org.eclipse.jetty.server.handler.ScopedHandler.nextScope(ScopedHandler.java:174)
	at org.eclipse.jetty.server.handler.ContextHandler.doScope(ContextHandler.java:1300)
	at org.eclipse.jetty.server.handler.ScopedHandler.handle(ScopedHandler.java:129)
	at org.eclipse.jetty.server.handler.HandlerWrapper.handle(HandlerWrapper.java:122)
	at org.eclipse.jetty.server.Server.handle(Server.java:562)
	at org.eclipse.jetty.server.HttpChannel.lambda$handle$0(HttpChannel.java:418)
	at org.eclipse.jetty.server.HttpChannel.dispatch(HttpChannel.java:675)
	at org.eclipse.jetty.server.HttpChannel.handle(HttpChannel.java:410)
	at org.eclipse.jetty.server.HttpConnection.onFillable(HttpConnection.java:282)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:319)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:100)
	at org.eclipse.jetty.io.SocketChannelEndPoint$1.run(SocketChannelEndPoint.java:101)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.runTask(AdaptiveExecutionStrategy.java:412)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.consumeTask(AdaptiveExecutionStrategy.java:381)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.tryProduce(AdaptiveExecutionStrategy.java:268)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.lambda$new$0(AdaptiveExecutionStrategy.java:138)
	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:407)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:894)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1038)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-09-10 14:03:49,216 [qtp1639139601-326] ERROR clj-backend.middleware.exception - 服务器错误 
clojure.lang.ExceptionInfo: 服务器错误
	at clj_backend.common.biz_error$backend_throw.invokeStatic(biz_error.clj:15)
	at clj_backend.common.biz_error$backend_throw.invoke(biz_error.clj:11)
	at clj_backend.common.biz_error$throw_error.invokeStatic(biz_error.clj:59)
	at clj_backend.common.biz_error$throw_error.invoke(biz_error.clj:32)
	at clj_backend.modules.script.service$handle_result.invokeStatic(service.clj:831)
	at clj_backend.modules.script.service$handle_result.invoke(service.clj:826)
	at clj_backend.modules.script.service$class_def.invokeStatic(service.clj:873)
	at clj_backend.modules.script.service$class_def.invoke(service.clj:853)
	at clj_backend.modules.script.service$class_def.invokeStatic(service.clj:859)
	at clj_backend.modules.script.service$class_def.invoke(service.clj:853)
	at clj_backend.modules.project.project_service$open_project$fn__47969$fn__47970.invoke(project_service.clj:341)
	at next.jdbc.transaction$transact_STAR_.invokeStatic(transaction.clj:72)
	at next.jdbc.transaction$transact_STAR_.invoke(transaction.clj:51)
	at next.jdbc.transaction$eval4714$fn__4715.invoke(transaction.clj:121)
	at next.jdbc.protocols$eval3706$fn__3707$G__3697__3716.invoke(protocols.clj:57)
	at next.jdbc$transact.invokeStatic(jdbc.clj:381)
	at next.jdbc$transact.invoke(jdbc.clj:373)
	at clj_backend.modules.project.project_service$open_project$fn__47969.invoke(project_service.clj:339)
	at next.jdbc.transaction$transact_STAR_.invokeStatic(transaction.clj:72)
	at next.jdbc.transaction$transact_STAR_.invoke(transaction.clj:51)
	at next.jdbc.transaction$eval4714$fn__4715.invoke(transaction.clj:121)
	at next.jdbc.protocols$eval3706$fn__3707$G__3697__3716.invoke(protocols.clj:57)
	at next.jdbc$transact.invokeStatic(jdbc.clj:381)
	at next.jdbc$transact.invoke(jdbc.clj:373)
	at clj_backend.modules.project.project_service$open_project.invokeStatic(project_service.clj:336)
	at clj_backend.modules.project.project_service$open_project.invoke(project_service.clj:328)
	at clj_backend.modules.project.project_routes$routes$fn__48144.invoke(project_routes.clj:72)
	at clj_backend.common.trial$trial_middleware$fn__42635.invoke(trial.clj:73)
	at reitit.ring.coercion$fn__52489$fn__52491$fn__52492.invoke(coercion.cljc:40)
	at reitit.ring.coercion$fn__52512$fn__52514$fn__52515.invoke(coercion.cljc:80)
	at reitit.ring.middleware.exception$wrap$fn__49468$fn__49469.invoke(exception.clj:49)
	at clj_backend.middleware.logger$logger_middleware$fn__52707.invoke(logger.clj:18)
	at muuntaja.middleware$wrap_format_request$fn__52630.invoke(middleware.clj:114)
	at muuntaja.middleware$wrap_format_response$fn__52634.invoke(middleware.clj:132)
	at muuntaja.middleware$wrap_format_negotiate$fn__52627.invoke(middleware.clj:96)
	at ring.middleware.params$wrap_params$fn__18645.invoke(params.clj:67)
	at reitit.ring$ring_handler$fn__12807.invoke(ring.cljc:329)
	at clojure.lang.AFn.applyToHelper(AFn.java:154)
	at clojure.lang.AFn.applyTo(AFn.java:144)
	at clojure.lang.AFunction$1.doInvoke(AFunction.java:33)
	at clojure.lang.RestFn.invoke(RestFn.java:411)
	at clojure.lang.Var.invoke(Var.java:386)
	at ring.middleware.reload$wrap_reload$fn__16585.invoke(reload.clj:39)
	at selmer.middleware$wrap_error_page$fn__16600.invoke(middleware.clj:18)
	at prone.middleware$wrap_exceptions$fn__16842.invoke(middleware.clj:169)
	at ring.middleware.flash$wrap_flash$fn__16963.invoke(flash.clj:39)
	at ring.middleware.session$wrap_session$fn__18174.invoke(session.clj:108)
	at ring.middleware.cors$handle_cors.invokeStatic(cors.cljc:175)
	at ring.middleware.cors$handle_cors.invoke(cors.cljc:167)
	at ring.middleware.cors$wrap_cors$fn__16942.invoke(cors.cljc:205)
	at ring.middleware.keyword_params$wrap_keyword_params$fn__18265.invoke(keyword_params.clj:53)
	at ring.middleware.nested_params$wrap_nested_params$fn__18323.invoke(nested_params.clj:89)
	at ring.middleware.multipart_params$wrap_multipart_params$fn__18621.invoke(multipart_params.clj:173)
	at ring.middleware.params$wrap_params$fn__18645.invoke(params.clj:67)
	at ring.middleware.cookies$wrap_cookies$fn__18053.invoke(cookies.clj:175)
	at ring.middleware.absolute_redirects$wrap_absolute_redirects$fn__18816.invoke(absolute_redirects.clj:47)
	at ring.middleware.resource$wrap_resource_prefer_resources$fn__18681.invoke(resource.clj:25)
	at ring.middleware.content_type$wrap_content_type$fn__18764.invoke(content_type.clj:34)
	at ring.middleware.default_charset$wrap_default_charset$fn__18788.invoke(default_charset.clj:31)
	at ring.middleware.not_modified$wrap_not_modified$fn__18745.invoke(not_modified.clj:61)
	at ring.middleware.x_headers$wrap_x_header$fn__18205.invoke(x_headers.clj:22)
	at ring.middleware.x_headers$wrap_x_header$fn__18205.invoke(x_headers.clj:22)
	at ring.middleware.x_headers$wrap_x_header$fn__18205.invoke(x_headers.clj:22)
	at ring.adapter.jetty9$proxy_handler$fn__58690.invoke(jetty9.clj:75)
	at ring.adapter.jetty9.proxy$org.eclipse.jetty.servlet.ServletHandler$ff19274a.doHandle(Unknown Source)
	at org.eclipse.jetty.server.handler.ScopedHandler.nextHandle(ScopedHandler.java:221)
	at org.eclipse.jetty.server.handler.ContextHandler.doHandle(ContextHandler.java:1378)
	at org.eclipse.jetty.server.handler.ScopedHandler.nextScope(ScopedHandler.java:176)
	at org.eclipse.jetty.servlet.ServletHandler.doScope(ServletHandler.java:463)
	at ring.adapter.jetty9.proxy$org.eclipse.jetty.servlet.ServletHandler$ff19274a.doScope(Unknown Source)
	at org.eclipse.jetty.server.handler.ScopedHandler.nextScope(ScopedHandler.java:174)
	at org.eclipse.jetty.server.handler.ContextHandler.doScope(ContextHandler.java:1300)
	at org.eclipse.jetty.server.handler.ScopedHandler.handle(ScopedHandler.java:129)
	at org.eclipse.jetty.server.handler.HandlerWrapper.handle(HandlerWrapper.java:122)
	at org.eclipse.jetty.server.Server.handle(Server.java:562)
	at org.eclipse.jetty.server.HttpChannel.lambda$handle$0(HttpChannel.java:418)
	at org.eclipse.jetty.server.HttpChannel.dispatch(HttpChannel.java:675)
	at org.eclipse.jetty.server.HttpChannel.handle(HttpChannel.java:410)
	at org.eclipse.jetty.server.HttpConnection.onFillable(HttpConnection.java:282)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:319)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:100)
	at org.eclipse.jetty.io.SocketChannelEndPoint$1.run(SocketChannelEndPoint.java:101)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.runTask(AdaptiveExecutionStrategy.java:412)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.consumeTask(AdaptiveExecutionStrategy.java:381)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.tryProduce(AdaptiveExecutionStrategy.java:268)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.lambda$new$0(AdaptiveExecutionStrategy.java:138)
	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:407)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:894)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1038)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-09-10 14:04:30,312 [qtp1639139601-326] ERROR clj-backend.middleware.exception - 服务器错误 
clojure.lang.ExceptionInfo: 服务器错误
	at clj_backend.common.biz_error$backend_throw.invokeStatic(biz_error.clj:15)
	at clj_backend.common.biz_error$backend_throw.invoke(biz_error.clj:11)
	at clj_backend.common.biz_error$throw_error.invokeStatic(biz_error.clj:59)
	at clj_backend.common.biz_error$throw_error.invoke(biz_error.clj:32)
	at clj_backend.modules.script.service$handle_result.invokeStatic(service.clj:831)
	at clj_backend.modules.script.service$handle_result.invoke(service.clj:826)
	at clj_backend.modules.script.service$class_def.invokeStatic(service.clj:873)
	at clj_backend.modules.script.service$class_def.invoke(service.clj:853)
	at clj_backend.modules.script.service$class_def.invokeStatic(service.clj:859)
	at clj_backend.modules.script.service$class_def.invoke(service.clj:853)
	at clj_backend.modules.project.project_service$open_project$fn__47969$fn__47970.invoke(project_service.clj:341)
	at next.jdbc.transaction$transact_STAR_.invokeStatic(transaction.clj:72)
	at next.jdbc.transaction$transact_STAR_.invoke(transaction.clj:51)
	at next.jdbc.transaction$eval4714$fn__4715.invoke(transaction.clj:121)
	at next.jdbc.protocols$eval3706$fn__3707$G__3697__3716.invoke(protocols.clj:57)
	at next.jdbc$transact.invokeStatic(jdbc.clj:381)
	at next.jdbc$transact.invoke(jdbc.clj:373)
	at clj_backend.modules.project.project_service$open_project$fn__47969.invoke(project_service.clj:339)
	at next.jdbc.transaction$transact_STAR_.invokeStatic(transaction.clj:72)
	at next.jdbc.transaction$transact_STAR_.invoke(transaction.clj:51)
	at next.jdbc.transaction$eval4714$fn__4715.invoke(transaction.clj:121)
	at next.jdbc.protocols$eval3706$fn__3707$G__3697__3716.invoke(protocols.clj:57)
	at next.jdbc$transact.invokeStatic(jdbc.clj:381)
	at next.jdbc$transact.invoke(jdbc.clj:373)
	at clj_backend.modules.project.project_service$open_project.invokeStatic(project_service.clj:336)
	at clj_backend.modules.project.project_service$open_project.invoke(project_service.clj:328)
	at clj_backend.modules.project.project_routes$routes$fn__48144.invoke(project_routes.clj:72)
	at clj_backend.common.trial$trial_middleware$fn__42635.invoke(trial.clj:73)
	at reitit.ring.coercion$fn__52489$fn__52491$fn__52492.invoke(coercion.cljc:40)
	at reitit.ring.coercion$fn__52512$fn__52514$fn__52515.invoke(coercion.cljc:80)
	at reitit.ring.middleware.exception$wrap$fn__49468$fn__49469.invoke(exception.clj:49)
	at clj_backend.middleware.logger$logger_middleware$fn__52707.invoke(logger.clj:18)
	at muuntaja.middleware$wrap_format_request$fn__52630.invoke(middleware.clj:114)
	at muuntaja.middleware$wrap_format_response$fn__52634.invoke(middleware.clj:132)
	at muuntaja.middleware$wrap_format_negotiate$fn__52627.invoke(middleware.clj:96)
	at ring.middleware.params$wrap_params$fn__18645.invoke(params.clj:67)
	at reitit.ring$ring_handler$fn__12807.invoke(ring.cljc:329)
	at clojure.lang.AFn.applyToHelper(AFn.java:154)
	at clojure.lang.AFn.applyTo(AFn.java:144)
	at clojure.lang.AFunction$1.doInvoke(AFunction.java:33)
	at clojure.lang.RestFn.invoke(RestFn.java:411)
	at clojure.lang.Var.invoke(Var.java:386)
	at ring.middleware.reload$wrap_reload$fn__16585.invoke(reload.clj:39)
	at selmer.middleware$wrap_error_page$fn__16600.invoke(middleware.clj:18)
	at prone.middleware$wrap_exceptions$fn__16842.invoke(middleware.clj:169)
	at ring.middleware.flash$wrap_flash$fn__16963.invoke(flash.clj:39)
	at ring.middleware.session$wrap_session$fn__18174.invoke(session.clj:108)
	at ring.middleware.cors$handle_cors.invokeStatic(cors.cljc:175)
	at ring.middleware.cors$handle_cors.invoke(cors.cljc:167)
	at ring.middleware.cors$wrap_cors$fn__16942.invoke(cors.cljc:205)
	at ring.middleware.keyword_params$wrap_keyword_params$fn__18265.invoke(keyword_params.clj:53)
	at ring.middleware.nested_params$wrap_nested_params$fn__18323.invoke(nested_params.clj:89)
	at ring.middleware.multipart_params$wrap_multipart_params$fn__18621.invoke(multipart_params.clj:173)
	at ring.middleware.params$wrap_params$fn__18645.invoke(params.clj:67)
	at ring.middleware.cookies$wrap_cookies$fn__18053.invoke(cookies.clj:175)
	at ring.middleware.absolute_redirects$wrap_absolute_redirects$fn__18816.invoke(absolute_redirects.clj:47)
	at ring.middleware.resource$wrap_resource_prefer_resources$fn__18681.invoke(resource.clj:25)
	at ring.middleware.content_type$wrap_content_type$fn__18764.invoke(content_type.clj:34)
	at ring.middleware.default_charset$wrap_default_charset$fn__18788.invoke(default_charset.clj:31)
	at ring.middleware.not_modified$wrap_not_modified$fn__18745.invoke(not_modified.clj:61)
	at ring.middleware.x_headers$wrap_x_header$fn__18205.invoke(x_headers.clj:22)
	at ring.middleware.x_headers$wrap_x_header$fn__18205.invoke(x_headers.clj:22)
	at ring.middleware.x_headers$wrap_x_header$fn__18205.invoke(x_headers.clj:22)
	at ring.adapter.jetty9$proxy_handler$fn__58690.invoke(jetty9.clj:75)
	at ring.adapter.jetty9.proxy$org.eclipse.jetty.servlet.ServletHandler$ff19274a.doHandle(Unknown Source)
	at org.eclipse.jetty.server.handler.ScopedHandler.nextHandle(ScopedHandler.java:221)
	at org.eclipse.jetty.server.handler.ContextHandler.doHandle(ContextHandler.java:1378)
	at org.eclipse.jetty.server.handler.ScopedHandler.nextScope(ScopedHandler.java:176)
	at org.eclipse.jetty.servlet.ServletHandler.doScope(ServletHandler.java:463)
	at ring.adapter.jetty9.proxy$org.eclipse.jetty.servlet.ServletHandler$ff19274a.doScope(Unknown Source)
	at org.eclipse.jetty.server.handler.ScopedHandler.nextScope(ScopedHandler.java:174)
	at org.eclipse.jetty.server.handler.ContextHandler.doScope(ContextHandler.java:1300)
	at org.eclipse.jetty.server.handler.ScopedHandler.handle(ScopedHandler.java:129)
	at org.eclipse.jetty.server.handler.HandlerWrapper.handle(HandlerWrapper.java:122)
	at org.eclipse.jetty.server.Server.handle(Server.java:562)
	at org.eclipse.jetty.server.HttpChannel.lambda$handle$0(HttpChannel.java:418)
	at org.eclipse.jetty.server.HttpChannel.dispatch(HttpChannel.java:675)
	at org.eclipse.jetty.server.HttpChannel.handle(HttpChannel.java:410)
	at org.eclipse.jetty.server.HttpConnection.onFillable(HttpConnection.java:282)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:319)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:100)
	at org.eclipse.jetty.io.SocketChannelEndPoint$1.run(SocketChannelEndPoint.java:101)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.runTask(AdaptiveExecutionStrategy.java:412)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.consumeTask(AdaptiveExecutionStrategy.java:381)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.tryProduce(AdaptiveExecutionStrategy.java:268)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.lambda$new$0(AdaptiveExecutionStrategy.java:138)
	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:407)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:894)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1038)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-09-10 14:19:40,133 [async-dispatch-13] ERROR jdbc.audit - 59. PreparedStatement.execute() PRAGMA wal_checkpoint(PASSIVE) 
 
org.sqlite.SQLiteException: [SQLITE_LOCKED]  A table in the database is locked (database table is locked)
	at org.sqlite.core.DB.newSQLException(DB.java:1030)
	at org.sqlite.core.DB.newSQLException(DB.java:1042)
	at org.sqlite.core.DB.execute(DB.java:881)
	at org.sqlite.jdbc3.JDBC3PreparedStatement.execute(JDBC3PreparedStatement.java:54)
	at net.sf.log4jdbc.PreparedStatementSpy.execute(PreparedStatementSpy.java:417)
	at next.jdbc.result_set$stmt__GT_result_set.invokeStatic(result_set.clj:663)
	at next.jdbc.result_set$stmt__GT_result_set.invoke(result_set.clj:658)
	at next.jdbc.result_set$eval4526$fn__4534.invoke(result_set.clj:891)
	at next.jdbc.protocols$eval3616$fn__3617$G__3607__3626.invoke(protocols.clj:33)
	at next.jdbc$execute_BANG_.invokeStatic(jdbc.clj:250)
	at next.jdbc$execute_BANG_.invoke(jdbc.clj:237)
	at clj_backend.modules.sqlite.cache_utils$safe_wal_checkpoint$fn__47855.invoke(cache_utils.clj:15)
	at clj_backend.modules.sqlite.cache_utils$safe_wal_checkpoint.invokeStatic(cache_utils.clj:13)
	at clj_backend.modules.sqlite.cache_utils$safe_wal_checkpoint.invoke(cache_utils.clj:9)
	at clj_backend.modules.sqlite.cache_utils$flush_project_cache.invokeStatic(cache_utils.clj:49)
	at clj_backend.modules.sqlite.cache_utils$flush_project_cache.invoke(cache_utils.clj:40)
	at clj_backend.modules.project.project_service$save_project.invokeStatic(project_service.clj:414)
	at clj_backend.modules.project.project_service$save_project.invoke(project_service.clj:400)
	at clj_backend.modules.project.project_service$handle_project_event.invokeStatic(project_service.clj:633)
	at clj_backend.modules.project.project_service$handle_project_event.invoke(project_service.clj:628)
	at clj_backend.modules.project.project_service$eval48077$fn__48078$fn__48100$state_machine__29475__auto____48101$fn__48103.invoke(project_service.clj:638)
	at clj_backend.modules.project.project_service$eval48077$fn__48078$fn__48100$state_machine__29475__auto____48101.invoke(project_service.clj:638)
	at clojure.core.async.impl.runtime$run_state_machine.invokeStatic(runtime.clj:62)
	at clojure.core.async.impl.runtime$run_state_machine.invoke(runtime.clj:61)
	at clojure.core.async.impl.runtime$run_state_machine_wrapped.invokeStatic(runtime.clj:66)
	at clojure.core.async.impl.runtime$run_state_machine_wrapped.invoke(runtime.clj:64)
	at clojure.core.async.impl.runtime$take_BANG_$fn__24211.invoke(runtime.clj:75)
	at clojure.core.async.impl.channels.ManyToManyChannel$fn__24005$fn__24006.invoke(channels.clj:100)
	at clojure.lang.AFn.run(AFn.java:22)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at clojure.core.async.impl.concurrent$counted_thread_factory$reify__23874$fn__23875.invoke(concurrent.clj:29)
	at clojure.lang.AFn.run(AFn.java:22)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-09-10 14:19:40,133 [async-dispatch-13] ERROR jdbc.sqltiming - 59. PreparedStatement.execute() FAILED! PRAGMA wal_checkpoint(PASSIVE) 
 {FAILED after 8 msec} 
org.sqlite.SQLiteException: [SQLITE_LOCKED]  A table in the database is locked (database table is locked)
	at org.sqlite.core.DB.newSQLException(DB.java:1030)
	at org.sqlite.core.DB.newSQLException(DB.java:1042)
	at org.sqlite.core.DB.execute(DB.java:881)
	at org.sqlite.jdbc3.JDBC3PreparedStatement.execute(JDBC3PreparedStatement.java:54)
	at net.sf.log4jdbc.PreparedStatementSpy.execute(PreparedStatementSpy.java:417)
	at next.jdbc.result_set$stmt__GT_result_set.invokeStatic(result_set.clj:663)
	at next.jdbc.result_set$stmt__GT_result_set.invoke(result_set.clj:658)
	at next.jdbc.result_set$eval4526$fn__4534.invoke(result_set.clj:891)
	at next.jdbc.protocols$eval3616$fn__3617$G__3607__3626.invoke(protocols.clj:33)
	at next.jdbc$execute_BANG_.invokeStatic(jdbc.clj:250)
	at next.jdbc$execute_BANG_.invoke(jdbc.clj:237)
	at clj_backend.modules.sqlite.cache_utils$safe_wal_checkpoint$fn__47855.invoke(cache_utils.clj:15)
	at clj_backend.modules.sqlite.cache_utils$safe_wal_checkpoint.invokeStatic(cache_utils.clj:13)
	at clj_backend.modules.sqlite.cache_utils$safe_wal_checkpoint.invoke(cache_utils.clj:9)
	at clj_backend.modules.sqlite.cache_utils$flush_project_cache.invokeStatic(cache_utils.clj:49)
	at clj_backend.modules.sqlite.cache_utils$flush_project_cache.invoke(cache_utils.clj:40)
	at clj_backend.modules.project.project_service$save_project.invokeStatic(project_service.clj:414)
	at clj_backend.modules.project.project_service$save_project.invoke(project_service.clj:400)
	at clj_backend.modules.project.project_service$handle_project_event.invokeStatic(project_service.clj:633)
	at clj_backend.modules.project.project_service$handle_project_event.invoke(project_service.clj:628)
	at clj_backend.modules.project.project_service$eval48077$fn__48078$fn__48100$state_machine__29475__auto____48101$fn__48103.invoke(project_service.clj:638)
	at clj_backend.modules.project.project_service$eval48077$fn__48078$fn__48100$state_machine__29475__auto____48101.invoke(project_service.clj:638)
	at clojure.core.async.impl.runtime$run_state_machine.invokeStatic(runtime.clj:62)
	at clojure.core.async.impl.runtime$run_state_machine.invoke(runtime.clj:61)
	at clojure.core.async.impl.runtime$run_state_machine_wrapped.invokeStatic(runtime.clj:66)
	at clojure.core.async.impl.runtime$run_state_machine_wrapped.invoke(runtime.clj:64)
	at clojure.core.async.impl.runtime$take_BANG_$fn__24211.invoke(runtime.clj:75)
	at clojure.core.async.impl.channels.ManyToManyChannel$fn__24005$fn__24006.invoke(channels.clj:100)
	at clojure.lang.AFn.run(AFn.java:22)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at clojure.core.async.impl.concurrent$counted_thread_factory$reify__23874$fn__23875.invoke(concurrent.clj:29)
	at clojure.lang.AFn.run(AFn.java:22)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-09-10 14:19:40,344 [async-dispatch-13] ERROR jdbc.audit - 59. PreparedStatement.execute() PRAGMA wal_checkpoint(PASSIVE) 
 
org.sqlite.SQLiteException: [SQLITE_LOCKED]  A table in the database is locked (database table is locked)
	at org.sqlite.core.DB.newSQLException(DB.java:1030)
	at org.sqlite.core.DB.newSQLException(DB.java:1042)
	at org.sqlite.core.DB.execute(DB.java:881)
	at org.sqlite.jdbc3.JDBC3PreparedStatement.execute(JDBC3PreparedStatement.java:54)
	at net.sf.log4jdbc.PreparedStatementSpy.execute(PreparedStatementSpy.java:417)
	at next.jdbc.result_set$stmt__GT_result_set.invokeStatic(result_set.clj:663)
	at next.jdbc.result_set$stmt__GT_result_set.invoke(result_set.clj:658)
	at next.jdbc.result_set$eval4526$fn__4534.invoke(result_set.clj:891)
	at next.jdbc.protocols$eval3616$fn__3617$G__3607__3626.invoke(protocols.clj:33)
	at next.jdbc$execute_BANG_.invokeStatic(jdbc.clj:250)
	at next.jdbc$execute_BANG_.invoke(jdbc.clj:237)
	at clj_backend.modules.sqlite.cache_utils$safe_wal_checkpoint$fn__47855.invoke(cache_utils.clj:15)
	at clj_backend.modules.sqlite.cache_utils$safe_wal_checkpoint.invokeStatic(cache_utils.clj:13)
	at clj_backend.modules.sqlite.cache_utils$safe_wal_checkpoint.invoke(cache_utils.clj:9)
	at clj_backend.modules.sqlite.cache_utils$flush_project_cache.invokeStatic(cache_utils.clj:49)
	at clj_backend.modules.sqlite.cache_utils$flush_project_cache.invoke(cache_utils.clj:40)
	at clj_backend.modules.project.project_service$save_project.invokeStatic(project_service.clj:414)
	at clj_backend.modules.project.project_service$save_project.invoke(project_service.clj:400)
	at clj_backend.modules.project.project_service$handle_project_event.invokeStatic(project_service.clj:633)
	at clj_backend.modules.project.project_service$handle_project_event.invoke(project_service.clj:628)
	at clj_backend.modules.project.project_service$eval48077$fn__48078$fn__48100$state_machine__29475__auto____48101$fn__48103.invoke(project_service.clj:638)
	at clj_backend.modules.project.project_service$eval48077$fn__48078$fn__48100$state_machine__29475__auto____48101.invoke(project_service.clj:638)
	at clojure.core.async.impl.runtime$run_state_machine.invokeStatic(runtime.clj:62)
	at clojure.core.async.impl.runtime$run_state_machine.invoke(runtime.clj:61)
	at clojure.core.async.impl.runtime$run_state_machine_wrapped.invokeStatic(runtime.clj:66)
	at clojure.core.async.impl.runtime$run_state_machine_wrapped.invoke(runtime.clj:64)
	at clojure.core.async.impl.runtime$take_BANG_$fn__24211.invoke(runtime.clj:75)
	at clojure.core.async.impl.channels.ManyToManyChannel$fn__24005$fn__24006.invoke(channels.clj:100)
	at clojure.lang.AFn.run(AFn.java:22)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at clojure.core.async.impl.concurrent$counted_thread_factory$reify__23874$fn__23875.invoke(concurrent.clj:29)
	at clojure.lang.AFn.run(AFn.java:22)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-09-10 14:19:40,344 [async-dispatch-13] ERROR jdbc.sqltiming - 59. PreparedStatement.execute() FAILED! PRAGMA wal_checkpoint(PASSIVE) 
 {FAILED after 0 msec} 
org.sqlite.SQLiteException: [SQLITE_LOCKED]  A table in the database is locked (database table is locked)
	at org.sqlite.core.DB.newSQLException(DB.java:1030)
	at org.sqlite.core.DB.newSQLException(DB.java:1042)
	at org.sqlite.core.DB.execute(DB.java:881)
	at org.sqlite.jdbc3.JDBC3PreparedStatement.execute(JDBC3PreparedStatement.java:54)
	at net.sf.log4jdbc.PreparedStatementSpy.execute(PreparedStatementSpy.java:417)
	at next.jdbc.result_set$stmt__GT_result_set.invokeStatic(result_set.clj:663)
	at next.jdbc.result_set$stmt__GT_result_set.invoke(result_set.clj:658)
	at next.jdbc.result_set$eval4526$fn__4534.invoke(result_set.clj:891)
	at next.jdbc.protocols$eval3616$fn__3617$G__3607__3626.invoke(protocols.clj:33)
	at next.jdbc$execute_BANG_.invokeStatic(jdbc.clj:250)
	at next.jdbc$execute_BANG_.invoke(jdbc.clj:237)
	at clj_backend.modules.sqlite.cache_utils$safe_wal_checkpoint$fn__47855.invoke(cache_utils.clj:15)
	at clj_backend.modules.sqlite.cache_utils$safe_wal_checkpoint.invokeStatic(cache_utils.clj:13)
	at clj_backend.modules.sqlite.cache_utils$safe_wal_checkpoint.invoke(cache_utils.clj:9)
	at clj_backend.modules.sqlite.cache_utils$flush_project_cache.invokeStatic(cache_utils.clj:49)
	at clj_backend.modules.sqlite.cache_utils$flush_project_cache.invoke(cache_utils.clj:40)
	at clj_backend.modules.project.project_service$save_project.invokeStatic(project_service.clj:414)
	at clj_backend.modules.project.project_service$save_project.invoke(project_service.clj:400)
	at clj_backend.modules.project.project_service$handle_project_event.invokeStatic(project_service.clj:633)
	at clj_backend.modules.project.project_service$handle_project_event.invoke(project_service.clj:628)
	at clj_backend.modules.project.project_service$eval48077$fn__48078$fn__48100$state_machine__29475__auto____48101$fn__48103.invoke(project_service.clj:638)
	at clj_backend.modules.project.project_service$eval48077$fn__48078$fn__48100$state_machine__29475__auto____48101.invoke(project_service.clj:638)
	at clojure.core.async.impl.runtime$run_state_machine.invokeStatic(runtime.clj:62)
	at clojure.core.async.impl.runtime$run_state_machine.invoke(runtime.clj:61)
	at clojure.core.async.impl.runtime$run_state_machine_wrapped.invokeStatic(runtime.clj:66)
	at clojure.core.async.impl.runtime$run_state_machine_wrapped.invoke(runtime.clj:64)
	at clojure.core.async.impl.runtime$take_BANG_$fn__24211.invoke(runtime.clj:75)
	at clojure.core.async.impl.channels.ManyToManyChannel$fn__24005$fn__24006.invoke(channels.clj:100)
	at clojure.lang.AFn.run(AFn.java:22)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at clojure.core.async.impl.concurrent$counted_thread_factory$reify__23874$fn__23875.invoke(concurrent.clj:29)
	at clojure.lang.AFn.run(AFn.java:22)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-09-10 14:50:02,264 [async-dispatch-19] ERROR jdbc.audit - 61. PreparedStatement.execute() PRAGMA wal_checkpoint(RESTART) 
 
org.sqlite.SQLiteException: [SQLITE_LOCKED]  A table in the database is locked (database table is locked)
	at org.sqlite.core.DB.newSQLException(DB.java:1030)
	at org.sqlite.core.DB.newSQLException(DB.java:1042)
	at org.sqlite.core.DB.execute(DB.java:881)
	at org.sqlite.jdbc3.JDBC3PreparedStatement.execute(JDBC3PreparedStatement.java:54)
	at net.sf.log4jdbc.PreparedStatementSpy.execute(PreparedStatementSpy.java:417)
	at next.jdbc.result_set$stmt__GT_result_set.invokeStatic(result_set.clj:663)
	at next.jdbc.result_set$stmt__GT_result_set.invoke(result_set.clj:658)
	at next.jdbc.result_set$eval4526$fn__4534.invoke(result_set.clj:891)
	at next.jdbc.protocols$eval3616$fn__3617$G__3607__3626.invoke(protocols.clj:33)
	at next.jdbc$execute_BANG_.invokeStatic(jdbc.clj:250)
	at next.jdbc$execute_BANG_.invoke(jdbc.clj:237)
	at clj_backend.modules.sqlite.cache_utils$safe_wal_checkpoint$fn__47855.invoke(cache_utils.clj:19)
	at clj_backend.modules.sqlite.cache_utils$safe_wal_checkpoint.invokeStatic(cache_utils.clj:13)
	at clj_backend.modules.sqlite.cache_utils$safe_wal_checkpoint.invoke(cache_utils.clj:9)
	at clj_backend.modules.sqlite.cache_utils$flush_project_cache.invokeStatic(cache_utils.clj:49)
	at clj_backend.modules.sqlite.cache_utils$flush_project_cache.invoke(cache_utils.clj:40)
	at clj_backend.modules.project.project_service$save_project.invokeStatic(project_service.clj:414)
	at clj_backend.modules.project.project_service$save_project.invoke(project_service.clj:400)
	at clj_backend.modules.project.project_service$handle_project_event.invokeStatic(project_service.clj:633)
	at clj_backend.modules.project.project_service$handle_project_event.invoke(project_service.clj:628)
	at clj_backend.modules.project.project_service$eval48077$fn__48078$fn__48100$state_machine__29475__auto____48101$fn__48103.invoke(project_service.clj:638)
	at clj_backend.modules.project.project_service$eval48077$fn__48078$fn__48100$state_machine__29475__auto____48101.invoke(project_service.clj:638)
	at clojure.core.async.impl.runtime$run_state_machine.invokeStatic(runtime.clj:62)
	at clojure.core.async.impl.runtime$run_state_machine.invoke(runtime.clj:61)
	at clojure.core.async.impl.runtime$run_state_machine_wrapped.invokeStatic(runtime.clj:66)
	at clojure.core.async.impl.runtime$run_state_machine_wrapped.invoke(runtime.clj:64)
	at clojure.core.async.impl.runtime$take_BANG_$fn__24211.invoke(runtime.clj:75)
	at clojure.core.async.impl.channels.ManyToManyChannel$fn__24005$fn__24006.invoke(channels.clj:100)
	at clojure.lang.AFn.run(AFn.java:22)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at clojure.core.async.impl.concurrent$counted_thread_factory$reify__23874$fn__23875.invoke(concurrent.clj:29)
	at clojure.lang.AFn.run(AFn.java:22)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-09-10 14:50:02,265 [async-dispatch-19] ERROR jdbc.sqltiming - 61. PreparedStatement.execute() FAILED! PRAGMA wal_checkpoint(RESTART) 
 {FAILED after 0 msec} 
org.sqlite.SQLiteException: [SQLITE_LOCKED]  A table in the database is locked (database table is locked)
	at org.sqlite.core.DB.newSQLException(DB.java:1030)
	at org.sqlite.core.DB.newSQLException(DB.java:1042)
	at org.sqlite.core.DB.execute(DB.java:881)
	at org.sqlite.jdbc3.JDBC3PreparedStatement.execute(JDBC3PreparedStatement.java:54)
	at net.sf.log4jdbc.PreparedStatementSpy.execute(PreparedStatementSpy.java:417)
	at next.jdbc.result_set$stmt__GT_result_set.invokeStatic(result_set.clj:663)
	at next.jdbc.result_set$stmt__GT_result_set.invoke(result_set.clj:658)
	at next.jdbc.result_set$eval4526$fn__4534.invoke(result_set.clj:891)
	at next.jdbc.protocols$eval3616$fn__3617$G__3607__3626.invoke(protocols.clj:33)
	at next.jdbc$execute_BANG_.invokeStatic(jdbc.clj:250)
	at next.jdbc$execute_BANG_.invoke(jdbc.clj:237)
	at clj_backend.modules.sqlite.cache_utils$safe_wal_checkpoint$fn__47855.invoke(cache_utils.clj:19)
	at clj_backend.modules.sqlite.cache_utils$safe_wal_checkpoint.invokeStatic(cache_utils.clj:13)
	at clj_backend.modules.sqlite.cache_utils$safe_wal_checkpoint.invoke(cache_utils.clj:9)
	at clj_backend.modules.sqlite.cache_utils$flush_project_cache.invokeStatic(cache_utils.clj:49)
	at clj_backend.modules.sqlite.cache_utils$flush_project_cache.invoke(cache_utils.clj:40)
	at clj_backend.modules.project.project_service$save_project.invokeStatic(project_service.clj:414)
	at clj_backend.modules.project.project_service$save_project.invoke(project_service.clj:400)
	at clj_backend.modules.project.project_service$handle_project_event.invokeStatic(project_service.clj:633)
	at clj_backend.modules.project.project_service$handle_project_event.invoke(project_service.clj:628)
	at clj_backend.modules.project.project_service$eval48077$fn__48078$fn__48100$state_machine__29475__auto____48101$fn__48103.invoke(project_service.clj:638)
	at clj_backend.modules.project.project_service$eval48077$fn__48078$fn__48100$state_machine__29475__auto____48101.invoke(project_service.clj:638)
	at clojure.core.async.impl.runtime$run_state_machine.invokeStatic(runtime.clj:62)
	at clojure.core.async.impl.runtime$run_state_machine.invoke(runtime.clj:61)
	at clojure.core.async.impl.runtime$run_state_machine_wrapped.invokeStatic(runtime.clj:66)
	at clojure.core.async.impl.runtime$run_state_machine_wrapped.invoke(runtime.clj:64)
	at clojure.core.async.impl.runtime$take_BANG_$fn__24211.invoke(runtime.clj:75)
	at clojure.core.async.impl.channels.ManyToManyChannel$fn__24005$fn__24006.invoke(channels.clj:100)
	at clojure.lang.AFn.run(AFn.java:22)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at clojure.core.async.impl.concurrent$counted_thread_factory$reify__23874$fn__23875.invoke(concurrent.clj:29)
	at clojure.lang.AFn.run(AFn.java:22)
	at java.base/java.lang.Thread.run(Thread.java:840)
