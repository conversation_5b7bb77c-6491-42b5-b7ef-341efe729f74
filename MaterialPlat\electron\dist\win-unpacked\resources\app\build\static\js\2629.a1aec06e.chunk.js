"use strict";(self.webpackChunkzhongji_experiment_react=self.webpackChunkzhongji_experiment_react||[]).push([[2629],{82629:(e,l,i)=>{i.r(l),i.d(l,{default:()=>O});var a=i(65043),d=i(6051),n=i(97914),t=i(36497),o=i(16569),s=i(25055),r=i(47419),u=i(11645),v=i(83720),c=i(95206),p=i(80077),m=i(74117),_=i(56434),h=i.n(_),f=i(65694),g=i(39713),y=i(80231),b=i(67208),x=i(36950),j=i(30780),w=i(18650),A=(i(56543),i(10866)),C=i(81143);i(68374);const k=C.Ay.div`
    width: 100%;
    height: 100%;
    padding: 10px;
    background: #fff;
    /* display: flex; */
    overflow-y: auto;
    /* justify-content: space-between; */

    &::-webkit-scrollbar-thumb {
        background: #666666;
    }
    .container-box{
        min-width: 400px;
    }
    
    img {
        width: 100%;
        height: 100%;
        object-fit: contain;
    }

`,S=C.Ay.div`
    .unique { 
        border-bottom: 1px solid rgba(220 ,220, 220,1);
        padding: 2px
    }
    .unique-content {
        padding: 2px;
    }

`,F={labelCol:{flex:"80px"},wrapperCol:{flex:"1"}};var I=i(70579);const E=e=>{let{value:l={units_id:"",value:""},onChange:i,unitData:o={},type:s,readOnly:r}=e;const[u,v]=(0,a.useState)(l.value),[c,p]=(0,a.useState)(l.units_id),m=e=>{null===i||void 0===i||i({value:u,units_id:c,...e})};return(0,I.jsxs)(d.A,{children:[(0,I.jsx)(n.A,{value:l.val||u,onChange:e=>{v(e),m({value:e})},style:{width:"5vw"},disabled:r}),!s&&(0,I.jsx)(t.A,{disabled:r,showSearch:!0,optionFilterProp:"name",value:l.unit||c,style:{width:"4vw"},fieldNames:{label:"name",value:"id"},onChange:e=>{const l=(0,x.tJ)(u,null===o||void 0===o?void 0:o.id,e,c);p(e),v(l),m({units_id:e,value:l})},options:o.units})]})},N=e=>{let{value:l={units_id:"",value:"",name:""},onChange:i,...a}=e;return(0,I.jsx)(d.A,{children:(0,I.jsx)(t.A,{...a,style:{width:"13vw"},value:l.value?String(l.value):void 0,onChange:e=>{null===i||void 0===i||i({value:e})}})})},V=e=>{let{domId:l,layoutConfig:i}=e;return(0,I.jsx)(S,{children:(0,I.jsx)(y.A,{domId:l,layoutConfig:i})})},O=e=>{let{id:l,layoutConfig:i}=e;const[d,n]=o.Ay.useMessage(),[_]=s.A.useForm(),[y]=s.A.useForm(),{useSubscriber:C}=(0,g.A)(),{t:S}=(0,m.Bd)(),{updateOptSample:O,getSample:J}=(0,A.A)(),{sampleList:$,optSample:q,defaultSample:z}=(0,p.d4)((e=>e.project)),D=(0,p.d4)((e=>e.global.unitList)),{openExperiment:B}=(0,p.d4)((e=>e.subTask)),[L,P]=(0,a.useState)([]),[T,H]=(0,a.useState)([]),{initTableConfigData:K}=(0,j.A)(),[M,R]=(0,a.useState)(""),[U,G]=(0,a.useState)(),Q=(0,p.wA)(),W=(0,a.useRef)({});(0,a.useEffect)((()=>(X(),()=>{Z()})),[L]);const X=()=>{let e=null;q?e=q.code:z&&(e=z.code),e&&L.forEach((l=>{let{code:i,parameter_id:a}=l;Y(e,i,a)}))},Y=async(e,l,i)=>{const a=await C(`${e}-${l}`);W.current={...W.current,[`${e}-${l}`]:a};for await(const[d,n]of a){const e=JSON.parse(n);e&&_.setFieldValue(i,{..._.getFieldValue(i),val:e.Value})}},Z=()=>{Object.values(W.current).forEach((e=>null===e||void 0===e?void 0:e.close()))};(0,a.useEffect)((()=>{var e,l;R(""),G({parameter_id:"type"}),_.resetFields(),P([]),H([]);const i=J(q);if(R(null===$||void 0===$||null===(e=$.find((e=>(null===e||void 0===e?void 0:e.code)===(null===i||void 0===i?void 0:i.sample_type))))||void 0===e?void 0:e.img),(null===q||void 0===q||null===(l=q.data)||void 0===l?void 0:l.length)>0){var a;const e=null===i||void 0===i?void 0:i.data.map((e=>"select"===(null===e||void 0===e?void 0:e.data_type)?e:{...e,value:(0,x.tJ)(e.value,e.dimension_id,e.units_id)}));P(e);const l={};e.forEach((e=>{l[e.parameter_id]=e})),y.setFieldsValue(null===i||void 0===i||null===(a=i.samples)||void 0===a?void 0:a.reduce(((e,l)=>(e[l.id]=l.value,e)),{})),H(null===i||void 0===i?void 0:i.samples),_.setFieldsValue({sample_type:null===i||void 0===i?void 0:i.sample_type,...l})}else _.setFieldsValue({sample_type:null===i||void 0===i?void 0:i.sample_type});return()=>{_.resetFields(),P([]),H([])}}),[q,$]);const ee=async()=>{const e=await _.validateFields(),l=await y.validateFields(),i=T.map((e=>({...e,value:l[e.id]})));if(e&&q){const e=L.map((e=>{if("select"===e.data_type)return{...e,value:e.value};const l=D.find((l=>l.id===e.dimension_id));return l?{...e,value:(0,x.tJ)(e.value,e.dimension_id,l.default_unit_id,e.units_id)}:{...e,value:e.value}}));O({...q,data:e,samples:i||[]});const l={...q,samples:i||[],checked:!!q.checked,data:e,id:q.key};ie(l),await(0,b.olN)({sample_param:e}),K()}},le=e=>e.map((e=>(null===e||void 0===e||delete e.new,null!==e&&void 0!==e&&e.children&&(null===e||void 0===e?void 0:e.children.length)>0&&le(null===e||void 0===e?void 0:e.children),e))),ie=async e=>{try{await(0,b.jSz)({sample_instance:e})&&((async()=>{try{const e=await(0,b.HBc)();e&&Q({type:f.UK,param:le(e)})}catch(e){throw console.log(e),e}})(),d.open({type:"success",content:S("\u64cd\u4f5c\u6210\u529f")}))}catch(l){throw console.log(l),l}},ae=e=>{var l;(G(e),"type"===e.parameter_id)?R(null===$||void 0===$||null===(l=$.find((e=>(null===e||void 0===e?void 0:e.code)===(null===q||void 0===q?void 0:q.sample_type))))||void 0===l?void 0:l.img):R(null===e||void 0===e?void 0:e.parameter_img)};return(0,I.jsxs)(I.Fragment,{children:[n,(0,I.jsx)(k,{children:(0,I.jsx)("div",{className:"container-box",children:(0,I.jsxs)(r.A,{children:[(0,I.jsx)(u.A,{span:12,children:(0,I.jsxs)(s.A,{...F,labelAlign:"left",form:_,children:[(0,I.jsx)(s.A.Item,{label:S("\u8bd5\u6837\u7c7b\u578b"),name:"sample_type",rules:[{required:!0}],onClick:()=>ae({parameter_id:"type"}),style:{background:"type"===(null===U||void 0===U?void 0:U.parameter_id)?"rgba(66,111,255,0.1)":""},children:(0,I.jsx)(t.A,{disabled:!0,showSearch:!0,optionFilterProp:"sample_name",fieldNames:{label:"sample_name",value:"code"},options:$.map((e=>({...e,sample_name:S(e.sample_name)}))),onChange:(e,l)=>{const i=h()(null===l||void 0===l?void 0:l.parameters),a=null===i||void 0===i?void 0:i.map((e=>(e.sample_code=null===l||void 0===l?void 0:l.code,e.parameter_code=null===e||void 0===e?void 0:e.code,null===e||void 0===e||delete e.code,null===e||void 0===e||delete e.order_num,null===e||void 0===e||delete e.delete_flag,null===e||void 0===e||delete e.units_id,e)));P([...L,...a])}})}),null===L||void 0===L?void 0:L.map((e=>(0,I.jsx)(I.Fragment,{children:"select"===e.data_type?(0,I.jsx)(s.A.Item,{label:S(e.parameter_name),name:e.parameter_id,hidden:!(null===e||void 0===e||!e.hidden_flag),onClick:()=>ae(e),style:{background:(null===U||void 0===U?void 0:U.parameter_id)===(null===e||void 0===e?void 0:e.parameter_id)?"rgba(66,111,255,0.1)":""},children:(0,I.jsx)(N,{disabled:!!e.func||!!e.is_disabled_func||!!e.is_visible_func||B,options:(null===e||void 0===e?void 0:e.select_options)||[],onChange:l=>{P(L.map((i=>i.parameter_id===e.parameter_id?{...i,...l}:i)))}})},e.parameter_id):(0,I.jsx)(s.A.Item,{label:S(e.parameter_name),name:e.parameter_id,hidden:!(null===e||void 0===e||!e.hidden_flag),onClick:()=>ae(e),style:{background:(null===U||void 0===U?void 0:U.parameter_id)===(null===e||void 0===e?void 0:e.parameter_id)?"rgba(66,111,255,0.1)":""},children:(0,I.jsx)(E,{type:e.type,readOnly:!!e.func||!!e.is_disabled_func||!!e.is_visible_func||B,onSub:ee,unitData:null===D||void 0===D?void 0:D.find((l=>l.id===e.dimension_id)),onChange:l=>{P(L.map((i=>i.parameter_id===e.parameter_id?{...i,...l}:i)))}})},e.parameter_id)}))),(0,I.jsx)(s.A,{form:y,...F,labelAlign:"left",children:null===T||void 0===T?void 0:T.map((e=>(0,I.jsx)(s.A.Item,{label:S(e.name),name:e.id,children:(0,I.jsx)(v.A,{style:{width:"9.5vw"}})},e.id)))}),(0,I.jsx)(s.A.Item,{children:(0,I.jsx)(c.Ay,{disabled:B,type:"primary",onClick:ee,children:S("\u63d0\u4ea4")})})]})}),(0,I.jsx)(u.A,{push:3,span:9,children:(0,I.jsx)("img",{src:M||w.Np,alt:""})})]})})}),(0,I.jsx)(V,{domId:l,layoutConfig:i})]})}}}]);
//# sourceMappingURL=2629.a1aec06e.chunk.js.map