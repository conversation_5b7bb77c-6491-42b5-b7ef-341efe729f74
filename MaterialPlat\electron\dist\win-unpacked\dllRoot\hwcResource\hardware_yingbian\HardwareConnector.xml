<?xml version="1.0"?>
<doc>
    <assembly>
        <name>HardwareConnector</name>
    </assembly>
    <members>
        <member name="T:CCSS.HWConnector.WebServer">
            <summary>
            硬件连接器Web服务器
            提供硬件管理和控制的HTTP API接口
            
            Swagger访问方式：
            - Swagger UI: http://localhost:{port}/swagger
            - Swagger JSON: http://localhost:{port}/swagger/v1/swagger.json
            - 其中{port}为服务器启动时指定的端口号
            
            主要功能：
            - 硬件设备管理
            - 端口列表查询
            - 调试控制接口
            - 数据接收和处理
            </summary>
        </member>
        <member name="M:CCSS.HWConnector.WebServer.ToResultMsg``1(System.Int32,System.String,``0)">
            <summary>
            统一封装返回的结果
            </summary>
            <param name="code"></param>
            <param name="msg"></param>
            <param name="t"></param>
            <typeparam name="T"></typeparam>
            <returns></returns>
        </member>
        <member name="T:CCSS.HWConnector.HwCmdClient">
            <summary>
            命令服务类，用于启动、停止和发送消息。
            RouterSocket 和 Dealer通过Routing Key来决定单对单通讯
            </summary>
        </member>
        <member name="M:CCSS.HWConnector.HwCmdClient.Start(System.String,System.Action{System.String},System.String)">
            <summary>
            启动服务。
            </summary>
            <param name="msgHandler">处理接收消息的委托。</param>
        </member>
        <member name="M:CCSS.HWConnector.HwCmdClient.sendMsg(System.String)">
            <summary>
            发送消息。
            </summary>
            <param name="msg">要发送的消息。</param>
        </member>
        <member name="M:CCSS.HWConnector.HwCmdClient.Stop">
            <summary>
            停止服务。
            </summary>
        </member>
        <member name="T:CCSS.HWConnector.HWDataClient">
            <summary>
            数据服务类，用于启动、停止和发送消息，有心跳检测和重新连接功能。
            使用 PushSocket，并使用 lock 确保 sendMsg 的线程安全性。
            </summary>
        </member>
        <member name="M:CCSS.HWConnector.HWDataClient.Start(System.String,System.String)">
            <summary>
            启动数据服务，包括绑定端口和设置心跳。
            </summary>
        </member>
        <member name="M:CCSS.HWConnector.HWDataClient.Stop">
            <summary>
            停止数据服务。
            </summary>
        </member>
        <member name="M:CCSS.HWConnector.HWDataClient.sendMsg(NetMQ.NetMQMessage)">
            <summary>
            发送消息，如果对端断链会尝试重新连接和发送。此方法是线程安全的。
            </summary>
            <param name="msg">要发送的NetMQMessage。</param>
        </member>
        <member name="M:CCSS.HWConnector.HWDataClient.HandleReceiveReady(System.Object,NetMQ.NetMQSocketEventArgs)">
            <summary>
            处理接收准备的事件。
            </summary>
            <param name="sender">事件发送者。</param>
            <param name="e">事件参数。</param>
        </member>
        <member name="M:CCSS.HWConnector.HWDataClient.ReconnectAndSendMessage(NetMQ.NetMQMessage)">
            <summary>
            断链后的重新连接和发送消息逻辑。
            </summary>
            <param name="msg">要重新发送的消息。</param>
        </member>
        <member name="T:Config">
            <summary>
            硬件连接器的配置
            </summary>
        </member>
        <member name="M:Config.#ctor(System.String,System.String)">
            <summary>
            硬件连接器的配置
            </summary>
        </member>
        <member name="M:Config.GetHardware(System.String)">
            <summary>
            获取硬件实现。
            </summary>
            <remarks>
            如果配置文件存在，此方法将从中读取硬件实现的类型名称。
            如果没有配置文件或解析失败，将使用默认硬件实现"HwSim16.HwClassMain, HwSim16"。
            </remarks>
            <returns>硬件实现的实例。</returns>
            <exception cref="T:System.InvalidOperationException">如果找不到类型或无法创建类型的实例。</exception>
        </member>
        <member name="P:Events.EventHandlers.DebugIsActive">
            <summary>
            控制DebugInfoPrint是否激活，0=不打印，1=打印
            </summary>
        </member>
        <member name="P:Events.EventHandlers.DebugInterval">
            <summary>
            控制DebugInfoPrint的打印间隔，每隔多少次调用才打印一次
            </summary>
        </member>
        <member name="P:Events.EventHandlers.DebugPrintMode">
            <summary>
            控制调试信息打印模式：
            0 = 不打印Count相关信息
            1-9 = 打印基础Count信息（CDataBlock的各种Count）
            10-29 = 打印ServoData详细信息（10=基础DataCount，11=包含CData的SensorCount）
            30-39 = 打印TempData详细信息（30=基础DataCount，31=包含CData的SensorCount）
            40-49 = 打印CreepData详细信息（40=基础DataCount，41=包含CData的SensorCount）
            50 = 打印ADData详细信息（包含DataCount）
            60 = 打印BitIn数组信息（最后一个数据）
            </summary>
        </member>
        <member name="P:Events.EventHandlers.DebugPrintIndex">
            <summary>
            控制CData详细信息打印的索引：
            -1 = 只打印SensorCount信息
            >=0 = 打印指定索引的CData完整信息（包括所有属性和传感器数据）
            </summary>
        </member>
        <member name="F:Events.EventHandlers.debugCallCounter">
            <summary>
            内部计数器，用于跟踪DebugInfoPrint的调用次数
            </summary>
        </member>
        <member name="F:Events.EventHandlers.dataBlockHandlerStopwatch">
            <summary>
            用于统计DataBlockHandler函数执行时间的Stopwatch
            </summary>
        </member>
        <member name="F:Events.EventHandlers.intervalTotalElapsedMs">
            <summary>
            DataBlockHandler函数在当前时间间隔内的累计执行时间（毫秒）
            </summary>
        </member>
        <member name="F:Events.EventHandlers.totalDataCount">
            <summary>
            DataBlock.Servo[0].DataCount的累加值
            </summary>
        </member>
        <member name="M:Events.EventHandlers.DebugInfoPrint(IHardware.Hw.CDataBlock)">
            <summary>
            调试信息打印函数，用于输出CDataBlock的详细Count信息
            根据DebugPrintMode的值控制不同级别的调试信息输出：
            
            DebugPrintMode = 0: 不打印任何Count相关信息
            DebugPrintMode = 1-9: 打印CDataBlock基础Count信息
              - ServoChCount, TempChCount, CreepChCount
              - InCount, OutCount, ADCount
            
            DebugPrintMode = 10-29: 打印ServoData详细信息
              - 10: 打印每个ServoData轴的DataCount
              - 11: 根据DebugPrintIndex打印CData详细信息
            
            DebugPrintMode = 30-39: 打印TempData详细信息
              - 30: 打印每个TempData轴的DataCount
              - 31: 根据DebugPrintIndex打印CData详细信息
            
            DebugPrintMode = 40-49: 打印CreepData详细信息
              - 40: 打印每个CreepData轴的DataCount
              - 41: 根据DebugPrintIndex打印CData详细信息
            
            DebugPrintMode == 50: 打印ADData详细信息
              - 打印每个ADData轴的DataCount
            DebugPrintMode == 60: 打印BitIn数组信息
              - 打印BitIn数组的最后一个数据
            
            DebugPrintIndex控制CData详细信息打印：
              - -1: 只打印SensorCount信息
              - >=0: 打印指定索引的CData完整信息（包括所有属性和传感器数据）
            </summary>
            <param name="dataBlock">要调试的CDataBlock数据块</param>
        </member>
        <member name="M:Events.EventHandlers.CanSendMsg(IHardware.Hw.CDataBlock)">
            <summary>
            判断这条数据是否需要发送,全都是0 则不发送
            </summary>
            <param name="cDataBlock"></param>
            <returns>返回false则不发送数据</returns>
        </member>
        <member name="M:Events.EventHandlers.CanSendFlatMsg(Consts.FlatCDataBlock)">
            <summary>
            判断这条FlatCDataBlock数据是否需要发送,全都是0 则不发送
            </summary>
            <param name="flatDataBlock"></param>
            <returns>返回false则不发送数据</returns>
        </member>
        <member name="M:HardWareConnection.GetHardWare(System.String)">
            <summary>
            根据配置文件所在的路径实例化不同的Dll
            </summary>
            <param name="Path"></param>
            <returns></returns>
        </member>
        <member name="T:Ccss.HWConnector.Messages.FunctionCmd">
            试验机-> 硬件连接器
        </member>
        <member name="M:Ccss.HWConnector.Messages.FunctionCmd.#ctor(System.String,System.String,System.String,System.Object[],System.String)">
            试验机-> 硬件连接器
        </member>
        <member name="T:Ccss.HWConnector.Messages.FuncReturn">
            <summary>
            硬件连接器 -> 试验机
            FIXME: 增加字段CmdId作为消息返回的标识, 后续删除ProcessId和SubtaskId 修改Cmd为必填字段
            </summary>
        </member>
        <member name="M:Ccss.HWConnector.Messages.FuncReturn.#ctor(System.Int32,System.String,System.String,System.String,System.Object,System.String)">
            <summary>
            硬件连接器 -> 试验机
            FIXME: 增加字段CmdId作为消息返回的标识, 后续删除ProcessId和SubtaskId 修改Cmd为必填字段
            </summary>
        </member>
        <member name="M:HardwareConnectorWinForm.Program.Main(System.String[])">
            <summary>
             The main entry point for the application.
            </summary>
        </member>
        <member name="M:HardwareConnectorWinForm.StartFrom.CloseAllDevice">
            <summary>
            关闭所有的该dll的所有的物理硬件
            </summary>
        </member>
        <member name="F:HardwareConnectorWinForm.StartFrom.components">
            <summary>
             Required designer variable.
            </summary>
        </member>
        <member name="M:HardwareConnectorWinForm.StartFrom.Dispose(System.Boolean)">
            <summary>
             Clean up any resources being used.
            </summary>
            <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        </member>
        <member name="M:HardwareConnectorWinForm.StartFrom.InitializeComponent">
            <summary>
             Required method for Designer support - do not modify
             the contents of this method with the code editor.
            </summary>
        </member>
        <member name="T:HardwareConnectorWinForm.ApplicationConfiguration">
            <summary>
             Bootstrap the application configuration.
            </summary>
        </member>
        <member name="M:HardwareConnectorWinForm.ApplicationConfiguration.Initialize">
            <summary>
             Bootstrap the application as follows:
             <code>
             global::System.Windows.Forms.Application.EnableVisualStyles();
             global::System.Windows.Forms.Application.SetCompatibleTextRenderingDefault(false);
             global::System.Windows.Forms.Application.SetHighDpiMode(HighDpiMode.SystemAware);
            </code>
            </summary>
        </member>
    </members>
</doc>
