"use strict";(self.webpackChunkzhongji_experiment_react=self.webpackChunkzhongji_experiment_react||[]).push([[742],{10742:(e,i,t)=>{t.r(i),t.d(i,{default:()=>B});var n=t(65043),l=t(74117),o=t(80077),d=t(81143),a=t(80231),r=t(21256),s=t(36950),c=t(6051),u=t(34458),v=(t(56543),t(33154)),p=t(29977),h=t(68374),x=t(86178),m=t.n(x),g=t(70579);const f=()=>{const{t:e}=(0,l.Bd)(),[i,t]=(0,n.useState)(m()().format("YYYY-MM-DD HH:mm:ss"));return(0,n.useEffect)((()=>{const e=setInterval((()=>{t(m()().format("YYYY-MM-DD HH:mm:ss"))}),1e3);return()=>clearInterval(e)}),[]),(0,g.jsx)("span",{children:i})},j=d.Ay.div`
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%;
    font-size: ${(0,h.D0)("14px")};
    color: #003EBD;
    
    .layout-left {
        display: flex;
        align-items: center;
        gap: 10px;
        
        .item-input-content {
            display: inline-block;
            height: 100%;
            vertical-align: middle;
            
            .item-input {
                margin-right: 10px;
                display: flex;
                height: 100%;
                align-items: center;
            }
        }
    }
    
    .layout-right {
        display: flex;
        align-items: center;
        
        &.title {
            font-size: ${(0,h.D0)("16px")};
            font-weight: 400;
        }
    }
`,y=e=>{var i,t,n;let{config:o}=e;const{t:d}=(0,l.Bd)(),a=(0,p.A)(),{findUnit:r}=(0,v.A)(),s=e=>{var i;return null!==(i=null===a||void 0===a?void 0:a.find((i=>i.code===e)))&&void 0!==i?i:{}},h=e=>{var i;const{default_val:{unitType:t,unit:n}={}}=s(e)||{};return t&&n&&(null===(i=r(t,n))||void 0===i?void 0:i.name)||""},x=e=>{var i,t;return{width:e.width,color:null===a||void 0===a||null===(i=a.find((i=>i.code===(null===e||void 0===e?void 0:e.color))))||void 0===i||null===(t=i.default_val)||void 0===t?void 0:t.value,justifyContent:e.align}};return(0,g.jsxs)(j,{children:[(0,g.jsx)("div",{className:"layout-left",children:null===o||void 0===o||null===(i=o.data)||void 0===i?void 0:i.map(((e,i)=>{var t,n,l;const o=s(e.input_code);return null!==o&&void 0!==o&&o.name?(0,g.jsx)("div",{className:"item-input-content",children:(0,g.jsxs)("div",{className:"item-input",style:x(e),children:[o.name,o.name&&"\uff1a",String(null!==(t=null===o||void 0===o||null===(n=o.default_val)||void 0===n?void 0:n.value)&&void 0!==t?t:""),String(null!==(l=h(e.input_code))&&void 0!==l?l:"")]})},i):null}))}),(0,g.jsx)("div",{className:"layout-right",children:(0,g.jsxs)(c.A,{children:[(0,g.jsxs)("div",{className:"title",children:[d("\u7528\u6237"),"\uff1a",null===(t=(0,u.ug)())||void 0===t?void 0:t.name]}),(0,g.jsxs)("div",{className:"title",children:[d("\u89d2\u8272"),"\uff1a",d(null===(n=(0,u.ug)())||void 0===n?void 0:n.role_name)]}),(0,g.jsxs)("div",{className:"title",children:[d("\u65f6\u95f4"),"\uff1a",(0,g.jsx)(f,{})]})]})})]})};var w=t(97914),b=t(36497),C=t(75440),A=t(4554),_=t(9339),k=t(47936);const N="center",I=[{label:"\u5de6\u4fa7",value:"left"},{label:"\u5c45\u4e2d",value:"center"},{label:"\u53f3\u4fa7",value:"right"}],S=d.Ay.div`
    background-color: #fff;
    padding: 20px 40px;

    .table-layout {
        height: 50vh;
        .col-layout {
            margin-bottom: 1vw;
            display: flex;
            .tip-text {
                flex: 1;
                text-align: right;
                color: red;
            }
        }
        .action-buttons {
            display: flex;
            gap: 8px;
            font-size: 16px;
            color: #007bff;
            .disabled {
                color: #ccc;
                cursor: not-allowed;
            }
        }
    }
    .cursor-pointer {
        cursor: pointer;
    }
    > .footer-btns {
        display: flex;
        justify-content: center;
        margin-top: 40px;
        button {
            margin-right: 10px;
        }
    }
`,D=e=>{var i;let{open:t,config:o,onOk:d,onCancel:a}=e;const{t:r}=(0,l.Bd)(),s=(0,p.A)(),c=(0,k.A)(),[u,v]=(0,n.useState)(0),[h,x]=(0,n.useState)([]),[m,f]=(0,n.useState)(!1),[j,y]=(0,n.useState)(),[D,M]=(0,n.useState)(void 0);(0,n.useEffect)((()=>{var e,i;v(null!==(e=null===o||void 0===o?void 0:o.count)&&void 0!==e?e:0),x(null!==(i=null===o||void 0===o?void 0:o.data)&&void 0!==i?i:[])}),[o]);const Y=()=>{a()},z=(e,i,t)=>{x(h.map((n=>n.id===t?{...n,[i]:e}:n)))},B=(e,i)=>{y(i),f(!0),M(e)},E=[{title:r("\u5217"),width:"3vw",dataIndex:"id",render:(e,i,t)=>(0,g.jsx)("span",{children:t+1})},{title:r("\u5bbd\u5ea6"),width:"5vw",dataIndex:"width",render:(e,i)=>(0,g.jsx)(w.A,{value:e,onChange:e=>z(e,"width",i.id),min:50,max:500,step:10})},{title:r("\u8f93\u5165\u53d8\u91cf"),width:"10vw",dataIndex:"input_code",render:(e,i)=>{var t,n;return(0,g.jsx)("div",{onClick:()=>B(i,"input_code"),className:"cursor-pointer",children:null!==(t=null===s||void 0===s||null===(n=s.find((i=>i.code===e)))||void 0===n?void 0:n.name)&&void 0!==t?t:"--"})}},{title:r("\u989c\u8272\u53d8\u91cf"),width:"10vw",dataIndex:"color",render:(e,i)=>{var t,n;return(0,g.jsx)("div",{onClick:()=>B(i,"color"),className:"cursor-pointer",children:null!==(t=null===c||void 0===c||null===(n=c.find((i=>i.code===e)))||void 0===n?void 0:n.name)&&void 0!==t?t:"--"})}},{title:r("\u5bf9\u9f50\u65b9\u5f0f"),width:"8vw",dataIndex:"align",render:(e,i)=>(0,g.jsx)(b.A,{value:e,onChange:e=>z(e,"align",i.id),options:I,style:{width:"100%"}})}];return(0,g.jsxs)(g.Fragment,{children:[(0,g.jsx)(C.A,{title:r("\u9875\u811a\u914d\u7f6e"),open:t,onCancel:Y,width:"80vw",footer:null,children:(0,g.jsxs)(S,{children:[(0,g.jsxs)("div",{className:"table-layout",children:[(0,g.jsxs)("div",{className:"col-layout",children:[(0,g.jsxs)("span",{children:[r("\u5217\u6570"),"\uff1a"]}),(0,g.jsx)(w.A,{value:u,onChange:e=>{x((i=>{const t=Array.from({length:Math.max(e-i.length,0)},(()=>({id:crypto.randomUUID(),width:100,input_code:"",color:"",align:N})));return[...i,...t]})),v(e)},min:0,max:10,style:{marginLeft:10}})]}),(0,g.jsx)(_.A,{columns:E,dataSource:h.slice(0,u),pagination:!1,rowKey:"id",scroll:{y:400}})]}),(0,g.jsxs)("div",{className:"footer-btns",children:[(0,g.jsx)(A.A,{onClick:()=>{d({data:h.slice(0,u),count:u})},children:r("\u786e\u5b9a")}),(0,g.jsx)(A.A,{onClick:Y,children:r("\u53d6\u6d88")})]})]})}),m&&(0,g.jsx)(C.A,{title:r("\u9009\u62e9\u53d8\u91cf"),open:m,onOk:()=>{z(null===D||void 0===D?void 0:D[j],j,null===D||void 0===D?void 0:D.id),f(!1),M(void 0)},onCancel:()=>{f(!1)},width:"30vw",children:(0,g.jsx)(b.A,{value:null===D||void 0===D?void 0:D[j],onChange:e=>{M({...D,[j]:e})},style:{width:"100%"},showSearch:!0,placeholder:r("\u8bf7\u9009\u62e9\u53d8\u91cf"),optionFilterProp:"children",filterOption:(e,i)=>{var t;return(null!==(t=null===i||void 0===i?void 0:i.label)&&void 0!==t?t:"").toLowerCase().includes(e.toLowerCase())},options:(null===(i="color"===j?c:s)||void 0===i?void 0:i.map((e=>({label:e.name,value:e.code}))))||[]})})]})},M=d.Ay.div`
  position: relative;
  width: 100%;
  height: 100%;
  background: rgba(255,255,255,0.9);
  padding: 0 5px;
  display: flex;
  align-items: center;
  justify-content: space-between;
`,Y=d.Ay.div`
  .unique { 
    border-bottom: 1px solid rgba(220 ,220, 220,1);
    padding: 2px
  }
  .unique-content {
    padding: 2px;
  }
`,z=e=>{let{domId:i,layoutConfig:t,onEdit:n}=e;const{t:o}=(0,l.Bd)();return(0,g.jsx)(Y,{children:(0,g.jsx)(a.A,{domId:i,layoutConfig:t,children:(0,g.jsx)("div",{className:"unique-content",onClick:n,children:o("\u7f16\u8f91\u63a7\u4ef6\u5c5e\u6027")})})})},B=e=>{let{item:i,id:t,layoutConfig:l}=e;const d=(0,o.d4)((e=>e.template.widgetData)),{editWidget:a}=(0,r.A)(),[c,u]=(0,n.useState)(!1),v=(0,n.useMemo)((()=>(0,s.Rm)(d,"widget_id",null===i||void 0===i?void 0:i.widget_id)),[i,d]);return(0,g.jsxs)(M,{children:[(0,g.jsx)(y,{config:null===v||void 0===v?void 0:v.data_source}),(0,g.jsx)(D,{open:c,config:null===v||void 0===v?void 0:v.data_source,onOk:async e=>{if(v){await a({...v,data_source:e})&&u(!1)}},onCancel:()=>u(!1)}),(0,g.jsx)(z,{domId:t,layoutConfig:l,onEdit:()=>{u(!0)}})]})}},47936:(e,i,t)=>{t.d(i,{A:()=>a});var n=t(65043),l=t(80077),o=t(32099);const d=()=>(0,o.Mz)([e=>e.inputVariable.inputVariableMap,e=>e.inputVariable.textCodeList],((e,i)=>i.map((i=>e.get(i))))),a=()=>{const e=(0,n.useMemo)(d,[]);return(0,l.d4)((i=>e(i)))}}}]);
//# sourceMappingURL=742.72b6769d.chunk.js.map