"use strict";(self.webpackChunkzhongji_experiment_react=self.webpackChunkzhongji_experiment_react||[]).push([[1234],{4136:(e,n,i)=>{i.d(n,{E_:()=>d,N1:()=>c,QP:()=>a,kQ:()=>u,mc:()=>r,zM:()=>x});var t=i(81143),l=i(68374);const r=t.Ay.div`
    background-color: #fff;

    .save-close {
        display: flex;
        justify-content: space-around;
        .save-btn {
            margin-right: ${(0,l.D0)("20px")};
        }
    }
`,s=`1px solid ${l.o$.borderGray}`,o=(0,l.D0)("20px"),a=t.Ay.div`
    padding: ${(0,l.D0)("20px")} ${(0,l.D0)("40px")};

    >.form {
        max-height: 70vh;
        overflow: auto;
        .title {
            font-size: 13px;
            font-weight: bold;
            margin-bottom: ${(0,l.D0)("15px")};
        }

        >.top {
            border: ${s};
            padding: ${(0,l.D0)("10px")};
        }
        >.bottom {
            >.bottom-left {
                margin-top: ${(0,l.D0)("20px")};
                border: ${s};
                padding: ${(0,l.D0)("10px")};
                width: 100%;
            }
            >.bottom-right {
                margin-top: ${(0,l.D0)("20px")};
                border: ${s};
                padding: ${(0,l.D0)("10px")};
                width: 100%;
            }
        }
    }
    >.footer-btns {
        display: flex;
        justify-content: center;
        margin-top: ${(0,l.D0)("40px")};
        button {
            margin-right: ${(0,l.D0)("10px")};
        }
    }
`,d=t.Ay.div`
    >.container {
        display: flex;
        .title {
            font-size: 13px;
            font-weight: bold;
            margin-bottom: ${(0,l.D0)("15px")};
        }

        >.siderbar {
            padding: ${(0,l.D0)("20px")};
            width: ${(0,l.D0)("260px")};
            border-right: 5px solid ${l.o$.modalBack};
            >.item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                &.active, &:hover {
                    background: ${l.o$.hoverActiveBlue};
                }
                > img {
                    height: 16px;
                }
            }
        
        }
        >.form {
            padding: ${(0,l.D0)("20px")} ${(0,l.D0)("40px")};
            width: 100%;

            >.step-a {
                >.centre {
                    margin-top: 10px;
                    padding: ${(0,l.D0)("20px")};
                    border: ${s};
                }
            }

            // 曲线
            >.step-b {
                >.top {
                    padding: ${(0,l.D0)("20px")};
                    border: ${s};
                    >.content {
                        display: flex;
                        justify-content: space-between;
                        >.left {
                            width: 70%;
                            .list-item {
                                display: flex;
                                justify-content: space-between;
                                align-items: center;
                                width: 100%;
                                cursor: pointer;
                                >.name-layout {
                                    padding: ${(0,l.D0)("8px")};
                                    width: 70%;
                                }
                                >.selected {
                                    background-color: #F5F7FF;         
                                    color: #155BEC;
                                    border-radius: 4px;
                                }
                            }
                        }
                    }
                }
                
                >.bottom {
                    padding: ${(0,l.D0)("20px")};
                    height: ${(0,l.D0)("300px")};
                    margin-top: ${o};
                    border: ${s};
                }
            }

            // X-轴
            >.step-c {
                >.top {
                    height: auto;
                    display: flex;
                    justify-content: space-between;

                    >.left {
                        width: 49%;
                        border: ${s};
                        padding: ${(0,l.D0)("20px")} ${(0,l.D0)("40px")};
                    }

                    >.right {
                        width: 49%;
                        border: ${s};
                        padding: ${(0,l.D0)("20px")} ${(0,l.D0)("40px")};
                    }
                }
                >.bottom {
                    margin-top: ${o};
                    border: ${s};
                    padding: ${(0,l.D0)("20px")} ${(0,l.D0)("40px")};
                
                    .fake-item {
                        height: 32px;
                        line-height: 32px;
                    }
                    .chunk {
                        border: ${s};
                        padding: ${(0,l.D0)("10px")};
                        margin-top: 10px;
                    }
                
                }
            }

            >.step-g {
                height: 40vh;
                .header {
                    display: flex;
                    justify-content: space-between;
                    margin-bottom: 2vw;
                }
                
            }
            >.step-h {
                width: 100%;
                .table-layout {
                    width: 40vw;
                }
                
                
            }
        }
    }
    >.footer-btns {
        display: flex;
        justify-content: center;
        padding: ${(0,l.D0)("20px")} 0;
        border-top: ${s};

        button {
            margin-right: ${(0,l.D0)("10px")};
        }
    }
`,c=t.Ay.div`
    width: 60%;
    position: relative;
    ${e=>`border-top: ${e.thickness}px ${e.border||"solid"}   ${e.color||"black"};`}
      
    &::before {
        content: "${e=>e.sign}";
        position: absolute; 
        color: ${e=>e.color||"black"};
        top: -2px; 
        left: 20%; 
        transform: translate(-50%, -50%); 
    }

    &::after {
        content: "${e=>e.sign}";
        position: absolute; 
        color: ${e=>e.color||"black"};
        top: -2px; 
        left: 80%; 
        transform: translate(-50%, -50%); 
    }
`,u=t.Ay.div`
    background-color: #fff;
    padding: 10px;
    .title {
        font-size: 13px;
        font-weight: bold;
        margin-bottom: ${(0,l.D0)("15px")};
    }
    >.footer-btns {
        display: flex;
        justify-content: center;
        padding: ${(0,l.D0)("20px")} 0;
        border-top: ${s};
        button {
            margin-right: ${(0,l.D0)("10px")};
        }
    }
    .chunk {
        border: ${s};
        padding: ${(0,l.D0)("10px")};
        margin-top: 10px;
    }

`,x=t.Ay.div`
    width: 100%;
    >.container {
        display: flex;
        flex-direction: column;
        width: 100%;
        .title {
            font-size: 13px;
            font-weight: bold;
            margin-bottom: ${(0,l.D0)("15px")};
        }
        >.center {
            align-items: center;
            justify-content: center;
        }
        >.top {
            height: auto;
            display: flex;
            flex-direction: column;
            width: 100%;
            border: ${s};
            margin-top: 10px;
            padding: ${(0,l.D0)("20px")} ${(0,l.D0)("40px")};
        }
    }
    >.footer-btns {
        display: flex;
        justify-content: center;
        margin-top: ${(0,l.D0)("40px")};
        button {
            margin-right: ${(0,l.D0)("10px")};
        }
    }

`},8829:(e,n,i)=>{i.d(n,{A:()=>o});var t=i(65043),l=i(80077),r=i(32099);const s=()=>(0,r.Mz)([e=>e.inputVariable.inputVariableMap,e=>e.inputVariable.selectCodeList],((e,n)=>n.map((n=>e.get(n))))),o=()=>{const e=(0,t.useMemo)(s,[]);return(0,l.d4)((n=>e(n)))}},16204:(e,n,i)=>{i.d(n,{A:()=>p});i(65043);var t=i(75440),l=i(25055),r=i(83720),s=i(6051),o=i(74117),a=i(4554),d=i(40940),c=(i(34458),i(81143));i(68374);const u=c.Ay.div`
    display: flex;
    justify-content: space-between;
    height: 10vh;
    .upload-modal-left {
        width: 70%;
        min-width: 0; /* 允许子元素收缩 */
    }
    .upload-modal-right {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        width: 9vw;
        flex-shrink: 0; /* 防止按钮区域被压缩 */
    }
    .path-layout {
        display: flex;
        align-items: center;
        margin-bottom: 16px;
        
        > div:first-child {
            flex-shrink: 0; /* 标签文字不压缩 */
            margin-right: 8px;
        }
        
        .ant-upload-wrapper {
            flex: 1;
            min-width: 0; /* 允许上传组件收缩 */
            
            .ant-upload-list {
                .ant-upload-list-item {
                    .ant-upload-list-item-name {
                        max-width: 200px; /* 限制文件名最大宽度 */
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                    }
                }
            }
        }
    }
`;var x=i(70579);const p=e=>{let{open:n,onCancel:i,onOk:c,title:p="\u5bfc\u51fa\u9879\u76ee",pathName:v="\u5bfc\u51fa\u8def\u5f84",defaultPath:h="",defaultFileName:f=""}=e;const{t:g}=(0,o.Bd)(),[m]=l.A.useForm();return(0,x.jsx)(t.A,{open:n,title:g(p),width:600,onCancel:i,footer:null,children:(0,x.jsxs)(u,{children:[(0,x.jsx)("div",{className:"upload-modal-left",children:(0,x.jsxs)(l.A,{form:m,initialValues:{path:h,fileName:f},children:[(0,x.jsx)(l.A.Item,{label:g(v),name:"path",rules:[{required:!0,message:g("\u8bf7\u9009\u62e9\u8def\u5f84")}],children:(0,x.jsx)(d.A,{})}),(0,x.jsx)(l.A.Item,{label:g("\u6587\u4ef6\u540d\u79f0"),name:"fileName",rules:[{required:!0,message:g("\u8bf7\u8f93\u5165\u6587\u4ef6\u540d\u79f0")},{whitespace:!0,message:g("\u6587\u4ef6\u540d\u79f0\u4e0d\u80fd\u4e3a\u7a7a")}],children:(0,x.jsx)(r.A,{placeholder:g("\u8bf7\u8f93\u5165\u5bfc\u51fa\u6587\u4ef6\u540d\u79f0"),style:{width:"100%"}})})]})}),(0,x.jsx)("div",{className:"upload-modal-right",children:(0,x.jsxs)(s.A,{direction:"vertical",children:[(0,x.jsx)(a.A,{block:!0,onClick:async()=>{try{const e=await m.validateFields();c(e.path,e.fileName)}catch(e){console.log("\u8868\u5355\u9a8c\u8bc1\u5931\u8d25:",e)}},children:g("\u786e\u8ba4")}),(0,x.jsx)(a.A,{block:!0,onClick:i,children:g("\u53d6\u6d88")})]})})]})})}},20871:(e,n,i)=>{i.d(n,{A:()=>o});var t=i(65043),l=i(80077),r=i(32099);const s=()=>(0,r.Mz)([e=>e.inputVariable.inputVariableMap,e=>e.inputVariable.doubleArrayListCodeList],((e,n)=>n.map((n=>e.get(n))))),o=()=>{const e=(0,t.useMemo)(s,[]);return(0,l.d4)((n=>e(n)))}},29534:(e,n,i)=>{i.d(n,{A:()=>r});i(65043);var t=i(4136),l=i(70579);const r=e=>{let{color:n,thickness:i,border:r,sign:s}=e;return(0,l.jsx)(t.N1,{color:n,thickness:i,border:r,sign:s})}},30263:(e,n,i)=>{i.d(n,{w:()=>l});var t=i(754);const l=()=>{var e;const n=t.A.getState().project.sampleData;return null===n||void 0===n||null===(e=n.map((e=>e.children)))||void 0===e?void 0:e.flat()}},41234:(e,n,i)=>{i.d(n,{A:()=>Hi});var t=i(65043),l=i(80077),r=i(16569),s=i(71424),o=i(53536),a=i(81143);const d=a.Ay.div`
    width: 100%;
    height: 100%;
    background: #fff;
    overflow: hidden;
`;var c=i(70588),u=i(6057);const x="dashed",p="dotted",v="extend",h="min-extend",f="all",g="data-range",m="last-range",y={"\u5de6\u79fb\u4e00\u4e2a":"ArrowLeft","\u5de6\u79fb\u5341\u4e2a":"ArrowDown","\u53f3\u79fb\u4e00\u4e2a":"ArrowRight","\u53f3\u79fb\u5341\u4e2a":"ArrowUp"},A={CTRL_LEFT:"ctrl+left",CTRL_RIGHT:"ctrl+right"},j="\u25a1",b="\u25bd";class k{constructor(e,n){this.chart=e,this.annotations=new Map,this.visible=!0,this.onPointMarkerPositionChangeRef=n||null}createAnnotation(e){const{id:n,x:i,y:t,content:l,lineInstance:r,isLine:s=!0,isChunk:o=!0,color:a="#000000",style:d={},position:c=null}=e;if(!r)return console.error("\u521b\u5efa\u6807\u6ce8\u65f6\u5fc5\u987b\u63d0\u4f9b lineInstance \u53c2\u6570"),null;const x=r.axisX,p=r.axisY;this.annotations.has(n)&&(console.warn(`\u6807\u6ce8ID "${n}" \u5df2\u5b58\u5728\uff0c\u5c06\u8986\u76d6\u539f\u6709\u6807\u6ce8`),this.removeAnnotation(n));const v=c?c.x:i,h=c?c.y:t+(d.offsetY||1);let f=null;if(s){const e=[{x:i,y:t},{x:v,y:h}];f=this.chart.addLineSeries({dataPattern:"ProgressiveX"}).setName(`\u8fde\u63a5\u7ebf-${n}`).setStrokeStyle(new u.o_e({thickness:d.lineThickness||1,fillStyle:new u.ZUH({color:d.lineColor||(0,u.M_8)(128,128,128)})})).add(e).setCursorEnabled(!1).setHighlightOnHover(!1).setEffect(!1)}const g=this.chart.addUIElement(u.taT.TextBox,{x:x,y:p}).setText(l).setTextFillStyle(new u.ZUH({color:(0,u.pLd)(a)})).setPosition({x:v,y:h}).setOrigin({x:0,y:0}).setMargin({left:d.marginLeft||8,right:d.marginRight||8,top:d.marginTop||4,bottom:d.marginBottom||4}).setDraggingMode(u.KNV.draggable);o||g.setBackground((e=>e.setStrokeStyle(new u.o_e({thickness:0,fillStyle:new u.ZUH({color:(0,u.M_8)(0,0,0,0)})}))));const m=g.onMouseDragStart((e=>{f&&f.setVisible(!1)})),y=g.onMouseDragStop((e=>{const l=g.getPosition();if(f){const e=l.y,r=this.annotations.get(n),s=[{x:r?r.originalX:i,y:r?r.originalY:t},{x:l.x,y:e}];f.clear().add(s),f.setVisible(!0)}this.onPointMarkerPositionChangeRef&&this.onPointMarkerPositionChangeRef.current({id:n,position:{x:l.x,y:l.y},originalPosition:{x:i,y:t}})})),A={id:n,originalX:i,originalY:t,annotationX:v,annotationY:h,connectionLine:f,annotationBox:g,dragStartToken:m,dragStopToken:y,visible:!0};return this.annotations.set(n,A),this.visible||this.hideAnnotation(n),A}removeAnnotation(e){const n=this.annotations.get(e);return!!n&&(n.connectionLine&&n.connectionLine.dispose(),n.annotationBox&&n.annotationBox.dispose(),this.annotations.delete(e),!0)}getAnnotationPosition(e){const n=this.annotations.get(e);if(!n)return null;const i=n.annotationBox.getPosition();return{id:e,size:n.annotationBox.getSize(),currentPosition:{x:i.x,y:i.y},originalPosition:{x:n.originalX,y:n.originalY}}}getAllAnnotationPositions(){const e=[];return this.annotations.forEach(((n,i)=>{const t=this.getAnnotationPosition(i);t&&e.push(t)})),e}updateAnnotationPosition(e,n,i,t){let l=arguments.length>4&&void 0!==arguments[4]?arguments[4]:null;const r=this.annotations.get(e);if(!r)return!1;if(!i||n>=i.length||n<0)return console.warn(`\u65e0\u6548\u7684\u6570\u636e\u70b9\u4e0b\u6807: ${n}`),!1;const s=i[n],{x:o}=s,{y:a}=s;r.originalX=o,r.originalY=a,void 0!==t&&null!==t&&(r.annotationBox.setText(t),r.title=t);const d=l?l.x:o,c=l?l.y:a+1,u=c;if(r.annotationBox.setPosition({x:d,y:c}),r.connectionLine){const e=[{x:o,y:a},{x:d,y:u}];r.connectionLine.clear().add(e)}return!0}showAnnotation(e){const n=this.annotations.get(e);return!!n&&(n.connectionLine&&n.connectionLine.setVisible(!0),n.annotationBox.setVisible(!0),n.visible=!0,!0)}hideAnnotation(e){const n=this.annotations.get(e);return!!n&&(n.connectionLine&&n.connectionLine.setVisible(!1),n.annotationBox.setVisible(!1),n.visible=!1,!0)}showAllAnnotations(){this.visible=!0,this.annotations.forEach((e=>{e.connectionLine&&e.connectionLine.setVisible(!0),e.annotationBox.setVisible(!0),e.visible=!0}))}hideAllAnnotations(){this.visible=!1,this.annotations.forEach((e=>{e.connectionLine&&e.connectionLine.setVisible(!1),e.annotationBox.setVisible(!1),e.visible=!1}))}toggleAllAnnotations(){this.visible?this.hideAllAnnotations():this.showAllAnnotations()}getAnnotation(e){return this.annotations.get(e)}getAllAnnotations(){return Array.from(this.annotations.values())}dispose(){this.annotations.forEach((e=>{e.connectionLine&&e.connectionLine.dispose(),e.annotationBox&&e.annotationBox.dispose()})),this.annotations.clear()}getAnnotationCount(){return this.annotations.size}hasAnnotation(e){return this.annotations.has(e)}}const C=e=>{const n=(0,u.pLd)(e.line_color),i=e.thickness||2;return"dashed"===e.pattern||"dashed"===e.line_type?new u.Gp6({thickness:i,fillStyle:new u.ZUH({color:n}),patternScale:3}):"dotted"===e.pattern||"dotted"===e.line_type?new u.Gp6({thickness:i,fillStyle:new u.ZUH({color:n}),patternScale:1}):new u.o_e({thickness:i,fillStyle:new u.ZUH({color:n})})},S=(e,n,i,t)=>{const l=Object.keys(e),r=Object.keys(n);if(0===l.length||0===r.length)return{xMin:0,xMax:100,yMin:0,yMax:100};let s,o;s=i&&e[i]?e[i]:e[l[0]],o=t&&n[t]?n[t]:n[r[0]];const a=s.getInterval(),d=o.getInterval();return{xMin:a.start,xMax:a.end,yMin:d.start,yMax:d.end}},T=(e,n,i)=>{const{xMin:t,xMax:l,yMin:r,yMax:s}=i,o=e.y-n*e.x,a=[],d=n*t+o;d>=r&&d<=s&&a.push({x:t,y:d});const c=n*l+o;if(c>=r&&c<=s&&a.push({x:l,y:c}),0!==n){const e=(r-o)/n;e>=t&&e<=l&&a.push({x:e,y:r})}if(0!==n){const e=(s-o)/n;e>=t&&e<=l&&a.push({x:e,y:s})}0===n&&e.y>=r&&e.y<=s&&(a.push({x:t,y:e.y}),a.push({x:l,y:e.y}));const u=a.filter(((e,n,i)=>n===i.findIndex((n=>Math.abs(n.x-e.x)<.001&&Math.abs(n.y-e.y)<.001))));return u.length>=2?{start:u[0],end:u[u.length-1]}:{start:{x:t,y:n*t+o},end:{x:l,y:n*l+o}}},w=function(e,n){let i,t,l=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};const r=n=>{if(void 0!==n.index){const i=l[e.lineId];return i?((e,n)=>e&&Array.isArray(e)&&e.find((e=>e.index===n))||null)(i,n.index):null}return void 0!==n.x&&void 0!==n.y?{x:n.x,y:n.y}:null};if("straight"===e.type){if("segment"===e.configType){const l=r(e.data[0]),s=r(e.data[1]);if(!l||!s)return{startPoint:null,endPoint:null};const o=(s.y-l.y)/(s.x-l.x),a=T(l,o,n);i=a.start,t=a.end}else if("slopeStraight"===e.configType){const l=e.data[0],s=r(l);if(!s)return{startPoint:null,endPoint:null};const o=l.slope||l.m,a=T(s,o,n);i=a.start,t=a.end}else if("xStraight"===e.configType){const{x:l}=e.data[0],r=((e,n)=>{const{yMin:i,yMax:t}=n;return{start:{x:e,y:i},end:{x:e,y:t}}})(l,n);i=r.start,t=r.end}}else if("segment"===e.type&&"segment"===e.configType){const n=r(e.data[0]),l=r(e.data[1]);if(!n||!l)return{startPoint:null,endPoint:null};i=n,t=l}return{startPoint:i,endPoint:t}},I=function(e,n,i,t){let l=arguments.length>4&&void 0!==arguments[4]?arguments[4]:{},r=arguments.length>5&&void 0!==arguments[5]?arguments[5]:{},s=arguments.length>6&&void 0!==arguments[6]?arguments[6]:null;const o=n[e];if(!o||!o.line)return console.warn(`\u8f85\u52a9\u7ebf ${e} \u4e0d\u5b58\u5728`),!1;if(s&&(o.config={...o.config,...s},s.style)){const e=C(s.style);o.line.setStrokeStyle(e)}const{line:a,config:d}=o,c=S(i,t,d.xAxisId,d.yAxisId),{startPoint:u,endPoint:x}=w(d,c,l,r);return!(!u||!x)&&(a.clear(),a.add([u,x]),!0)},L=function(e,n,i){let t=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};Object.values(e).forEach((e=>{!function(e,n,i){let t=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};const{line:l,config:r}=e;if(!l)return;const s=S(n,i,r.xAxisId,r.yAxisId),{startPoint:o,endPoint:a}=w(r,s,t);o&&a&&(l.clear(),l.add([o,a]))}(e,n,i,t)}))},M=function(e,n,i){let t=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};const{line:l,config:r}=e;if(!l)return;if(r.style){const e=C(r.style);l.setStrokeStyle(e)}const s=S(n,i,r.xAxisId,r.yAxisId),{startPoint:o,endPoint:a}=w(r,s,t);o&&a&&(l.clear(),l.add([o,a]))},N=function(e,n,i){let t=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};Object.values(e).forEach((e=>{M(e,n,i,t)}))},$=e=>{let{chartXY:n,id:i,showTitle:t,title:l,color:r,content:s,position:o,onChunkMarkerPositionChangeRef:a}=e;const d=n.addUIElement(u.Vir.Column).setOrigin(u.s2.RightTop).setPosition(null!==o&&void 0!==o?o:{x:100,y:100}).setMargin(10).setVisible(!1).setDraggingMode(u.KNV.draggable);return t&&d.addElement(u.taT.TextBox).setText(l).setTextFillStyle(new u.ZUH({color:(0,u.pLd)(r)})),s.forEach(((e,n)=>{d.addElement(u.taT.TextBox).setText(e).setTextFillStyle(new u.ZUH({color:(0,u.pLd)(r)}))})),d.onMouseDragStop((e=>{const n=d.getPosition();console.log("currentPosition",n),a&&a.current({id:i,position:{x:n.x,y:n.y}})})),d},E=e=>{let{lineType:n,thickness:i,color:t}=e;switch(n){case x:return new u.Gp6({thickness:i,fillStyle:new u.ZUH({color:(0,u.pLd)(t||"#000000")}),patternScale:2});case p:return new u.Gp6({thickness:i,fillStyle:new u.ZUH({color:(0,u.pLd)(t||"#000000")}),patternScale:1});default:return new u.o_e({thickness:i,fillStyle:new u.ZUH({color:(0,u.pLd)(t||"#000000")})})}},R=(e,n)=>{const{id:i,title:t,style:{...l},gridLine:{open:r,...s},zeroLine:{open:o,...a},interval:{start:d=0,end:c=10,isLog:x}}=n;e.id=i,t&&e.setTitle(t),l&&e.setStrokeStyle(E(l)),x||e.setInterval({start:d,end:c}),o&&e.addConstantLine().setMouseInteractions(!1).setValue(0).setStrokeStyle(E(a)),e.setTickStrategy(u.e4v.Numeric,(e=>{const n=r?E(s):u.io8;return e.setMinorTickStyle((e=>e.setGridStrokeStyle(n))).setMajorTickStyle((e=>e.setGridStrokeStyle(n)))}))},O=e=>{switch(e){case b:return u.uZd.Triangle;case j:return u.uZd.Square;default:return u.uZd.Circle}},P=(e,n,i,t)=>{const{chart:l,xAxis:r,yAxis:s,lines:o,markerPoint:a,markerChunk:d,legend:c,auxiliary:x}=n;e.getDefaultAxisX().dispose(),e.getDefaultAxisY().dispose();let p={},v={},h={},f={},g={},m={},y={},A={},j={};if(((e,n)=>{const{title:i}=n;e.setTitle(i)})(e,l),r){s.some((e=>e.interval.islog));p=((e,n)=>Object.fromEntries(n.map((n=>{const{id:i,interval:{isLog:t}}=n,l=e.addAxisX({type:t?"logarithmic":"linear",base:10});return R(l,n),[i,l]}))))(e,r)}if(s){r.some((e=>e.interval.islog));v=((e,n)=>Object.fromEntries(n.map(((n,i)=>{const{id:t,interval:{isLog:l}}=n,r=e.addAxisY({type:l?"logarithmic":"linear",base:10,opposite:1===i});return R(r,n),[t,r]}))))(e,s)}p&&v&&o&&(h=(e=>{let{chartXY:n,xAxisMap:i,yAxisMap:t,lines:l}=e;const r={};return l.forEach((e=>{const{id:l,title:s,xAxisId:o,yAxisId:a,xRatio:d,yRatio:c,xOffset:x,yOffset:p,xName:v,yName:h,style:{isLine:f,color:g="#000",thickness:m,lineStyle:y,isSign:A,signStyle:j,signEach:b}}=e,k=i[o],C=t[a];let S;S=A?n.addPointLineSeries({xAxis:k,yAxis:C,pointShape:O(j)}).setIndividualPointSizeEnabled(!b).setPointFillStyle(new u.ZUH({color:(0,u.pLd)(g||"#000")})):n.addLineSeries({xAxis:k,yAxis:C}),S.setName(s).setStrokeStyle(f?E({lineType:y,thickness:m,color:g}):u.io8).setCursorEnabled(!1).setHighlight(!1).setHighlightOnHover(!1).setEffect(!1),S.id=l,S.xAxisId=o,S.yAxisId=a,S.xRatio=d,S.yRatio=c,S.xOffset=x,S.yOffset=p,S.xName=v,S.yName=h,r[l]=S})),r})({chartXY:e,xAxisMap:p,yAxisMap:v,lines:o})),p&&v&&x&&(j=((e,n,i,t)=>{const l={};if(!n||0===n.length)return l;try{n.forEach((n=>{const{id:r,type:s,configType:o,data:a,style:d,xAxisId:c,yAxisId:u}=n,x=e.addLineSeries({dataPattern:"ProgressiveX",xAxis:i[c],yAxis:t[u]});x.setStrokeStyle(C(d)),x.setCursorEnabled(!1),x.setHighlightOnHover(!1),x.setEffect(!1),l[r]={id:r,line:x,config:n,type:s,configType:o,data:a,style:d,xAxisId:c,yAxisId:u}}))}catch(r){console.log("initAuxiliaryLines error:",r)}return l})(e,x,p,v)),h&&(f=((e,n,i)=>{if(null===i||void 0===i||!i.open)return null;const t=e.addLegendBox().setTitle("").setOrigin(u.s2.RightTop).setPosition({x:100,y:100}).setMargin(10).setDraggingMode(u.KNV.draggable).setVisible(!0);return Object.values(n).forEach((e=>{t.add(e,{toggleVisibilityOnClick:!1})})),t})(e,h,c)),h&&(g=(e=>{const n=e.addUIElement(u.Vir.Column).setOrigin(u.s2.RightTop).setPosition({x:100,y:100}).setMargin(10).setVisible(!1),i=n.addElement(u.taT.TextBox).setText("\u5341\u5b57\u7ebf"),t=n.addElement(u.Vir.Row).addElement(u.taT.TextBox),l=n.addElement(u.Vir.Row).addElement(u.taT.TextBox),r=n.addElement(u.Vir.Row).addElement(u.taT.TextBox);return{layout:n,titleElement:i,pointIndexText:t,pointXText:l,pointYText:r}})(e),m=(e=>{const n=u.nSh.XY.setPointMarker(u.m2n.UICircle).setResultTableBackground(u.ItK.Rectangle).addStyler((e=>e.setPointMarker((e=>e.setSize({x:5,y:5}))))),i=Object.fromEntries(Object.entries(e).map((e=>{let[i,t]=e;return[i,t.addMarker(n).setResultTableVisibility(!1).setMouseInteractions(!1).setTickMarkerXVisibility(!1).setTickMarkerYVisibility(!1).setVisible(!1)]})));return i})(h));let b=null;if(h&&a){const n=((e,n,i,t,l,r)=>{const s={},o=new k(i,r);try{null===e||void 0===e||e.forEach((e=>{let{id:i,lineId:t,title:l,pointIndex:r,isLine:o=!0,isChunk:a=!0,color:d="#000000",position:c}=e;const u=n[t];u?s[i]={pointIndex:r,lineId:t,title:l,lineInstance:u,isLine:o,isChunk:a,color:d,position:c}:console.warn("\u672a\u627e\u5230\u7ebf\u6761\u5b9e\u4f8b:",t)}))}catch(a){console.log("initLineMarkerPoint error:",a)}return{markerPointMap:s,pointTagManager:o}})(a,h,e,0,0,i);y=n.markerPointMap,b=n.pointTagManager}return h&&A&&(A=((e,n,i)=>{const t={};try{null===e||void 0===e||e.forEach((e=>{let{id:l,content:r,color:s="#000000",showBorder:o=!0,title:a,showTitle:d,position:c}=e;const u=$({chartXY:n,id:l,showTitle:d,title:a,color:s,content:r,position:c,onChunkMarkerPositionChangeRef:i});t[l]={id:l,chunkContainer:u,color:s,showBorder:o}}))}catch(l){console.log("initLineMarkerChunk error:",l)}return t})(d,e,t)),{xAxisMap:p,yAxisMap:v,linesMap:h,legend:f,resultLabel:g,lineCrossMap:m,markerPointMap:y,markerChunkMap:A,pointTagManager:b,auxiliaryLinesMap:j}},_=e=>{let{point:n,line:i,lineCross:t,resultLabel:{titleElement:l,pointIndexText:r,pointXText:s,pointYText:o},lineCrossIndexRef:a}=e;t.setPosition(n),l.setText(`\u5341\u5b57\u7ebf(${null===i||void 0===i?void 0:i.getName()})`),r.setText(`\u7d22\u5f15: ${n.index}`),s.setText(`${(null===i||void 0===i?void 0:i.xName)||i.axisX.getTitle()}: ${null===n||void 0===n?void 0:n.x}`),o.setText(`${(null===i||void 0===i?void 0:i.yName)||i.axisY.getTitle()}: ${null===n||void 0===n?void 0:n.y}`),a.current=n.index},V={[y.\u53f3\u79fb\u4e00\u4e2a]:1,[y.\u53f3\u79fb\u5341\u4e2a]:10,[y.\u5de6\u79fb\u4e00\u4e2a]:-1,[y.\u5de6\u79fb\u5341\u4e2a]:-10},B={ArrowLeft:-1,ArrowRight:1},D=e=>{var n;let{targetLineId:i,lineCrossIdRef:t,lineCrossIndexRef:l,lineCrossMap:r,resultLabel:s,lineMap:o,lineDataMap:a,breakPointRef:d,onHighlightChange:c}=e;const u=t.current;var x;t.current&&(null===(x=r[t.current])||void 0===x||x.setVisible(!1));const p=r[i];null===p||void 0===p||p.setVisible(!0),t.current=i,c&&"function"===typeof c&&c(i,u),s.layout.setVisible(!0);const v=o[i],h=null===a||void 0===a?void 0:a[i];if(!h||0===h.length)return;const f=null===d||void 0===d||null===(n=d.current)||void 0===n?void 0:n[i];let g,m;void 0!==f&&null!==f&&f<h.length?(g=h[f],m=f):(g=h.at(-1),m=h.length-1),l.current=m,_({point:g,line:v,lineCross:p,resultLabel:s,lineCrossIndexRef:l})},G={chart:{title:"\u6807\u9898"},xAxis:[{id:"x1",title:"\u53d8\u5f62",style:{thickness:2,lineType:"solid",color:"#000000"},gridLine:{open:!1,thickness:1,color:"#000000",lineType:"solid"},zeroLine:{open:!1,thickness:1,color:"#000000",lineType:"solid"},interval:{proportionType:"data-range",start:0,end:100,isLog:!1,intervalNumber:0,lastRange:10}}],yAxis:[{id:"y1",title:"\u53d8\u5f62",style:{thickness:2,lineType:"solid",color:"#000000"},gridLine:{open:!1,thickness:1,color:"#000000",lineType:"solid"},zeroLine:{open:!1,thickness:1,color:"#000000",lineType:"solid"},interval:{proportionType:"extend",start:0,end:10,isLog:!1,intervalNumber:0,lastRange:10}}],lines:[{id:"1",title:"aaaaaaa",xAxisId:"x1",xUnit:"",xRatio:1,yAxisId:"y1",yUnit:"",yRatio:1,xOffset:0,yOffset:0,style:{isLine:!0,color:"#c73232",thickness:1,lineStyle:"solid",isSign:!1,signStyle:"",signEach:!1}}],markerPoint:[{id:"111",lineId:"1",title:"1111",isLine:!0,isChunk:!0,color:"#000",pointIndex:10}],markerChunk:[{id:"111",color:"red",showBorder:!0,content:["11111","11111","11111"]}],legend:{open:!0},breakPoint:{},auxiliary:[]},F=(e,n,i)=>{const{start:t,end:l}=e.getInterval();if(n.end>i.max||l>i.max)return{start:t,end:l};const r=t;let s=l,o=!0,a=0;const d={start:t,end:l};for(;o;){const e=s-r,n=r+.95*e;i.max>n?(s=r+1.3*e,d.end=s,a+=1):o=!1}return d},X=(e,n,i)=>{const{start:t,end:l}=e.getInterval(),r={start:t,end:l};if(n.start<i.min||t<i.min)return{start:t,end:l};let s=t;const o=l;let a=!0;let d=0;for(;a&&d<50;){const e=o-s,n=s+.05*e;i.min<n?(s=o-1.3*e,r.start=s,d+=1):a=!1}return r},Y=e=>{let{type:n,interval:i,lineInterval:t,axis:l}=e;if(t)switch(n){case v:l.setInterval(F(l,i,t));break;case h:l.setInterval(X(l,i,t));break;case f:l.setInterval(((e,n,i)=>{const t={...n},l=F(e,n,i),r=X(e,n,i);return t.start=r.start,t.end=l.end,t})(l,i,t));break;case g:{if(t.max===t.min){l.setInterval({start:t.min-5,end:t.max+5});break}const e=t.max-t.min;l.setInterval({start:t.min-.1*e,end:t.max+.1*e});break}case m:l.setInterval({start:t.last-(null===i||void 0===i?void 0:i.lastRange),end:t.last})}},U=e=>{let{isX:n,option:i,axis:t,lines:l}=e;const{proportionType:r,start:s,end:o,isLog:a,intervalNumber:d,lastRange:c}=i.interval;if(a)return;const u=(e=>{if(!e||0===e.length)return null;const n=e.map((e=>{var n,i,t,l;return 0===e.getPointAmount()?null:{XMax:e.getXMax(),XMin:e.getXMin(),YMax:e.getYMax(),YMin:e.getYMin(),lastX:null!==(n=null===(i=e.getLastPoint())||void 0===i?void 0:i.x)&&void 0!==n?n:0,lastY:null!==(t=null===(l=e.getLastPoint())||void 0===l?void 0:l.y)&&void 0!==t?t:0}}));return n.every((e=>null===e))?null:{XMax:Math.max(...n.filter((e=>null!==e)).map((e=>e.XMax))),XMin:Math.min(...n.filter((e=>null!==e)).map((e=>e.XMin))),YMax:Math.max(...n.filter((e=>null!==e)).map((e=>e.YMax))),YMin:Math.min(...n.filter((e=>null!==e)).map((e=>e.YMin))),lastX:Math.max(...n.filter((e=>null!==e)).map((e=>e.lastX))),lastY:Math.min(...n.filter((e=>null!==e)).map((e=>e.lastY)))}})(l);Y({type:r,interval:{start:s,end:o,lastRange:c},lineInterval:u&&{min:n?u.XMin:u.YMin,max:n?u.XMax:u.YMax,last:n?u.lastX:u.lastY},axis:t})},z=e=>{let{option:n,xAxisMapRef:i,yAxisMapRef:t,lineMapRef:l}=e;try{const{xAxis:e,yAxis:r,lines:s}=n;e.forEach((e=>{U({isX:!0,option:e,axis:i.current[e.id],lines:s.filter((n=>n.xAxisId===e.id)).map((e=>l.current[e.id]))})})),r.forEach((e=>{U({isX:!1,option:e,axis:t.current[e.id],lines:s.filter((n=>n.yAxisId===e.id)).map((e=>l.current[e.id]))})}))}catch(r){console.log("err",r)}};var H=i(68374);const W=a.Ay.div`
    display: flex;
    height: 100%;
    width: 100%;
    position: relative;
`,K=(e,n)=>{try{var i,t,l,r;const s=null!==(i=e.xRatio)&&void 0!==i?i:1,o=null!==(t=e.yRatio)&&void 0!==t?t:1,a=null!==(l=e.xOffset)&&void 0!==l?l:0,d=null!==(r=e.yOffset)&&void 0!==r?r:0;let c=n;return 0===a&&0===d||(c=n.map((e=>({...e,x:e.x+a,y:e.y+d})))),c.map((e=>({...e,x:e.x*s,y:e.y*o})))}catch(s){return console.log("error",s),n}};var Z=i(70579);const q=(e,n)=>{let{option:i=G,highlightLineId:l,crossPercent:r,onCrossMove:s=()=>{},onAnnotationPositionChange:o=e=>console.log("a",e),onChunkMarkerPositionChange:a=e=>console.log("a",e)}=e;const d=(0,t.useRef)(),x=(0,t.useRef)(null),p=(0,t.useRef)(null),v=(0,t.useRef)(null),h=(0,t.useRef)(null),f=(0,t.useRef)(null),g=(0,t.useRef)(null),j=(0,t.useRef)(null),b=(0,t.useRef)(null),k=(0,t.useRef)(null),S=(0,t.useRef)(null),T=(0,t.useRef)({layout:null,titleElement:null,pointIndexText:null,pointXText:null,pointYText:null}),w=(0,t.useRef)(null),E=(0,t.useRef)(null),R=(0,t.useRef)(),O=(0,t.useRef)(null),F=(0,t.useRef)(!0),X=(0,t.useRef)({x:{},y:{}}),Y=(0,t.useRef)({}),U=(0,t.useRef)();(0,t.useEffect)((()=>{U.current=s}),[s]);const H=(0,t.useRef)();(0,t.useEffect)((()=>{H.current=o}),[o]);const q=(0,t.useRef)();(0,t.useEffect)((()=>{q.current=a}),[a]);(0,t.useImperativeHandle)(n,(()=>({clearLine:e=>{var n,i,t,l;null!==(n=h.current)&&void 0!==n&&n[e]?(f.current[e]=[],g.current[e]=[],null===(i=h.current)||void 0===i||null===(t=i[e])||void 0===t||null===(l=t.clear)||void 0===l||l.call(t)):console.warn("\u5728\u4e0d\u5b58\u5728\u7684\u66f2\u7ebf\u6e05\u9664\u6570\u636e\uff1a",e)},clearAllLine:()=>{Object.values(h.current).forEach((e=>{var n;f.current[e.id]=[],g.current[e.id]=[],null===e||void 0===e||null===(n=e.clear)||void 0===n||n.call(e)}))},lineAdd:(e,n)=>{var t,l,r;if(null===(t=h.current)||void 0===t||!t[e])return void console.warn("\u5728\u4e3a\u4e0d\u5b58\u5728\u7684\u66f2\u7ebf\u6dfb\u52a0\u70b9\uff1a",e);const s=n.map(((n,i)=>{const t=f.current[e].length+i;return{index:f.current[e].length+i,size:t%10===0?5:1,...n}}));g.current[e].push(...s);const o=K(h.current[e],s);f.current[e].push(...o);const a=Y.current[e],d=null===(l=i.xAxis.find((n=>n.id===h.current[e].xAxisId)))||void 0===l?void 0:l.interval,c=null===(r=i.yAxis.find((n=>n.id===h.current[e].yAxisId)))||void 0===r?void 0:r.interval;if((null===d||void 0===d?void 0:d.proportionType)===m||(null===c||void 0===c?void 0:c.proportionType)===m){var u,x,p,v;const n=f.current[e].at(-1);let i,t;d.proportionType===m&&(i=f.current[e].findIndex((e=>e.x>=n.x-d.lastRange))),c.proportionType===m&&(t=f.current[e].findIndex((e=>e.y>=n.y-c.lastRange)));const l=Math.max(i,t);g.current[e]=g.current[e].slice(l),f.current[e]=f.current[e].slice(l),null===(u=h.current)||void 0===u||u[e].clear(),null===(x=h.current)||void 0===x||null===(p=x[e])||void 0===p||null===(v=p.add)||void 0===v||v.call(p,f.current[e])}else if(a){let n=f.current[e];var y,A,j;if(void 0!==a&&a>=0&&(n=f.current[e].slice(0,a+1)),n.length>0)null===(y=h.current)||void 0===y||null===(A=y[e])||void 0===A||null===(j=A.add)||void 0===j||j.call(A,n)}else{var b,k,C;null===(b=h.current)||void 0===b||null===(k=b[e])||void 0===k||null===(C=k.add)||void 0===C||C.call(k,s)}((e,n)=>{if(!w.current||!E.current)return;Object.entries(w.current).filter((n=>{let[,i]=n;return i.lineId===e})).forEach((e=>{let[i,{pointIndex:t,title:l,lineInstance:r,isLine:s,isChunk:o,color:a,position:d}]=e;const c=n.find((e=>e.index===t));c&&E.current.createAnnotation({id:i,x:c.x,y:c.y,content:l,lineInstance:r,isLine:s,isChunk:o,color:a,position:d,style:{offsetY:1,lineThickness:1,marginLeft:8,marginRight:8,marginTop:4,marginBottom:4}})}))})(e,s),F.current&&ee()},openCross:e=>{var n;const i=null!==(n=null!==e&&void 0!==e?e:l)&&void 0!==n?n:Object.keys(h.current)[0];D({targetLineId:i,lineCrossIdRef:k,lineCrossIndexRef:S,lineCrossMap:b.current,resultLabel:T.current,lineMap:h.current,lineDataMap:f.current,onHighlightChange:(e,n)=>{var i,t,l,r,s,o,a,d;n&&h.current[n]&&(null===(i=(t=h.current[n]).setHighlightOnHover)||void 0===i||i.call(t,!1),null===(l=(r=h.current[n]).setEffect)||void 0===l||l.call(r,!1));e&&h.current[e]&&(null===(s=(o=h.current[e]).setHighlightOnHover)||void 0===s||s.call(o,!0),null===(a=(d=h.current[e]).setEffect)||void 0===a||a.call(d,!0))}})},closeCross:()=>{var e,n,i,t,r,s,o,a,d;(null===(e=b.current[k.current])||void 0===e||e.setVisible(!1),k.current&&h.current[k.current])&&(null===(n=(i=h.current[k.current]).setHighlightOnHover)||void 0===n||n.call(i,!1),null===(t=(r=h.current[k.current]).setEffect)||void 0===t||t.call(r,!1));(k.current=null,T.current.layout.setVisible(!1),l&&h.current[l])&&(null===(s=(o=h.current[l]).setHighlightOnHover)||void 0===s||s.call(o,!0),null===(a=(d=h.current[l]).setEffect)||void 0===a||a.call(d,!0))},getCrossPoint:()=>{if(!k.current)return null;return f.current[k.current][S.current]},showTag:()=>{E.current&&E.current.showAllAnnotations(),Object.values(R.current).forEach((e=>{e.chunkContainer.setVisible(!0)}))},hideTag:()=>{E.current&&E.current.hideAllAnnotations(),Object.values(R.current).forEach((e=>{e.chunkContainer.setVisible(!1)}))},restore:()=>{Object.entries(p.current||{}).forEach((e=>{let[n,t]=e;const l=(i.xAxis||[]).find((e=>e.id===n));t&&l&&l.interval&&t.setInterval({start:l.interval.start,end:l.interval.end})})),Object.entries(v.current||{}).forEach((e=>{let[n,t]=e;const l=(i.yAxis||[]).find((e=>e.id===n));t&&l&&l.interval&&t.setInterval({start:l.interval.start,end:l.interval.end})})),ee(),O.current&&p.current&&v.current&&N(O.current,p.current,v.current,f.current),J(),F.current=!0},showAnnotation:e=>!!E.current&&E.current.showAnnotation(e),hideAnnotation:e=>!!E.current&&E.current.hideAnnotation(e),toggleAllAnnotations:()=>{E.current&&E.current.toggleAllAnnotations()},updateAnnotationPosition:(e,n,i,t)=>{if(E.current){const l=w.current[e];if(l)return E.current.updateAnnotationPosition(e,n,f.current[l.lineId],i,t)}return!1},getAnnotationCount:()=>E.current?E.current.getAnnotationCount():0,updateAuxiliaryLines:e=>{if(e&&Array.isArray(e)){if(O.current&&p.current&&v.current){const n=Object.keys(O.current),i=e.map((e=>e.id)).filter(Boolean);n.forEach((e=>{if(!i.includes(e)){const n=O.current[e];n&&n.line&&(n.line.clear(),n.line.dispose()),delete O.current[e]}})),e.forEach((e=>{if(e.id&&e.configType)if(O.current[e.id])I(e.id,O.current,p.current,v.current,f.current,h.current,e);else{const n=x.current.addLineSeries({dataPattern:"ProgressiveX"});if(n.setCursorEnabled(!1),n.setHighlightOnHover(!1),n.setEffect(!1),e.style){const i=C(e.style);n.setStrokeStyle(i)}O.current[e.id]={line:n,config:e},M(O.current[e.id],p.current,v.current,f.current)}}))}}else console.warn("updateAuxiliaryLines: \u65e0\u6548\u7684\u8f85\u52a9\u7ebf\u914d\u7f6e")},updateAuxiliaryLine:(e,n)=>e&&n?!!(O.current&&p.current&&v.current)&&I(e,O.current,p.current,v.current,f.current,h.current,n):(console.warn("updateAuxiliaryLine: \u7f3a\u5c11\u5fc5\u8981\u53c2\u6570"),!1),updateSingleAuxiliaryLine:(e,n)=>e&&n?!!(O.current&&p.current&&v.current)&&I(e,O.current,p.current,v.current,f.current,h.current,n):(console.warn("updateSingleAuxiliaryLine: \u7f3a\u5c11\u5fc5\u8981\u53c2\u6570"),!1),clearAllAuxiliaryLines:()=>{O.current&&Object.values(O.current).forEach((e=>{e.line&&e.line.clear()}))},getAuxiliaryLineCount:()=>O.current?Object.keys(O.current).length:0,updateChunkContent:e=>{let{id:n,showTitle:i,title:t,color:l,content:r,position:s}=e;const o=R.current[n];if(o&&o.chunkContainer){const e=o.chunkContainer.getVisible();o.chunkContainer.getPosition();o.chunkContainer.dispose();const a=$({chartXY:x.current,id:n,showTitle:i,title:t,color:l,content:r,position:s,onChunkMarkerPositionChangeRef:q});return a.setVisible(e),o.chunkContainer=a,!0}return console.warn(`\u5757\u6807\u6ce8 ${n} \u4e0d\u5b58\u5728`),!1},showChunk:e=>{const n=R.current[e];return n&&n.chunkContainer?(n.chunkContainer.setVisible(!0),!0):(console.warn(`\u5757\u6807\u6ce8 ${e} \u4e0d\u5b58\u5728`),!1)},hideChunk:e=>{const n=R.current[e];return n&&n.chunkContainer?(n.chunkContainer.setVisible(!1),!0):(console.warn(`\u5757\u6807\u6ce8 ${e} \u4e0d\u5b58\u5728`),!1)},toggleChunk:e=>{const n=R.current[e];if(n&&n.chunkContainer){const e=n.chunkContainer.getVisible();return n.chunkContainer.setVisible(!e),!e}return console.warn(`\u5757\u6807\u6ce8 ${e} \u4e0d\u5b58\u5728`),!1},getChunkCount:()=>R.current?Object.keys(R.current).length:0,setBreakPoint:()=>{var e,n,i,t,l,r;if(!k.current||null===S.current)return void console.warn("\u6ca1\u6709\u5f00\u542f\u5341\u5b57\u7ebf\u6216\u672a\u9009\u4e2d\u70b9\uff0c\u65e0\u6cd5\u8bbe\u7f6e\u65ad\u88c2\u70b9");const s=k.current,o=S.current,a=f.current[s];if(!a||0===a.length)return void console.warn("\u7ebf\u6570\u636e\u4e3a\u7a7a\uff0c\u65e0\u6cd5\u8bbe\u7f6e\u65ad\u88c2\u70b9");const d=a.findIndex((e=>e.index===o));if(-1===d)return void console.warn(`\u65e0\u6cd5\u627e\u5230index\u4e3a${o}\u7684\u70b9\uff0c\u65e0\u6cd5\u8bbe\u7f6e\u65ad\u88c2\u70b9`);Y.current[s]=d;const c=a.slice(0,d+1);if(null===(e=h.current)||void 0===e||null===(n=e[s])||void 0===n||null===(i=n.clear)||void 0===i||i.call(n),null===(t=h.current)||void 0===t||null===(l=t[s])||void 0===l||null===(r=l.add)||void 0===r||r.call(l,c),w.current&&E.current){Object.entries(w.current).filter((e=>{let[,n]=e;return n.lineId===s})).forEach((e=>{let[n,{pointIndex:i}]=e;c.some((e=>e.index===i))?E.current.showAnnotation(n):(E.current.hideAnnotation(n),console.log(`\u9690\u85cf\u70b9\u6807\u6ce8 ${n}\uff0c\u56e0\u4e3a\u5176pointIndex ${i} \u4e0d\u5728\u65ad\u88c2\u70b9\u8303\u56f4\u5185`))}))}console.log(`\u5df2\u8bbe\u7f6e\u65ad\u88c2\u70b9\uff1a\u7ebf ${s}\uff0c\u70b9index ${o}\uff0c\u6570\u7ec4\u4e0b\u6807 ${d}`)},clearBreakPoint:()=>{var e,i;Y.current={},Object.entries(f.current||{}).forEach((e=>{let[n,i]=e;var t,l,r,s,o,a;i&&i.length>0&&(null===(t=h.current)||void 0===t||null===(l=t[n])||void 0===l||null===(r=l.clear)||void 0===r||r.call(l),null===(s=h.current)||void 0===s||null===(o=s[n])||void 0===o||null===(a=o.add)||void 0===a||a.call(o,i))})),w.current&&E.current&&(Object.keys(w.current).forEach((e=>{E.current.showAnnotation(e)})),console.log("\u5df2\u6062\u590d\u6240\u6709\u70b9\u6807\u6ce8\u7684\u53ef\u89c1\u6027")),console.log("\u5df2\u6e05\u9664\u6240\u6709\u65ad\u88c2\u70b9\uff0c\u91cd\u65b0\u7ed8\u5236\u5b8c\u6574\u66f2\u7ebf\u6570\u636e"),null===(e=n.current)||void 0===e||null===(i=e.restore)||void 0===i||i.call(e)}}))),(0,c.vC)(Object.values(y),(e=>{k.current&&(e=>{let{key:n,lineData:i,line:t,lineCross:l,resultLabel:r,lineCrossIndexRef:s,onCrossMoveRef:o,breakPointIndex:a}=e,d=s.current+V[n],c=i.length-1;void 0!==a&&a>=0&&(c=Math.min(c,a)),d>c?d=c:d<0&&(d=0);const u=i[d];u&&(o.current({lineId:t.id,lineLength:i.length-1,pointIndex:u.index}),_({point:u,line:t,lineCross:l,resultLabel:r,lineCrossIndexRef:s}))})({key:e.key,lineData:f.current[k.current],line:h.current[k.current],lineCross:b.current[k.current],resultLabel:T.current,lineCrossIndexRef:S,onCrossMoveRef:U,breakPointIndex:Y.current[k.current]})})),(0,c.vC)(Object.values(A),(e=>{k.current&&(e=>{let{key:n,lineCrossMap:i,lineCrossIdRef:t,lineCrossIndexRef:l,resultLabel:r,lineMap:s,lineDataMap:o,breakPointRef:a,onHighlightChange:d}=e;const c=Object.keys(i);if(1===c.length)return;let u=c.findIndex((e=>e===t.current))+B[n];u<0?u=c.length-1:u>c.length-1&&(u=0);const x=c[u];D({targetLineId:x,lineCrossIdRef:t,lineCrossIndexRef:l,lineCrossMap:i,resultLabel:r,lineMap:s,lineDataMap:o,breakPointRef:a,onHighlightChange:d})})({key:e.key,lineCrossIdRef:k,lineCrossIndexRef:S,lineCrossMap:b.current,resultLabel:T.current,lineMap:h.current,lineDataMap:f.current,breakPointRef:Y,onHighlightChange:(e,n)=>{var i,t,l,r,s,o,a,d;n&&h.current[n]&&(null===(i=(t=h.current[n]).setHighlightOnHover)||void 0===i||i.call(t,!1),null===(l=(r=h.current[n]).setEffect)||void 0===l||l.call(r,!1));e&&h.current[e]&&(null===(s=(o=h.current[e]).setHighlightOnHover)||void 0===s||s.call(o,!0),null===(a=(d=h.current[e]).setEffect)||void 0===a||a.call(d,!0))}})})),(0,t.useEffect)((()=>{(e=>{var n,i,t,l;let{crossPercent:r,lineCrossIdRef:s,lineDataMapRef:o,lineMapRef:a,lineCrossMapRef:d,resultLabelRef:c,lineCrossIndexRef:u,breakPointRef:x}=e;if(!s.current||void 0===r||null===r)return;const p=s.current,v=null===(n=o.current)||void 0===n?void 0:n[p],h=null===(i=a.current)||void 0===i?void 0:i[p],f=null===(t=d.current)||void 0===t?void 0:t[p];if(!v||!h||!f||0===v.length)return;const g=v.length-1;let m=Math.round(r*g);const y=null===x||void 0===x||null===(l=x.current)||void 0===l?void 0:l[p];void 0!==y&&y>=0&&(m=Math.min(m,y));const A=u.current/g;if(Math.abs(A-r)<.01)return void console.log("\u4e0d\u79fb\u52a8");const j=v[Math.max(0,Math.min(m,g))];j&&_({point:j,line:h,lineCross:f,resultLabel:c.current,lineCrossIndexRef:u})})({crossPercent:r,lineCrossIdRef:k,lineDataMapRef:f,lineMapRef:h,lineCrossMapRef:b,resultLabelRef:T,lineCrossIndexRef:S,breakPointRef:Y})}),[r]),(0,t.useEffect)((()=>(console.log("ChartXY - option",i),F.current=!0,ne(),(e=>{let{lineMap:n,lineCrossMap:i,resultLabel:t,lineCrossIndexRef:l,lineDataMapRef:r,onCrossMoveRef:s}=e;Object.keys(n).forEach((e=>{const o=n[e],a=i[e];o.onMouseClick(((e,n)=>{if(!a.getVisible())return;const{x:i,y:d}=e.chart.engine.clientLocation2Engine(n.clientX,n.clientY),c=e.solveNearestFromScreen({x:i,y:d}).location;s.current({lineId:o.id,lineLength:r.current[o.id].length-1,pointIndex:c.index}),_({point:c,line:o,lineCross:a,resultLabel:t,lineCrossIndexRef:l})}))}))})({lineMap:h.current,lineCrossMap:b.current,resultLabel:T.current,lineCrossIndexRef:S,lineDataMapRef:f,onCrossMoveRef:U}),()=>{var e;E.current&&E.current.dispose(),null===(e=x.current)||void 0===e||e.dispose(),x.current=null,p.current=null,v.current=null,h.current=null,j.current=null,T.current=null,b.current=null,w.current=null,R.current=null,E.current=null,O.current=null,X.current={x:{},y:{}},Y.current={}})),[i]),(0,t.useEffect)((()=>{var e,n;h.current&&(Object.values(h.current).forEach((e=>{var n;null===e||void 0===e||null===(n=e.setEffect)||void 0===n||n.call(e,!1)})),l&&h.current[l]&&(null===(e=(n=h.current[l]).setEffect)||void 0===e||e.call(n,!0)))}),[l]);const Q=(0,t.useCallback)((()=>{F.current=!1}),[]),J=(0,t.useCallback)((()=>{E.current&&w.current&&Object.entries(w.current).forEach((e=>{let[n,i]=e;const{lineId:t,pointIndex:l}=i,r=f.current[t];if(!r||0===r.length)return;const s=r.find((e=>e.index===l));if(!s)return;const o=h.current[t];if(!o)return;const{xAxisId:a}=o,{yAxisId:d}=o,c=p.current[a],u=v.current[d];if(!c||!u)return;const x=c.getInterval(),g=u.getInterval(),m=s.x>=x.start&&s.x<=x.end,y=s.y>=g.start&&s.y<=g.end,A=m&&y,j=E.current.getAnnotationPosition(n);if(!j)return;const{currentPosition:b,size:k}=j,C=(b.x>=x.start&&(b.x,x.end),b.y>=g.start&&(b.y,g.end),E.current.getAnnotation(n));if(!C)return;const S=A;S||(console.log("isPointInRange",A,m,y),console.log("currentPosition",b,k,g)),S&&!C.visible?E.current.showAnnotation(n):!S&&C.visible&&E.current.hideAnnotation(n)}))}),[]),ee=()=>{Object.entries(p.current||{}).forEach((e=>{let[n,i]=e;const t=X.current.x[n];t&&i.offIntervalChange(t)})),Object.entries(v.current||{}).forEach((e=>{let[n,i]=e;const t=X.current.y[n];t&&i.offIntervalChange(t)})),z({option:i,xAxisMapRef:p,yAxisMapRef:v,lineMapRef:h}),Object.entries(p.current||{}).forEach((e=>{let[n,i]=e;const t=i.onIntervalChange((()=>{Q(),O.current&&L(O.current,p.current,v.current,f.current),J()}));X.current.x[n]=t})),Object.entries(v.current||{}).forEach((e=>{let[n,i]=e;const t=i.onIntervalChange((()=>{Q(),O.current&&L(O.current,p.current,v.current,f.current),J()}));X.current.y[n]=t}))},ne=()=>{const e=(0,u.dTW)().ChartXY({container:d.current,theme:u.BDt.light});e.setMouseInteractionRectangleFit(!1);const{xAxisMap:n,yAxisMap:t,linesMap:l,legend:r,resultLabel:s,lineCrossMap:o,markerPointMap:a,markerChunkMap:c,pointTagManager:m,auxiliaryLinesMap:y}=P(e,i,H,q);x.current=e,p.current=n,v.current=t,h.current=l,j.current=r,T.current=s,b.current=o,w.current=a,R.current=c,E.current=m,O.current=y;const A={},k={};Object.keys(l).forEach((e=>{var n,i;A[e]=null!==(n=null===(i=g.current)||void 0===i?void 0:i[e])&&void 0!==n?n:[],k[e]=K(l[e],A[e])})),g.current=A,f.current=k,Y.current={...i.breakPoint}||{},Object.entries(f.current).forEach((e=>{let[n,i]=e;if(0!==i.length){const e=Y.current[n];if(e){let t=i;void 0!==e&&e>=0&&(t=i.slice(0,e+1)),t.length>0&&h.current[n].add(t)}else h.current[n].add(i)}})),z({option:i,xAxisMapRef:p,yAxisMapRef:v,lineMapRef:h}),y&&Object.keys(y).length>0&&N(y,n,t,f.current),Object.entries(n).forEach((e=>{let[n,i]=e;const t=i.onIntervalChange((()=>{Q(),O.current&&L(O.current,p.current,v.current,f.current),J()}));X.current.x[n]=t})),Object.entries(t).forEach((e=>{let[n,i]=e;const t=i.onIntervalChange((()=>{Q(),O.current&&L(O.current,p.current,v.current,f.current),J()}));X.current.y[n]=t})),w.current&&E.current&&Object.entries(w.current).forEach((e=>{let[n,{pointIndex:i,lineId:t,title:l,lineInstance:r,isLine:s,isChunk:o,color:a,position:d}]=e;const c=f.current[t];if(!c||0===c.length)return;const u=c.find((e=>e.index===i));u&&E.current.createAnnotation({id:n,x:u.x,y:u.y,content:l,lineInstance:r,isLine:s,isChunk:o,color:a,position:d,style:{offsetY:1,lineThickness:1,marginLeft:8,marginRight:8,marginTop:4,marginBottom:4}})})),O.current&&Object.keys(O.current).length>0&&N(O.current,p.current,v.current,f.current)};return(0,Z.jsx)(W,{children:(0,Z.jsx)("div",{ref:d,style:{width:"100%",height:"100%"}})})},Q=(0,t.forwardRef)(q);var J=i(84617),ee=i(72238);const ne=(e,n,i,t,l)=>{const r=0===i;n.lines.filter((e=>e.dataSourceKey===l)).forEach((n=>{var i;r&&(null===(i=e.current)||void 0===i||i.clearLine(n.id));const l=[];for(let e=0;null!==(s=e<(null===(o=t[null===n||void 0===n?void 0:n.xSignal])||void 0===o?void 0:o.length))&&void 0!==s&&s;e+=1){var s,o,a,d;l.push({x:null===t||void 0===t||null===(a=t[null===n||void 0===n?void 0:n.xSignal])||void 0===a?void 0:a[e],y:null===t||void 0===t||null===(d=t[null===n||void 0===n?void 0:n.ySignal])||void 0===d?void 0:d[e]})}e.current.lineAdd(n.id,l)}))},ie=e=>{let{isBufferCurve:n,id:i,config:r,chartOption:s,chartXYRef:o,isLocked:a}=e;const d=(0,l.d4)((e=>e.project.optSample)),c=(0,t.useMemo)((()=>{var e,i,t,l,s,o;return n&&d&&(null===r||void 0===r||null===(e=r.base)||void 0===e?void 0:e.sourceType)===ee.xO.\u591a\u6570\u636e\u6e90?null===(i=r.curveGroup.yAxis)||void 0===i||null===(t=i.curves)||void 0===t||null===(l=t[null===d||void 0===d?void 0:d.code])||void 0===l||null===(s=l.lines)||void 0===s||null===(o=s[0])||void 0===o?void 0:o.id:null}),[n,d,r]),u=(0,t.useRef)({});return(0,J.A)({controlCompId:i,onMessage:e=>{var i;const{mode:t,data:l,doubleArrayIndex:d,sampleCode:c}=e,x=(e=>{let{isBufferCurve:n,sourceType:i,sampleCode:t,doubleArrayIndex:l}=e;return n?i===ee.xO.\u591a\u6570\u636e\u6e90?t:"optSample":l.toString()})({isBufferCurve:n,sourceType:null===r||void 0===r||null===(i=r.base)||void 0===i?void 0:i.sourceType,sampleCode:c,doubleArrayIndex:d});if(a)return 0===t&&(u.current[x]=[]),u.current[x]||(u.current[x]=[]),void u.current[x].push({mode:t,data:l});ne(o,s,t,l,x)}}),(0,t.useEffect)((()=>{var e,n;!a&&Object.keys(u.current).length>0&&(Object.entries(u.current).forEach((e=>{let[n,i]=e;i.forEach((e=>{let{mode:i,data:t}=e;ne(o,s,i,t,n)}))})),u.current={},null===(e=o.current)||void 0===e||null===(n=e.restore)||void 0===n||n.call(e))}),[a,null===s||void 0===s?void 0:s.lines,o]),{highlightLineId:c}};var te=i(93950),le=i.n(te),re=i(69581),se=i(67208),oe=i(28116);const ae=e=>{var n;let{config:i}=e;const l=(0,re.A)(null===i||void 0===i||null===(n=i.base)||void 0===n?void 0:n.crossInputCode),r=(0,t.useMemo)((()=>{var e,n,i;return l?(null===l||void 0===l||null===(e=l.default_val)||void 0===e?void 0:e.value)<0||(null===l||void 0===l||null===(n=l.default_val)||void 0===n?void 0:n.value)>1?null:(null===l||void 0===l||null===(i=l.default_val)||void 0===i?void 0:i.value)||0:null}),[l]),s=(0,t.useCallback)(le()((async e=>{let{lineId:n,lineLength:i,pointIndex:t}=e;if(!l)return;const r=t/i,s={...l,default_val:{...l.default_val,value:r}};(0,oe.P)({code:s.code},s),await(0,se.Tnc)(s)}),300),[se.Tnc,l]);return{crossPercent:r,onCrossMove:(0,t.useCallback)((e=>{let{lineId:n,lineLength:i,pointIndex:t}=e;s({lineId:n,lineLength:i,pointIndex:t})}),[s,l])}};var de=i(32099);const ce=()=>(0,de.Mz)([e=>e.inputVariable.inputVariableMap,(e,n)=>n],((e,n)=>n.map((n=>e.get(n))))),ue=e=>{const n=(0,t.useMemo)(ce,[]);return(0,l.d4)((i=>n(i,e)),l.bN)};var xe=i(14387),pe=i(56543),ve=i(754);const he=(e,n)=>{var i;const{optSample:t,resultHistoryData:l}=ve.A.getState().project,{resultData:r}=ve.A.getState().template,s=null===(i=r.find((n=>n.result_variable_id===e)))||void 0===i?void 0:i.code,o=l[null!==n&&void 0!==n?n:t.code];return o?o.find((e=>e.code===s)):null},fe=e=>ve.A.getState().template.resultData.find((n=>n.result_variable_id===e)),ge=e=>{try{const{auxiliary:n,curveGroup:i}=e;if(!n||0===n.length)return[];const t=n,l=ve.A.getState().template.auxiliaryLineList||[],r=[];return t.forEach((e=>{const n=l.find((n=>n.id===e));if(n){const e={name:n.name,id:n.id,type:n.type===xe.nT.\u76f4\u7ebf?"straight":"segment",xAxisId:n.x_channel||"x1",yAxisId:n.y_channel||"y1",style:{line_color:n.line_color||"#000000",line_type:n.line_type===xe.rI.\u5b9e\u7ebf?"solid":n.line_type===xe.rI.\u865a\u7ebf?"dashed":"dotted",thickness:n.thickness||2}},l=(e,n)=>{var i;if(null!==e&&void 0!==e&&e.is_fx&&n){var t;const e=ve.A.getState().inputVariable.inputVariableMap.get(n);return null===e||void 0===e||null===(t=e.default_val)||void 0===t?void 0:t.value}return null!==(i=null===e||void 0===e?void 0:e.value)&&void 0!==i?i:0},h=e=>{var n,i;let t=null!==(n=null===e||void 0===e?void 0:e.x)&&void 0!==n?n:0,l=null!==(i=null===e||void 0===e?void 0:e.y)&&void 0!==i?i:0;if(null!==e&&void 0!==e&&e.is_fx_x&&null!==e&&void 0!==e&&e.input_code_x){var r,s;const n=ve.A.getState().inputVariable.inputVariableMap.get(e.input_code_x);t=null!==(r=null===n||void 0===n||null===(s=n.default_val)||void 0===s?void 0:s.value)&&void 0!==r?r:t}if(null!==e&&void 0!==e&&e.is_fx_y&&null!==e&&void 0!==e&&e.input_code_y){var o,a;const n=ve.A.getState().inputVariable.inputVariableMap.get(e.input_code_y);l=null!==(o=null===n||void 0===n||null===(a=n.default_val)||void 0===a?void 0:a.value)&&void 0!==o?o:l}if(null!==e&&void 0!==e&&e.is_fx&&null!==e&&void 0!==e&&e.result_code){const n=((e,n)=>{const{optSample:i,resultHistoryData:t}=ve.A.getState().project,l=t[null!==n&&void 0!==n?n:i.code];return l?l.find((n=>n.code===e)):null})(e.result_code);if(n&&"number"===typeof n.index)return{index:n.index,isIndexPoint:!0}}return{x:t,y:l}};if(n.config_type===xe.Pj.\u4e24\u70b9\u914d\u7f6e){const l=h(n.dot),r=h(n.dot2),p=null===l||void 0===l?void 0:l.isIndexPoint,v=null===r||void 0===r?void 0:r.isIndexPoint;if(p||v){var t;let n;var s,o,a,d,c,u,x;if(null!==(t=i.yAxis.curves)&&void 0!==t&&t.optSample)n=null===(s=i.yAxis.curves)||void 0===s||null===(o=s.optSample)||void 0===o||null===(a=o.lines)||void 0===a?void 0:a[0].id;else n=null===(d=Object.values(i.yAxis.curves))||void 0===d||null===(c=d[0])||void 0===c||null===(u=c.lines)||void 0===u||null===(x=u[0])||void 0===x?void 0:x.id;e.lineId=n}e.configType="segment",e.data=[p?{index:l.index}:{x:l.x,y:l.y},v?{index:r.index}:{x:r.x,y:r.y}]}else if(n.config_type===xe.Pj.\u5782\u76f4X\u8f74\u914d\u7f6e){var p;e.configType="xStraight";const i=l(n.c_value,null===(p=n.c_value)||void 0===p?void 0:p.input_code);e.data=[{x:i}]}else if(n.config_type===xe.Pj.\u659c\u7387\u914d\u7f6e){var v;e.configType="slopeStraight";const i=h(n.dot),t=l(n.a_value,null===(v=n.a_value)||void 0===v?void 0:v.input_code);null!==i&&void 0!==i&&i.isIndexPoint?(e.lineId=n.array_code||"1",e.data=[{index:i.index,slope:t}]):e.data=[{x:i.x,y:i.y,slope:t}]}r.push(e)}})),r}catch(n){return console.log("error",n),[]}},me=e=>{let{chartOption:n,config:i,chartXYRef:r}=e;const s=(0,t.useRef)(),a=(0,l.d4)((e=>e.template.auxiliaryLineList)),d=(0,l.d4)((e=>e.project.optSample)),c=(0,l.d4)((e=>e.project.resultHistoryData)),u=(0,t.useMemo)((()=>{const e=[];return i.auxiliary.forEach((n=>{const i=a.find((e=>e.id===n));i&&(i.dot.is_fx_x&&i.dot.input_code_x&&e.push(i.dot.input_code_x),i.dot.is_fx_y&&i.dot.input_code_y&&e.push(i.dot.input_code_y),i.dot2.is_fx_x&&i.dot2.input_code_x&&e.push(i.dot2.input_code_x),i.dot2.is_fx_y&&i.dot2.input_code_y&&e.push(i.dot2.input_code_y),i.a_value.is_fx&&i.a_value.input_code&&e.push(i.a_value.input_code),i.c_value.is_fx&&i.c_value.input_code&&e.push(i.c_value.input_code))})),e}),[i,a]),x=ue(u);(0,t.useEffect)((()=>{s.current=n.auxiliary}),[n]),(0,t.useEffect)((()=>{const e=ge(i);(0,o.isEqual)(e,s.current)||(r.current.updateAuxiliaryLines(e),s.current=e)}),[a,x,d,c])};var ye=i(36950);const Ae=e=>{let{result:n,resultVariable:i,isName:t,isAbbr:l,isVal:r}=e;if(!i)return"";const s=[];if(t&&s.push(i.variable_name),l&&i.abbreviation&&s.push(i.abbreviation),r){let e="--";n&&"--"!==(null===n||void 0===n?void 0:n.value)&&(e=(0,ye.jq)(i.format_type,(0,ye.tJ)(null===n||void 0===n?void 0:n.value,i.dimension_id,i.unit_id),(0,ye._q)(i.format_type,i.format_info))),s.push(`: ${e}`),i.unit_name&&s.push(` ${i.unit_name}`)}return s.join("")},je=(e,n)=>{const{thickness:i,zeroLineColor:t,color:l,zeroLineThickness:r,isGrid:s,name:o,type:a,isLog:d,zeroLineType:c,isZeroLine:u,gridThickness:x,lastRange:p,lowLimit:v,proportionType:h,upLimit:f,gridColor:g,gridType:m}=n;return{id:e,title:o,style:{thickness:i,lineType:a,color:l},gridLine:{open:s,thickness:x,color:g,lineType:m},zeroLine:{open:u,thickness:r,color:t,lineType:c},interval:{proportionType:h,start:v,end:f,isLog:d,lastRange:p}}},be=e=>{let{base:n,xAxis:i,curveGroup:t}=e;try{const e=[],{xOffset:i=0,yOffset:l=0}=n;return Object.entries(t).forEach((n=>{let[t,r]=n;const{isEnable:s,name:o,xSignal:a,xUnit:d,curves:c}=r;if(!s)return;let u=0;Object.entries(c).forEach((n=>{let[r,s]=n;s.lines.forEach(((n,s)=>{let{id:o,name:c,color:x,signEach:p,isLine:v,signType:h,lineType:f,code:g,yUnit:m,xName:y,yName:A,lineThickness:j,isSign:b}=n;e.push({id:o,title:c,xAxisId:"1",yAxisId:t,xSignal:a,ySignal:g,xRatio:(0,ye.O2)(d),yRatio:(0,ye.O2)(m),xOffset:u*i,yOffset:u*l,xName:y,yName:A,dataSourceKey:r,style:{isLine:v,color:x,thickness:j,lineStyle:f,isSign:b,signStyle:h,signEach:p}}),u+=1}))}))})),e}catch(l){return console.log("error",l),[]}},ke=e=>{let{curveGroup:n,pointTagOpen:i,compStatus:t,isBufferCurve:l}=e;try{const e=[];return i?(Object.entries(n).forEach((n=>{let[i,r]=n;const{isEnable:s,curves:o}=r;s&&Object.entries(o).forEach((n=>{let[i,r]=n;r.lines.forEach(((n,r)=>{const{pointTags:s,id:o}=n;if(!s||0===s.length)return;const a=o;s.forEach(((n,r)=>{const{id:s,color:o,resultVariableId:d,isName:c,isChunk:u,isLine:x,isVal:p,isAbbr:v}=n,h=he(d,l&&"optSample"!==i?i:void 0),f=fe(d),g=Ae({result:h,resultVariable:f,isName:c,isAbbr:v,isVal:p});var m,y;h&&void 0!==h.index&&-1!==h.index&&e.push({id:s,lineId:a,title:g,isLine:x,isChunk:u,color:o,pointIndex:h.index,position:null===t||void 0===t||null===(m=t.pointTag)||void 0===m||null===(y=m.position)||void 0===y?void 0:y[s]})}))}))}))})),e):e}catch(r){return console.log("error",r),[]}},Ce=(e,n)=>{let{open:i,list:t}=e;const l=[];return i?(t.forEach((e=>{var i,t;const{id:r,showTitle:s,title:o,color:a,isName:d,isChunk:c,isSample:u,isVal:x,isAbbr:p,curveIndex:v,results:h}=e,f=[];h.forEach((e=>{const n=he(e),i=fe(e),t=Ae({result:n,resultVariable:i,isName:d,isAbbr:p,isVal:x});t&&f.push(t)})),l.push({id:r,color:a||"#000000",showBorder:!1,showTitle:s,title:o,content:f,position:null===n||void 0===n||null===(i=n.chunkTag)||void 0===i||null===(t=i.position)||void 0===t?void 0:t[r]})})),l):l},Se=e=>{const{isName:n,name:i}=e;return{title:n?i:""}},Te=(e,n,i)=>{const{base:t,xAxis:l,yAxis:r,y2Axis:s,curveGroup:o,legend:a,chunkTag:d,pointTag:c}=e;return{chart:Se(t),xAxis:[je("1",l)],yAxis:o.y2Axis.isEnable?[je("yAxis",r),je("y2Axis",s)]:[je("yAxis",r)],lines:be(e),legend:{open:null===a||void 0===a?void 0:a.open},markerPoint:ke({curveGroup:o,pointTagOpen:null===c||void 0===c?void 0:c.open,compStatus:n,isBufferCurve:i}),markerChunk:Ce(d,n),auxiliary:ge(e)}},we=e=>{let{chartOption:n,config:i,chartXYRef:r,compStatus:s,updateCompStatus:a}=e;const d=(0,t.useRef)(),c=(0,l.d4)((e=>e.project.optSample)),u=(0,l.d4)((e=>e.project.resultHistoryData)),x=(0,l.d4)((e=>e.template.resultData)),p=(0,t.useRef)();(0,t.useEffect)((()=>{d.current=n.markerPoint.map((e=>[e.id]))}),[n]),(0,t.useEffect)((()=>{const{markerPoint:e}=Te(i,s);(0,o.isEqual)(e,d.current)||(e.forEach((e=>{var n;(0,o.isEqual)(null===(n=d.current)||void 0===n?void 0:n.find((n=>n.id===e.id)),e)||r.current.updateAnnotationPosition(e.id,e.pointIndex,e.title,e.position)})),d.current=e)}),[c,u,x,s]);return{updatePointTagPosition:e=>{var n,i,t;const l={...null!==s&&void 0!==s?s:{},pointTag:{...null!==(n=null===s||void 0===s?void 0:s.pointTag)&&void 0!==n?n:{},position:{...null!==(i=null===s||void 0===s||null===(t=s.pointTag)||void 0===t?void 0:t.position)&&void 0!==i?i:{},[e.id]:e.position}}};p.current=(0,o.cloneDeep)(l.pointTag.position),a(l)}}},Ie=e=>{let{chartOption:n,config:i,chartXYRef:r,compStatus:s,updateCompStatus:a}=e;const d=(0,t.useRef)(),c=(0,l.d4)((e=>e.project.optSample)),u=(0,l.d4)((e=>e.project.resultHistoryData)),x=(0,l.d4)((e=>e.template.resultData));(0,t.useEffect)((()=>{d.current=n.markerPoint.map((e=>[e.id]))}),[n]),(0,t.useEffect)((()=>{const{markerChunk:e}=Te(i,s);(0,o.isEqual)(e,d.current)||(e.forEach((e=>{var n;const{id:i,showTitle:t,title:l,color:s,content:a,position:c}=e;(0,o.isEqual)(null===(n=d.current)||void 0===n?void 0:n.find((n=>n.id===e.id)),e)||r.current.updateChunkContent({id:i,showTitle:t,title:l,color:s,content:a,position:c})})),d.current=e)}),[c,u,x,s]);return{updateChunkTagPosition:e=>{var n,i,t;const l={...null!==s&&void 0!==s?s:{},chunkTag:{...null!==(n=null===s||void 0===s?void 0:s.chunkTag)&&void 0!==n?n:{},position:{...null!==(i=null===s||void 0===s||null===(t=s.chunkTag)||void 0===t?void 0:t.position)&&void 0!==i?i:{},[e.id]:e.position}}};a(l)}}};var Le=i(30263);const Me=e=>{var n;let{config:i,compStatus:r,isBufferCurve:s}=e;const o=(0,l.d4)((e=>e.subTask.openExperiment)),a=(0,l.d4)((e=>e.project.multiSample)),d=(0,l.d4)((e=>e.project.optSample)),c=(0,l.d4)((e=>e.project.sampleData)),u=(0,t.useMemo)((()=>{var e;if(s&&(null===i||void 0===i||null===(e=i.base)||void 0===e?void 0:e.sourceType)===ee.xO.\u591a\u6570\u636e\u6e90){var n;const e=null===c||void 0===c||null===(n=c.map((e=>e.children)))||void 0===n?void 0:n.flat(),i={};return e.forEach((e=>{"finished"===e.status&&(!a.length>0||a.includes(e.id))&&(i[e.code]=e)})),i}return null}),[o,s,null===i||void 0===i||null===(n=i.base)||void 0===n?void 0:n.sourceType,a,c]),x=(0,t.useMemo)((()=>{var e;if(null===i||void 0===i||!i.base)return;let n={...i};var t,l,r,a;(s&&(null===i||void 0===i||null===(e=i.base)||void 0===e?void 0:e.sourceType)===ee.xO.\u591a\u6570\u636e\u6e90&&(u&&(n=((e,n)=>{const i=e=>Object.fromEntries(Object.entries(e).filter((e=>{let[i,t]=e;return!!n[i]})).map((e=>{let[i,t]=e;return[i,{...t,lines:t.lines.map((e=>"#FFFFFF"===e.color?{...e,color:n[i].color}:e))}]})));return{...e,curveGroup:{...e.curveGroup,yAxis:{...e.curveGroup.yAxis,curves:i(e.curveGroup.yAxis.curves)},y2Axis:{...e.curveGroup.y2Axis,curves:i(e.curveGroup.y2Axis.curves)}}}})(n,u)),n=((e,n)=>{const i=e=>Object.fromEntries(Object.entries(e).map((e=>{let[i,t]=e;return i===n.code?[i,t]:[i,{...t,lines:t.lines.map((e=>{var t,l;return{...e,pointTags:null!==(t=null===e||void 0===e||null===(l=e.pointTags)||void 0===l?void 0:l.filter((e=>!e.isSample||i===n.code)))&&void 0!==t?t:[]}}))}]})));return{...e,curveGroup:{...e.curveGroup,yAxis:{...e.curveGroup.yAxis,curves:i(e.curveGroup.yAxis.curves)},y2Axis:{...e.curveGroup.y2Axis,curves:i(e.curveGroup.y2Axis.curves)}}}})(n,d)),o)&&(n={...n,pointTag:{...null!==(t=null===(l=n)||void 0===l?void 0:l.pointTag)&&void 0!==t?t:{},open:!1},chunkTag:{...null!==(r=null===(a=n)||void 0===a?void 0:a.chunkTag)&&void 0!==r?r:{},open:!1}});return n}),[i,o,u,s,d]),p=(0,t.useMemo)((()=>{if(null!==x&&void 0!==x&&x.base)return Te(x,r,s)}),[x,s]);return{filteredConfig:x,chartOption:p}};var Ne=i(63804),$e=i(10866);const Ee=e=>{var n,i,s,o,a;let{isBufferCurve:d,id:c,config:u,chartXYRef:x}=e;const p=(0,l.d4)((e=>e.subTask.openExperiment)),v=(0,l.d4)((e=>e.project.optSample)),h=(0,l.d4)((e=>e.project.multiSample)),{handleSampleData:f,getSamples:g}=(0,$e.A)(),m=(0,t.useMemo)((()=>{if(null===u||void 0===u||!u.curveGroup)return[];const e=new Set;return Object.values(u.curveGroup).forEach((n=>{n.isEnable&&(n.xSignal&&e.add(n.xSignal),n.ySignal&&Array.isArray(n.ySignal)&&n.ySignal.forEach((n=>{n&&e.add(n)})))})),Array.from(e)}),[u]),y=(0,t.useMemo)((()=>{var e;return d?Ne.d.daqbuffer:(null===u||void 0===u||null===(e=u.base)||void 0===e?void 0:e.sourceType)===ee.xO.\u591a\u6570\u636e\u6e90?Ne.d.\u4e8c\u7ef4\u6570\u7ec4\u96c6\u5408:Ne.d.\u4e8c\u7ef4\u6570\u7ec4}),[null===u||void 0===u||null===(n=u.base)||void 0===n?void 0:n.sourceType,d]),A=(0,t.useMemo)((()=>{var e;if(d&&(null===u||void 0===u||null===(e=u.base)||void 0===e?void 0:e.sourceType)===ee.xO.\u591a\u6570\u636e\u6e90){const e=g(!0);return(null===h||void 0===h?void 0:h.length)>0?e.filter((e=>h.includes(e.id))).map((e=>e.code)):g(!0).map((e=>e.code))}return[v.code]}),[v.code,null===u||void 0===u||null===(i=u.base)||void 0===i?void 0:i.sourceType,h,g,d]),j=(0,t.useMemo)((()=>p?1:0),[d,p,null===u||void 0===u||null===(s=u.base)||void 0===s?void 0:s.sourceType]),{targetRef:b}=(0,Ne.A)({controlCompId:c,dataSourceType:y,dataSourceCode:null===u||void 0===u||null===(o=u.base)||void 0===o?void 0:o.sourceInputCode,dataCodes:m,timer:p?null===u||void 0===u||null===(a=u.base)||void 0===a?void 0:a.updateFreq:-1,number:-1,testStatus:j,daqCurveSelectedSampleCodes:A},(()=>{var e,n,i,t;null===(e=x.current)||void 0===e||null===(n=e.clearAllLine)||void 0===n||n.call(e),null===(i=x.current)||void 0===i||null===(t=i.restore)||void 0===t||t.call(i)}));return(0,t.useEffect)((()=>{p&&d&&u.base.sourceType===ee.xO.\u591a\u6570\u636e\u6e90&&r.Ay.info("buffer\u66f2\u7ebf\u7ed3\u679c\u6587\u4ef6\u4e0b\u8bd5\u9a8c\u4e2d\u4e0d\u663e\u793a\u5b9e\u65f6\u66f2\u7ebf\uff0c\u5728\u8bd5\u9a8c\u7ed3\u675f\u540e\u4f1a\u5448\u73b0\u5728\u66f2\u7ebf\u56fe\u4e2d")}),[u,p,d]),{targetRef:b}},Re=(0,t.forwardRef)(((e,n)=>{let{isBufferCurve:i,id:l,config:r,compStatus:s,updateCompStatus:o,openCross:a,openBreak:d,showPointTag:u,showChunkTag:x,setOpenBreakPoint:p,isLocked:v,isMarking:h,onMarkingStep:f}=e;const g=(0,t.useRef)(),{filteredConfig:m,chartOption:y}=Me({config:r,compStatus:s,isBufferCurve:i});me({chartOption:y,config:m,chartXYRef:g});const{updatePointTagPosition:A}=we({chartOption:y,config:r,chartXYRef:g,compStatus:s,updateCompStatus:o}),{updateChunkTagPosition:j}=Ie({chartOption:y,config:r,chartXYRef:g,compStatus:s,updateCompStatus:o}),{targetRef:b}=Ee({isBufferCurve:i,id:l,config:r,chartXYRef:g}),{highlightLineId:k}=ie({isBufferCurve:i,id:l,config:r,chartOption:y,chartXYRef:g,isLocked:v}),{crossPercent:C,onCrossMove:S}=ae({config:r});return(0,t.useImperativeHandle)(n,(()=>({restore:()=>{var e,n;null===(e=g.current)||void 0===e||null===(n=e.restore)||void 0===n||n.call(e)},clearBreakPoint:()=>{var e,n;null===(e=g.current)||void 0===e||null===(n=e.clearBreakPoint)||void 0===n||n.call(e)}})),[]),(0,t.useEffect)((()=>{var e,n,i,t;a?null===(e=g.current)||void 0===e||null===(n=e.openCross)||void 0===n||n.call(e):null===(i=g.current)||void 0===i||null===(t=i.closeCross)||void 0===t||t.call(i)}),[a]),(0,t.useEffect)((()=>{var e,n,i,t;d?null===(e=g.current)||void 0===e||null===(n=e.openCross)||void 0===n||n.call(e):null===(i=g.current)||void 0===i||null===(t=i.closeCross)||void 0===t||t.call(i)}),[d]),(0,c.vC)("Enter",(e=>{var n,i;if(d)null===(n=g.current)||void 0===n||null===(i=n.setBreakPoint)||void 0===i||i.call(n),p(!0);else if(h){var t;null===f||void 0===f||f(null===(t=g.current)||void 0===t?void 0:t.getCrossPoint())}})),(0,t.useEffect)((()=>{var e,n,i,t;u||x?null===(e=g.current)||void 0===e||null===(n=e.showTag)||void 0===n||n.call(e):null===(i=g.current)||void 0===i||null===(t=i.hideTag)||void 0===t||t.call(i)}),[u,x,y]),(0,t.useEffect)((()=>{var e,n,i,t;h?null===(e=g.current)||void 0===e||null===(n=e.openCross)||void 0===n||n.call(e):a||null===(i=g.current)||void 0===i||null===(t=i.closeCross)||void 0===t||t.call(i)}),[h,a]),(0,Z.jsx)("div",{ref:b,style:{width:"100%",height:"100%",overflow:"hidden"},children:(0,Z.jsx)(Q,{ref:g,option:y,crossPercent:C,onCrossMove:S,highlightLineId:k,onAnnotationPositionChange:A,onChunkMarkerPositionChange:j})})})),Oe=Re;var Pe=i(37762),_e=i(80231),Ve=i(33981),Be=i(16204),De=i(16271),Ge=i(20871),Fe=i(59551);const Xe=["\u66f2\u7ebf\u56fe","\u66f2\u7ebf\u7ec4","X-\u8f74","Y-\u8f741","Y-\u8f742","\u8f85\u52a9\u7ebf","\u6807\u7b7e","\u6807\u8bb0\u70b9\u8bbe\u7f6e","\u5b9a\u4e49\u5750\u6807\u6e90"],Ye="single",Ue="multi",ze={"\u2014\u2014\u2014\u2014\u2014\u2014":"solid","------":"dashed","......":"dotted"},He={"\u4e0a\u4e0b\u9650\u8303\u56f4":"not","\u6b63\u5411\u4f4d\u7f6e\u6269\u5c55":"extend","\u8d1f\u5411\u4f4d\u7f6e\u6269\u5c55":"min-extend","\u5e94\u7528\u8303\u56f4":"all","\u6570\u636e\u8303\u56f4":"data-range","\u626b\u63cf\u8303\u56f4":"last-range"},We={o:"o","\u25bd":"\u25bd","\u25a1":"\u25a1"},Ke=e=>Object.entries(e).map((e=>{let[n,i]=e;return{label:n,value:i}})),Ze={isLine:!0,lineType:ze["\u2014\u2014\u2014\u2014\u2014\u2014"],lineThickness:2,isSign:!1,signType:We.o,signEach:!1,color:"#000000",code:"",isApply:!1};var qe=i(66966),Qe=i(41780);const Je=e=>{let{sourceType:n,sourceInputCode:i,isBufferCurve:r}=e;const s=(0,De.A)(),o=(0,Ge.A)(),a=(0,Fe.A)(),d=(0,l.d4)((e=>e.template.signalList));return(0,t.useMemo)((()=>{if(!i)return[];if(r){var e,t;const n=a.find((e=>e.code===i));if(!n)return[];const l=null===n||void 0===n||null===(e=n.buffer_tab)||void 0===e||null===(t=e.signals)||void 0===t?void 0:t.map((e=>{var n,i,t;const l=d.find((n=>n.code===e)),r=(0,Qe.C)(null===l||void 0===l?void 0:l.dimension_id);return{code:e,name:null!==(n=null===l||void 0===l?void 0:l.variable_name)&&void 0!==n?n:e,dimensionId:null!==(i=null===l||void 0===l?void 0:l.dimension_id)&&void 0!==i?i:"",unitId:null!==(t=null===l||void 0===l?void 0:l.unit_id)&&void 0!==t?t:"",unitList:r}}));return null!==l&&void 0!==l?l:[]}return(0,qe.L)({sourceType:n,sourceInputCode:i})}),[n,i,s,o])},en=e=>{var n,i,s;let{domId:a,compName:d,setOpen:c,layoutConfig:u,config:x,isBufferCurve:p,openCross:v,setOpenCross:h,openBreak:f,setOpenBreak:g,showPointTag:m,setShowPointTag:y,showChunkTag:A,setShowChunkTag:j,openBreakPoint:b,setOpenBreakPoint:k,onRestore:C,onClearBreakPoint:S,isLocked:T,setIsLocked:w,isMarking:I,onActivateMarking:L,onStopMarking:M}=e;const N=(0,l.d4)((e=>e.subTask.openExperiment)),$=(0,l.d4)((e=>e.global.systemConfig)),E=(0,l.d4)((e=>e.global.stationList)),R=(0,l.d4)((e=>e.global.cfgList)),O=(0,l.d4)((e=>e.system.projectList)),P=(0,l.d4)((e=>e.project.projectId)),_=(0,l.d4)((e=>e.project.optSample)),V=Je({sourceType:null===x||void 0===x||null===(n=x.base)||void 0===n?void 0:n.sourceType,sourceInputCode:null===x||void 0===x||null===(i=x.base)||void 0===i?void 0:i.sourceInputCode,isBufferCurve:p}),{copy:B}=(0,Ve.A)(),[D,G]=(0,t.useState)(!1),F=[{label:"\u8bbe\u7f6e\u66f2\u7ebf",visible:!0,feature:!0,onClick:()=>c(!0),line:!0},{label:T?"\u89e3\u9501":"\u9501\u5b9a",visible:N,feature:!0,onClick:()=>w(!T)},{label:"\u6062\u590d",visible:!0,feature:!0,onClick:()=>null===C||void 0===C?void 0:C(),line:!0},{label:"\u62f7\u8d1d\u5230\u526a\u8d34\u677f",visible:!N,feature:!0,onClick:()=>{document.getElementById(a).querySelector("canvas").toBlob((e=>{B(e,pe.Je.\u56fe\u7247)}))}},{label:"\u6253\u5370\u66f2\u7ebf\u56fe",visible:!N,feature:!0,onClick:()=>{(()=>{const e=document.getElementById(a).querySelector("canvas"),n=new Pe.uE({orientation:"l"});n.addImage(e.toDataURL("image/jpg"),"PNG",0,0,n.internal.pageSize.getWidth(),n.internal.pageSize.getHeight()),n.save("curve.pdf")})()}},{label:"\u5bfc\u51facsv",visible:!N&&!p,feature:!0,onClick:()=>{x.base.sourceInputCode?G(!0):r.Ay.error("\u672a\u9009\u62e9\u6570\u636e\u6e90")}},{label:v?"\u5173\u95ed\u5341\u5b57\u7ebf":"\u6fc0\u6d3b\u5341\u5b57\u7ebf",visible:!N,feature:!f&&!I,onClick:()=>h(!v)},{label:f?"\u5173\u95ed\u8bbe\u7f6e\u65ad\u88c2\u70b9":"\u8bbe\u7f6e\u65ad\u88c2\u70b9",visible:!N,feature:!v&&!I,onClick:()=>g(!f)},{label:"\u64a4\u9500\u65ad\u88c2\u70b9",visible:!N&&b,feature:!v&&!I,onClick:()=>{null===S||void 0===S||S(),k(!1)}},{label:I?"\u505c\u6b62\u624b\u5de5\u6807\u8bb0":"\u6fc0\u6d3b\u624b\u5de5\u6807\u8bb0",visible:!N,feature:!v&&!f,onClick:I?M:L},{label:m?"\u9690\u85cf\u6807\u7b7e":"\u663e\u793a\u6807\u7b7e",visible:!N,feature:!0,onClick:()=>{y(!m)}},{label:A?"\u9690\u85cf\u6807\u7b7e\u5757":"\u663e\u793a\u6807\u7b7e\u5757",visible:!N,feature:!0,line:!0,onClick:()=>{j(!A)}}].filter((e=>!!e&&e.visible)),X=(0,t.useMemo)((()=>{var e,n;return null!==(e=null===O||void 0===O||null===(n=O.find((e=>(null===e||void 0===e?void 0:e.project_id)===Number(P))))||void 0===n?void 0:n.project_name)&&void 0!==e?e:""}),[O]);return(0,Z.jsxs)(Z.Fragment,{children:[(0,Z.jsx)(_e.A,{domId:a,options:F,layoutConfig:u,capture:!0}),D&&(0,Z.jsx)(Be.A,{open:D,title:"\u5bfc\u51facsv",defaultPath:null===$||void 0===$?void 0:$.project_directory,defaultFileName:`${null!==X&&void 0!==X?X:""}-${null!==(s=null===_||void 0===_?void 0:_.name)&&void 0!==s?s:""}-${d}`,onOk:async(e,n)=>{G(!1);const{cfgId:i}=await(0,se.TEp)(),{stationId:t}=R.find((e=>e.cfgId===i)),{stationName:l}=E.find((e=>e.id===t)),r=`${"\\"===e.at(-1)?e:`${e}\\`}${l}_${t}\\${X}_${P}\\`;await(0,se.NQw)({templateName:(0,ye.n1)(),arrayCode:x.base.sourceInputCode,codes:(()=>{if(null===x||void 0===x||!x.curveGroup)return[];const e=ve.A.getState().global.unitList,n=[],i=(n,i)=>{var t,l,r,s,o;const a=null===e||void 0===e||null===(t=e.map((e=>e.units)))||void 0===t||null===(l=t.flat())||void 0===l?void 0:l.find((e=>e.id===i));return{code:n,name:null===(r=V.find((e=>e.code===n)))||void 0===r?void 0:r.name,unit:null!==(s=null===a||void 0===a?void 0:a.name)&&void 0!==s?s:"",proportion:null!==(o=null===a||void 0===a?void 0:a.proportion)&&void 0!==o?o:1}};return Object.values(x.curveGroup).forEach((e=>{if(e.isEnable){const t=i(e.xSignal,e.xUnit);e.xSignal&&n.every((e=>!(0,o.isEqual)(t,e)))&&n.push(i(e.xSignal,e.xUnit)),Object.values(e.curves).forEach((e=>{e.lines.forEach((e=>{const t=i(e.code,e.yUnit);n.every((e=>!(0,o.isEqual)(t,e)))&&n.push(t)}))}))}})),n})(),path:r,fileName:n,type:x.base.sourceType===ee.xO.\u5355\u6570\u636e\u6e90?"DoubleArray":"DoubleArrayList"})},onCancel:()=>{G(!1)}})]})};var nn=i(25055),tn=i(74117),ln=i(75440),rn=i(4554);const sn=`1px solid ${H.o$.borderGray}`,on=a.Ay.div`
    background-color: #fff;
    height: 100%;
    display: flex;
    flex-direction: column;

    .container {
        flex: 1;
        display: flex;
        height: 800px;
        .title {
            font-size: 13px;
            font-weight: bold;
            margin-bottom: ${(0,H.D0)("15px")};
        }

        >.siderbar {
            padding: ${(0,H.D0)("20px")};
            width: ${(0,H.D0)("260px")};
            border-right: 5px solid ${H.o$.modalBack};
            >.item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                &.active, &:hover {
                    background: ${H.o$.hoverActiveBlue};
                }
                > img {
                    height: 16px;
                }
            }
        
        }
        >.form {
            padding: ${(0,H.D0)("20px")} ${(0,H.D0)("40px")};
            width: 100%;
            flex: 1;
            overflow: hidden;
            
            .step-base {
                .centre {
                    margin-top: 10px;
                    padding: ${(0,H.D0)("20px")};
                    border: ${sn};
                }
            }
            .step-curve {
                .centre {
                    margin-top: 10px;
                    padding: ${(0,H.D0)("20px")};
                    border: ${sn};
                    position: relative;
                    .right-float {
                        position: absolute;
                        right: 10px;
                    }
                }
            }
            .step-axis {
                .centre {
                    margin-top: 10px;
                    padding: ${(0,H.D0)("20px")};
                    border: ${sn};
                    position: relative;
                }
            }
            .step-auxiliary {
            }
            .step-marker {
                height: 40vh;
                .header {
                    display: flex;
                    justify-content: space-between;
                    margin-bottom: 2vw;
                }
                
            }
            
            .step-define-axis {
                width: 100%;
                .table-layout {
                    width: 40vw;
                }
                
                
            }

        }
    }
    >.footer-btns {
        display: flex;
        justify-content: center;
        padding: ${(0,H.D0)("20px")} 0;
        border-top: ${sn};

        button {
            margin-right: ${(0,H.D0)("10px")};
        }
    }
`;var an=i(18650);const dn=a.Ay.div`
    padding: ${(0,H.D0)("20px")};
    width: ${(0,H.D0)("260px")};
    border-right: 5px solid ${H.o$.modalBack};
    >.item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        &.active, &:hover {
            background: ${H.o$.hoverActiveBlue};
        }
        > img {
            height: 16px;
        }
    }
            
            
`,cn=e=>{const{steps:n,currentStep:i,visitedSteps:t,handleClickStep:l}=e;return(0,Z.jsx)(dn,{children:n.map(((e,n)=>(0,Z.jsxs)("div",{className:"item "+(i===e?"active":""),onClick:()=>l(e),children:[(0,Z.jsx)("span",{className:"name",children:e}),t.includes(e)&&(0,Z.jsx)("img",{src:an.Q1,alt:"\u8bbf\u95ee\u8fc7\u7684"})]},n)))})};var un=i(47419),xn=i(11645),pn=i(32513),vn=i(83720),hn=i(36497),fn=i(97914),gn=i(64798),mn=i(74448);const{Item:yn}=nn.A,An=(0,t.forwardRef)((e=>{let{isBufferCurve:n}=e;const{t:i}=(0,tn.Bd)(),l=nn.A.useFormInstance(),s=nn.A.useWatch(["base","isName"],l),o=nn.A.useWatch(["base","sourceType"],l),a=(0,gn.A)(),d=(0,De.A)(),c=(0,Ge.A)(),u=(0,Fe.A)(),x=(0,t.useMemo)((()=>n?u:o===Ye?d:c),[o,n]),p=()=>{l.setFieldValue(["xAxis","name"],""),l.setFieldValue(["xAxis","unit"],""),l.setFieldValue(["yAxis","name"],""),l.setFieldValue(["yAxis","unit"],""),l.setFieldValue(["y2Axis","name"],""),l.setFieldValue(["y2Axis","unit"],""),l.setFieldValue(["curveGroup","yAxis","xSignal"],""),l.setFieldValue(["curveGroup","yAxis","ySignal"],[]),l.setFieldValue(["curveGroup","y2Axis","xSignal"],""),l.setFieldValue(["curveGroup","y2Axis","ySignal"],[])};return(0,Z.jsxs)("div",{className:"step-base",children:[(0,Z.jsxs)("div",{className:"centre",children:[(0,Z.jsxs)(un.A,{children:[(0,Z.jsx)(xn.A,{span:8,children:(0,Z.jsx)(yn,{name:["base","isName"],label:i("\u66f2\u7ebf\u63a7\u4ef6\u540d\u79f0"),valuePropName:"checked",labelCol:{span:12},children:(0,Z.jsx)(pn.A,{})})}),(0,Z.jsx)(xn.A,{span:12,pull:3,children:(0,Z.jsx)(yn,{name:["base","name"],children:(0,Z.jsx)(vn.A,{disabled:!s})})})]}),(0,Z.jsx)(un.A,{children:(0,Z.jsx)(xn.A,{span:12,children:(0,Z.jsx)(yn,{name:["base","sourceType"],label:i(n?"\u663e\u793a":"\u5173\u8054\u6570\u636e\u6e90"),children:(0,Z.jsx)(hn.A,{options:[{label:i(n?"\u8bd5\u6837":"\u4e8c\u7ef4\u6570\u7ec4"),value:Ye},{label:i(n?"\u7ed3\u679c\u6587\u4ef6":"\u4e8c\u7ef4\u6570\u7ec4\u96c6\u5408"),value:Ue}],onChange:()=>{p(),l.setFieldValue(["base","sourceInputCode"],""),l.setFieldValue(["pointTag","open"],!1),l.setFieldValue(["curveGroup","yAxis","curves"],{}),l.setFieldValue(["curveGroup","y2Axis","curves"],{})}})})})}),(0,Z.jsx)(un.A,{children:(0,Z.jsx)(xn.A,{span:12,children:(0,Z.jsx)(yn,{name:["base","sourceInputCode"],label:i(n?"buffer":"\u6570\u636e\u6e90\u53d8\u91cf"),children:(0,Z.jsx)(hn.A,{options:x,fieldNames:{label:"name",value:"code"},onChange:(e,i)=>{let t;p(),t=n?(0,mn.C)({sourceType:o}):(e=>{let{sourceType:n,arrayVar:i}=e,t=1;n===ee.xO.\u591a\u6570\u636e\u6e90&&(t=i.double_array_list_tab.number);const l={};for(let r=0;r<t;r+=1)l[r]={name:`\u4e8c\u7ef4\u6570\u7ec4[${r}]`,lines:[]};return l})({sourceType:o,arrayVar:i}),r.Ay.info("\u66f4\u65b0\u6570\u636e\u6e90\u540e\uff0c\u9700\u8981\u91cd\u65b0\u914d\u7f6e \u66f2\u7ebf\u7ec4\u4fe1\u53f7|\u6807\u6ce8\u7ed3\u679c|\u5750\u6807\u6e90"),l.setFieldValue(["pointTag","open"],!1),l.setFieldValue(["defineAxis"],{isDefineAxis:!1,inputCode:"",source:[]}),l.setFieldValue(["curveGroup","yAxis","curves"],t),l.setFieldValue(["curveGroup","y2Axis","curves"],t)}})})})}),(0,Z.jsx)(un.A,{children:(0,Z.jsx)(xn.A,{span:12,children:(0,Z.jsx)(yn,{name:["base","updateFreq"],label:i("\u663e\u793a\u66f4\u65b0\u9891\u7387"),children:(0,Z.jsx)(hn.A,{options:[{label:"0.1s",value:90},{label:"0.2s",value:180},{label:"0.5s",value:470},{label:"1s",value:900},{label:"2s",value:1900}]})})})}),n&&o===Ue&&(0,Z.jsxs)(Z.Fragment,{children:[(0,Z.jsx)(un.A,{children:(0,Z.jsx)(xn.A,{span:12,children:(0,Z.jsx)(yn,{name:["base","xOffset"],label:i("x-\u504f\u79fb"),children:(0,Z.jsx)(fn.A,{})})})}),(0,Z.jsx)(un.A,{children:(0,Z.jsx)(xn.A,{span:12,children:(0,Z.jsx)(yn,{name:["base","yOffset"],label:i("y-\u504f\u79fb"),children:(0,Z.jsx)(fn.A,{})})})})]})]}),(0,Z.jsxs)("div",{className:"centre",children:[(0,Z.jsx)("div",{className:"title",children:i("\u6fc0\u6d3b\u5341\u5b57\u7ebf\u5173\u8054\u53d8\u91cf")}),(0,Z.jsx)(un.A,{children:(0,Z.jsx)(xn.A,{span:12,children:(0,Z.jsx)(yn,{name:["base","crossInputCode"],label:i("\u7ed1\u5b9a\u53d8\u91cf"),children:(0,Z.jsx)(hn.A,{allowClear:!0,fieldNames:{label:"name",value:"code"},options:a})})})})]})]})}));var jn=i(35964),bn=i(12624),kn=i(56434),Cn=i.n(kn),Sn=i(29534),Tn=i(42999);const{Item:wn}=nn.A,In=e=>{let n,{value:i}=e;switch(null===i||void 0===i?void 0:i.signType){case We.o:n="\u25cf";break;case We["\u25bd"]:n="\u25bc";break;case We["\u25a1"]:n="\u25a0";break;default:n=null}return(0,Z.jsx)(Sn.A,{color:null===i||void 0===i?void 0:i.color,thickness:null!==i&&void 0!==i&&i.isLine?null===i||void 0===i?void 0:i.lineThickness:0,border:null===i||void 0===i?void 0:i.lineType,sign:null!==i&&void 0!==i&&i.isSign?n:null})},Ln=e=>{let{channels:n,open:i,setOpen:r,value:s,onChange:o,treeData:a}=e;const{t:d}=(0,tn.Bd)(),[c]=nn.A.useForm(),u=(0,l.d4)((e=>e.global.unitList)),[x,p]=(0,t.useState)(!1),[v,h]=(0,t.useState)(null),{colCode:f,dataSourceKey:g,colIndex:m}=(0,t.useMemo)((()=>{var e,n;const i=null===a||void 0===a||null===(e=a.map((e=>e.children)))||void 0===e||null===(n=e.flat())||void 0===n?void 0:n.find((e=>e.key===v));return v&&i?{colCode:i.code,dataSourceKey:i.dataSourceKey,colIndex:i.index}:{}}),[a,v]),y=(0,t.useMemo)((()=>{var e,i,t;const l=null===n||void 0===n||null===(e=n.find((e=>e.code===f)))||void 0===e?void 0:e.dimensionId;return null!==(i=null===(t=u.find((e=>e.id===l)))||void 0===t?void 0:t.units)&&void 0!==i?i:[]}),[n,u,f]);(0,t.useEffect)((()=>{h(null),c.setFieldsValue({value:s})}),[i]);return(0,Z.jsx)(ln.A,{open:i,title:d("\u66f2\u7ebf\u5c5e\u6027"),onCancel:()=>r(!1),width:(0,H.D0)("1300px"),footer:null,children:(0,Z.jsxs)("div",{style:{display:"flex",flexDirection:"column"},children:[(0,Z.jsx)(nn.A,{form:c,labelAlign:"left",disabled:!v,onValuesChange:(e,n)=>{if(null===e||void 0===e||!e.value)return;const i=Object.keys(e.value)[0],t=e.value[i].lines.length-1,l=n.value[i].lines[t],r=e.value[i].lines[t];if(!l)return;if((null===r||void 0===r||!r.yUnit)&&!x)return;const s=Cn()(n);if(null!==r&&void 0!==r&&r.yUnit&&Object.keys(s.value).forEach((e=>{var n;const i=null===(n=s.value[e])||void 0===n?void 0:n.lines;i&&i.forEach(((n,i)=>{n.code===l.code&&(s.value[e].lines[i].yUnit=r.yUnit)}))})),x){const e=["yUnit"],n=Object.keys(r).filter((n=>!e.includes(n)));0!==n.length&&Object.keys(s.value).forEach((e=>{const i=s.value[e];null!==i&&void 0!==i&&i.lines&&i.lines.forEach(((e,i)=>{e&&n.forEach((n=>{e[n]=r[n]}))}))}))}c.setFieldsValue(s)},children:(0,Z.jsxs)("div",{style:{display:"flex",height:"500px",overflow:"hidden"},children:[(0,Z.jsx)("div",{style:{width:"300px",borderRight:"1px solid #f0f0f0",overflow:"scroll"},children:(0,Z.jsx)(wn,{name:"value",children:(0,Z.jsx)(jn.A,{treeData:a,blockNode:!0,defaultExpandAll:!0,selectedKeys:v?[v]:[],onSelect:e=>{h(null===e||void 0===e?void 0:e[0])}})})}),(0,Z.jsxs)("div",{style:{flex:1,padding:"16px"},children:[(0,Z.jsx)("div",{style:{marginBottom:"16px"},children:(0,Z.jsxs)(un.A,{gutter:16,children:[(0,Z.jsx)(xn.A,{span:8,children:(0,Z.jsx)(wn,{name:["value",g,"lines",m,"name"],label:d("\u66f2\u7ebf\u540d\u79f0"),children:(0,Z.jsx)(vn.A,{})})}),(0,Z.jsx)(xn.A,{span:9,children:(0,Z.jsx)(wn,{name:["value",g,"lines",m,"yUnit"],label:d("\u5355\u4f4d"),children:(0,Z.jsx)(hn.A,{options:y,fieldNames:{label:"name",value:"id"}})})})]})}),(0,Z.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"3px"},children:[(0,Z.jsx)(wn,{children:(0,Z.jsx)(bn.A,{value:x,onChange:e=>{p(e)}})}),(0,Z.jsx)(wn,{label:d("\u5f00\u542f\u65f6\u7684\u540e\u7eed\u4fee\u6539\u4f1a\u540c\u6b65\u5230\u6240\u6709\u66f2\u7ebf"),colon:!1})]}),(0,Z.jsxs)("div",{style:{marginBottom:"16px"},children:[(0,Z.jsx)("div",{style:{fontWeight:"bold",marginBottom:"8px"},children:d("\u66f2\u7ebf\u70b9\u4e4b\u95f4\u7684\u8fde\u7ebf")}),(0,Z.jsxs)(un.A,{gutter:16,children:[(0,Z.jsx)(xn.A,{span:6,children:(0,Z.jsx)(wn,{name:["value",g,"lines",m,"isLine"],valuePropName:"checked",children:(0,Z.jsx)(pn.A,{children:d("\u663e\u793a\u8fde\u7ebf")})})}),(0,Z.jsx)(xn.A,{span:9,children:(0,Z.jsx)(wn,{name:["value",g,"lines",m,"lineType"],label:d("\u7ebf\u578b"),children:(0,Z.jsx)(hn.A,{options:Object.keys(ze).map((e=>({label:e,value:ze[e]})))})})}),(0,Z.jsx)(xn.A,{span:9,children:(0,Z.jsx)(wn,{name:["value",g,"lines",m,"lineThickness"],label:d("\u7ebf\u5bbd"),children:(0,Z.jsx)(fn.A,{min:1,addonAfter:"mm"})})})]})]}),(0,Z.jsxs)("div",{style:{marginBottom:"16px"},children:[(0,Z.jsx)("div",{style:{fontWeight:"bold",marginBottom:"8px"},children:d("\u66f2\u7ebf\u70b9\u7b26\u53f7")}),(0,Z.jsxs)(un.A,{gutter:16,children:[(0,Z.jsx)(xn.A,{span:6,children:(0,Z.jsx)(wn,{name:["value",g,"lines",m,"isSign"],valuePropName:"checked",children:(0,Z.jsx)(pn.A,{children:d("\u663e\u793a\u7b26\u53f7")})})}),(0,Z.jsx)(xn.A,{span:9,children:(0,Z.jsx)(wn,{name:["value",g,"lines",m,"signType"],label:d("\u7b26\u53f7\u7c7b\u578b"),children:(0,Z.jsx)(hn.A,{options:Object.keys(We).map((e=>({label:e,value:We[e]})))})})}),(0,Z.jsx)(xn.A,{span:9,children:(0,Z.jsx)(wn,{name:["value",g,"lines",m,"signEach"],valuePropName:"checked",children:(0,Z.jsx)(pn.A,{children:d("\u6bcf\u4e2a\u70b9\u663e\u793a")})})})]})]}),(0,Z.jsx)("div",{children:(0,Z.jsxs)(un.A,{gutter:16,children:[(0,Z.jsxs)(xn.A,{span:12,children:[(0,Z.jsx)("div",{style:{fontWeight:"bold",marginBottom:"8px"},children:d("\u989c\u8272")}),(0,Z.jsx)(wn,{name:["value",g,"lines",m,"color"],children:(0,Z.jsx)(Tn.A,{disabled:-1===g})})]}),(0,Z.jsxs)(xn.A,{span:12,children:[(0,Z.jsx)("div",{style:{fontWeight:"bold",marginBottom:"8px"},children:d("\u793a\u4f8b\u9884\u89c8")}),(0,Z.jsx)(wn,{name:["value",g,"lines",m],children:(0,Z.jsx)(In,{})})]})]})})]})]})}),(0,Z.jsxs)("div",{style:{display:"flex",justifyContent:"flex-end",alignItems:"center",padding:"16px",borderTop:"1px solid #f0f0f0"},children:[(0,Z.jsx)(rn.A,{onClick:async()=>{const e=await c.validateFields();o(e.value),r(!1)},type:"primary",style:{marginRight:"8px"},children:d("\u786e\u5b9a")}),(0,Z.jsx)(rn.A,{onClick:()=>r(!1),style:{marginRight:"8px"},children:d("\u53d6\u6d88")}),(0,Z.jsx)(rn.A,{children:d("\u5e2e\u52a9")})]})]})})},Mn=e=>{let{channels:n,value:i,onChange:l}=e;const{t:r}=(0,tn.Bd)(),[s,o]=(0,t.useState)(!1),a=nn.A.useWatch(["base","sourceType"]),{getSamples:d}=(0,$e.A)(),c=(0,t.useMemo)((()=>{if(!i||"object"!==typeof i)return[];if(a===ee.xO.\u5355\u6570\u636e\u6e90){const e="optSample",t=i[e],l=(null===t||void 0===t?void 0:t.lines)||[];return[{title:"\u6240\u9009\u8bd5\u6837",key:e,disabled:!0,children:l.map(((i,t)=>{var l,r;return{dataSourceKey:e,index:t,title:null!==(l=null===(r=n.find((e=>e.code===i.code)))||void 0===r?void 0:r.name)&&void 0!==l?l:i.code,key:`${e}-${t}`,code:i.code}}))}]}return d().map((e=>{const t=e.code,l=i[t],r=(null===l||void 0===l?void 0:l.lines)||[];return{title:e.name,key:t,disabled:!0,children:r.map(((e,i)=>{var l,r;return{dataSourceKey:t,index:i,title:null!==(l=null===(r=n.find((n=>n.code===e.code)))||void 0===r?void 0:r.name)&&void 0!==l?l:e.code,key:`${t}-${i}`,code:e.code}}))}}))}),[i,d,a]);return(0,Z.jsxs)(Z.Fragment,{children:[(0,Z.jsx)(rn.A,{onClick:()=>o(!0),children:r("\u66f2\u7ebf\u6837\u5f0f")}),s&&(0,Z.jsx)(Ln,{channels:n,treeData:c,open:s,setOpen:o,value:i,onChange:l})]})},Nn=e=>{let{channels:n,value:i,onChange:l}=e;const{t:r}=(0,tn.Bd)(),[s,o]=(0,t.useState)(!1),a=(0,t.useMemo)((()=>i&&"object"===typeof i?Object.keys(i).map((e=>{const t=i[e],l=(null===t||void 0===t?void 0:t.lines)||[];return{title:`\u4e8c\u7ef4\u6570\u7ec4[${e}]`,key:`${e}`,disabled:!0,children:l.map(((i,t)=>{var l,r;return{dataSourceKey:e,index:t,title:null!==(l=null===(r=n.find((e=>e.code===i.code)))||void 0===r?void 0:r.name)&&void 0!==l?l:i.code,key:`${e}-${t}`,code:i.code}}))}})):[]),[i,n]);return(0,Z.jsxs)(Z.Fragment,{children:[(0,Z.jsx)(rn.A,{onClick:()=>o(!0),children:r("\u66f2\u7ebf\u6837\u5f0f")}),s&&(0,Z.jsx)(Ln,{channels:n,treeData:a,open:s,setOpen:o,value:i,onChange:l})]})},$n=e=>{let{isBufferCurve:n,channels:i,value:t,onChange:l}=e;return n?(0,Z.jsx)(Mn,{channels:i,value:t,onChange:l}):(0,Z.jsx)(Nn,{channels:i,value:t,onChange:l})},{Item:En,useWatch:Rn}=nn.A,On=e=>{let{channels:n,isBufferCurve:i}=e;const{t:t}=(0,tn.Bd)(),l=nn.A.useWatch(["base"]);return(0,Z.jsx)(Z.Fragment,{children:(0,Z.jsxs)("div",{className:"step-curve",children:[(0,Z.jsxs)("div",{className:"centre",children:[(0,Z.jsx)("div",{className:"right-float",children:(0,Z.jsx)(En,{name:["curveGroup","yAxis","curves"],noStyle:!0,children:(0,Z.jsx)($n,{channels:n,isBufferCurve:i})})}),(0,Z.jsx)(En,{hidden:!0,name:["curveGroup","yAxis","curves"],children:(0,Z.jsx)(vn.A,{})}),(0,Z.jsx)(un.A,{children:(0,Z.jsx)(xn.A,{hidden:!0,span:12,children:(0,Z.jsx)(En,{name:["curveGroup","yAxis","isEnable"],label:t("\u542f\u7528"),valuePropName:"checked",children:(0,Z.jsx)(pn.A,{})})})}),(0,Z.jsx)(un.A,{children:(0,Z.jsx)(xn.A,{span:12,children:(0,Z.jsx)(En,{name:["curveGroup","yAxis","name"],label:t("\u66f2\u7ebf\u5206\u7ec4\u540d\u79f0"),children:(0,Z.jsx)(vn.A,{})})})}),(0,Z.jsx)(un.A,{children:(0,Z.jsx)(xn.A,{span:12,children:(0,Z.jsx)(En,{name:["curveGroup","yAxis","xSignal"],label:t("X\u8f74"),children:(0,Z.jsx)(hn.A,{fieldNames:{label:"name",value:"code"},options:n})})})}),(0,Z.jsx)(un.A,{children:(0,Z.jsx)(xn.A,{span:12,children:(0,Z.jsx)(nn.A.Item,{noStyle:!0,children:(0,Z.jsx)(En,{name:["curveGroup","yAxis","ySignal"],label:"Y1\u8f74",children:(0,Z.jsx)(hn.A,{fieldNames:{label:"name",value:"code"},mode:"multiple",maxCount:(null===l||void 0===l?void 0:l.sourceType)===Ue?1:void 0,options:n})})})})})]}),(0,Z.jsxs)("div",{className:"centre",children:[(0,Z.jsx)("div",{className:"right-float",children:(0,Z.jsx)(En,{name:["curveGroup","y2Axis","curves"],noStyle:!0,children:(0,Z.jsx)($n,{channels:n,isBufferCurve:i})})}),(0,Z.jsx)(En,{hidden:!0,name:["curveGroup","y2Axis","curves"],children:(0,Z.jsx)(vn.A,{})}),(0,Z.jsx)(un.A,{children:(0,Z.jsx)(xn.A,{span:12,children:(0,Z.jsx)(En,{name:["curveGroup","y2Axis","isEnable"],label:t("\u542f\u7528"),valuePropName:"checked",children:(0,Z.jsx)(pn.A,{})})})}),(0,Z.jsx)(un.A,{children:(0,Z.jsx)(xn.A,{span:12,children:(0,Z.jsx)(En,{name:["curveGroup","y2Axis","name"],label:t("\u66f2\u7ebf\u5206\u7ec4\u540d\u79f0"),children:(0,Z.jsx)(vn.A,{})})})}),(0,Z.jsx)(un.A,{children:(0,Z.jsx)(xn.A,{span:12,children:(0,Z.jsx)(En,{name:["curveGroup","y2Axis","xSignal"],label:t("X\u8f74"),children:(0,Z.jsx)(hn.A,{fieldNames:{label:"name",value:"code"},options:n})})})}),(0,Z.jsx)(un.A,{children:(0,Z.jsx)(xn.A,{span:12,children:(0,Z.jsx)(nn.A.Item,{shouldUpdate:!0,noStyle:!0,children:(0,Z.jsx)(En,{name:["curveGroup","y2Axis","ySignal"],label:"Y2\u8f74",children:(0,Z.jsx)(hn.A,{fieldNames:{label:"name",value:"code"},mode:"multiple",maxCount:(null===l||void 0===l?void 0:l.sourceType)===Ue?1:void 0,options:n})})})})})]})]})})};var Pn=i(96603);const{Item:_n}=nn.A,{Group:Vn}=Pn.Ay,Bn=(0,t.forwardRef)((e=>{let{channels:n}=e;const{t:i}=(0,tn.Bd)(),r=(0,l.d4)((e=>e.global.unitList)),s=nn.A.useWatch(["xAxis","proportionType"]),o=nn.A.useWatch(["curveGroup","yAxis","xSignal"]),a=nn.A.useWatch(["xAxis","isGrid"]),d=nn.A.useWatch(["xAxis","isZeroLine"]),c=(0,t.useMemo)((()=>{var e,i,t,l;const s=null!==(e=null===n||void 0===n||null===(i=n.find((e=>e.code===o)))||void 0===i?void 0:i.dimensionId)&&void 0!==e?e:{dimensionId:""};return null!==(t=null===(l=r.find((e=>e.id===s)))||void 0===l?void 0:l.units)&&void 0!==t?t:[]}),[n,r,o]),u=[He["\u6570\u636e\u8303\u56f4"],He["\u626b\u63cf\u8303\u56f4"]];return(0,Z.jsxs)("div",{className:"step-axis",children:[(0,Z.jsxs)("div",{className:"centre",children:[(0,Z.jsx)("div",{className:"title",children:i("\u8f74\u7ebf")}),(0,Z.jsxs)(un.A,{children:[(0,Z.jsx)(xn.A,{span:8,children:(0,Z.jsx)(_n,{name:["xAxis","name"],label:i("\u540d\u79f0"),children:(0,Z.jsx)(vn.A,{})})}),(0,Z.jsx)(xn.A,{span:8,children:(0,Z.jsx)(_n,{name:["xAxis","unit"],label:i("\u5355\u4f4d"),children:(0,Z.jsx)(hn.A,{options:c,fieldNames:{label:"name",value:"id"}})})})]})]}),(0,Z.jsxs)("div",{className:"centre",children:[(0,Z.jsx)("div",{className:"title",children:i("\u8f74\u663e\u793a")}),(0,Z.jsxs)(un.A,{children:[(0,Z.jsx)(xn.A,{span:8,children:(0,Z.jsx)(_n,{name:["xAxis","proportionType"],label:i("\u81ea\u52a8\u8c03\u6574\u6bd4\u4f8b"),children:(0,Z.jsx)(hn.A,{options:Ke(He)})})}),(0,Z.jsx)(xn.A,{span:8,children:(0,Z.jsx)(_n,{name:["xAxis","lowLimit"],label:i("\u4e0b\u9650"),children:(0,Z.jsx)(fn.A,{disabled:u.includes(s)})})}),(0,Z.jsx)(xn.A,{span:8,children:(0,Z.jsx)(_n,{name:["xAxis","upLimit"],label:i("\u4e0a\u9650"),children:(0,Z.jsx)(fn.A,{disabled:u.includes(s)})})})]}),(0,Z.jsxs)(un.A,{children:[(0,Z.jsx)(xn.A,{span:8,children:(0,Z.jsx)(_n,{name:["xAxis","lastRange"],label:i("\u626b\u63cf\u8303\u56f4"),children:(0,Z.jsx)(fn.A,{style:{width:"100%"},disabled:s!==He["\u626b\u63cf\u8303\u56f4"]})})}),(0,Z.jsx)(xn.A,{span:8,children:(0,Z.jsx)(_n,{name:["xAxis","isLog"],label:"",valuePropName:"checked",children:(0,Z.jsx)(pn.A,{children:i("\u5bf9\u6570")})})})]})]}),(0,Z.jsxs)("div",{className:"centre",children:[(0,Z.jsx)("div",{className:"title",children:i("\u663e\u793a")}),(0,Z.jsxs)(un.A,{children:[(0,Z.jsx)(xn.A,{span:8,children:(0,Z.jsx)(_n,{name:["xAxis","type"],label:i("\u8f74\u7ebf"),initialValue:"solid",children:(0,Z.jsx)(hn.A,{options:Ke(ze)})})}),(0,Z.jsx)(xn.A,{span:8,children:(0,Z.jsx)(_n,{name:["xAxis","thickness"],label:i("\u5bbd\u5ea6"),initialValue:1,children:(0,Z.jsx)(fn.A,{min:1})})}),(0,Z.jsx)(xn.A,{span:8,children:(0,Z.jsx)(_n,{name:["xAxis","color"],label:"",initialValue:"#000",children:(0,Z.jsx)(Tn.A,{})})})]}),(0,Z.jsxs)(un.A,{children:[(0,Z.jsx)(xn.A,{span:8,children:(0,Z.jsx)(_n,{name:["xAxis","gridType"],label:(0,Z.jsx)(Z.Fragment,{children:(0,Z.jsx)(_n,{noStyle:!0,name:["xAxis","isGrid"],valuePropName:"checked",children:(0,Z.jsx)(pn.A,{children:i("\u7f51\u683c\u7ebf")})})}),initialValue:"solid",children:(0,Z.jsx)(hn.A,{disabled:!a,options:Ke(ze)})})}),(0,Z.jsx)(xn.A,{span:8,children:(0,Z.jsx)(_n,{name:["xAxis","gridThickness"],label:i("\u5bbd\u5ea6"),initialValue:1,children:(0,Z.jsx)(fn.A,{disabled:!a,min:1})})}),(0,Z.jsx)(xn.A,{span:8,children:(0,Z.jsx)(_n,{name:["xAxis","gridColor"],label:"",initialValue:"#000",children:(0,Z.jsx)(Tn.A,{disabled:!a})})})]}),(0,Z.jsxs)(un.A,{children:[(0,Z.jsx)(xn.A,{span:8,children:(0,Z.jsx)(_n,{name:["xAxis","zeroLineType"],label:(0,Z.jsx)(Z.Fragment,{children:(0,Z.jsx)(_n,{noStyle:!0,name:["xAxis","isZeroLine"],valuePropName:"checked",children:(0,Z.jsx)(pn.A,{children:i("\u96f6\u7ebf")})})}),initialValue:"solid",children:(0,Z.jsx)(hn.A,{disabled:!d,options:Ke(ze)})})}),(0,Z.jsx)(xn.A,{span:8,children:(0,Z.jsx)(_n,{name:["xAxis","zeroLineThickness"],label:i("\u5bbd\u5ea6"),initialValue:1,children:(0,Z.jsx)(fn.A,{disabled:!d,min:1})})}),(0,Z.jsx)(xn.A,{span:8,children:(0,Z.jsx)(_n,{name:["xAxis","zeroLineColor"],label:"",initialValue:"#000",children:(0,Z.jsx)(Tn.A,{disabled:!d})})})]})]})]})})),{Item:Dn}=nn.A,{Group:Gn}=Pn.Ay,Fn=(0,t.forwardRef)((()=>{const{t:e}=(0,tn.Bd)(),n=nn.A.useWatch(["yAxis","proportionType"]),i=(nn.A.useWatch(["curveGroup","yAxis","ySignal"]),nn.A.useWatch(["yAxis","isGrid"])),t=nn.A.useWatch(["yAxis","isZeroLine"]),l=[He["\u6570\u636e\u8303\u56f4"],He["\u626b\u63cf\u8303\u56f4"]];return(0,Z.jsxs)("div",{className:"step-axis",children:[(0,Z.jsxs)("div",{className:"centre",children:[(0,Z.jsx)("div",{className:"title",children:e("\u8f74\u7ebf")}),(0,Z.jsx)(un.A,{children:(0,Z.jsx)(xn.A,{span:8,children:(0,Z.jsx)(Dn,{name:["yAxis","name"],label:e("\u540d\u79f0"),children:(0,Z.jsx)(vn.A,{})})})})]}),(0,Z.jsxs)("div",{className:"centre",children:[(0,Z.jsx)("div",{className:"title",children:e("\u8f74\u663e\u793a")}),(0,Z.jsxs)(un.A,{children:[(0,Z.jsx)(xn.A,{span:8,children:(0,Z.jsx)(Dn,{name:["yAxis","proportionType"],label:e("\u81ea\u52a8\u8c03\u6574\u6bd4\u4f8b"),children:(0,Z.jsx)(hn.A,{options:Ke(He)})})}),(0,Z.jsx)(xn.A,{span:8,children:(0,Z.jsx)(Dn,{name:["yAxis","lowLimit"],label:e("\u4e0b\u9650"),children:(0,Z.jsx)(fn.A,{disabled:l.includes(n)})})}),(0,Z.jsx)(xn.A,{span:8,children:(0,Z.jsx)(Dn,{name:["yAxis","upLimit"],label:e("\u4e0a\u9650"),children:(0,Z.jsx)(fn.A,{disabled:l.includes(n)})})})]}),(0,Z.jsxs)(un.A,{children:[(0,Z.jsx)(xn.A,{span:8,children:(0,Z.jsx)(Dn,{name:["yAxis","lastRange"],label:e("\u626b\u63cf\u8303\u56f4"),children:(0,Z.jsx)(fn.A,{style:{width:"100%"},disabled:n!==He["\u626b\u63cf\u8303\u56f4"]})})}),(0,Z.jsx)(xn.A,{span:8,children:(0,Z.jsx)(Dn,{name:["yAxis","isLog"],label:"",valuePropName:"checked",children:(0,Z.jsx)(pn.A,{children:e("\u5bf9\u6570")})})})]})]}),(0,Z.jsxs)("div",{className:"centre",children:[(0,Z.jsx)("div",{className:"title",children:e("\u663e\u793a")}),(0,Z.jsxs)(un.A,{children:[(0,Z.jsx)(xn.A,{span:8,children:(0,Z.jsx)(Dn,{name:["yAxis","type"],label:e("\u8f74\u7ebf"),initialValue:"solid",children:(0,Z.jsx)(hn.A,{options:Ke(ze)})})}),(0,Z.jsx)(xn.A,{span:8,children:(0,Z.jsx)(Dn,{name:["yAxis","thickness"],label:e("\u5bbd\u5ea6"),initialValue:1,children:(0,Z.jsx)(fn.A,{min:1})})}),(0,Z.jsx)(xn.A,{span:8,children:(0,Z.jsx)(Dn,{name:["yAxis","color"],label:"",initialValue:"#000",children:(0,Z.jsx)(Tn.A,{})})})]}),(0,Z.jsxs)(un.A,{children:[(0,Z.jsx)(xn.A,{span:8,children:(0,Z.jsx)(Dn,{name:["yAxis","gridType"],label:(0,Z.jsx)(Z.Fragment,{children:(0,Z.jsx)(Dn,{noStyle:!0,name:["yAxis","isGrid"],valuePropName:"checked",children:(0,Z.jsx)(pn.A,{children:e("\u7f51\u683c\u7ebf")})})}),initialValue:"solid",children:(0,Z.jsx)(hn.A,{disabled:!i,options:Ke(ze)})})}),(0,Z.jsx)(xn.A,{span:8,children:(0,Z.jsx)(Dn,{name:["yAxis","gridThickness"],label:e("\u5bbd\u5ea6"),initialValue:1,children:(0,Z.jsx)(fn.A,{disabled:!i,min:1})})}),(0,Z.jsx)(xn.A,{span:8,children:(0,Z.jsx)(Dn,{name:["yAxis","gridColor"],label:"",initialValue:"#000000",children:(0,Z.jsx)(Tn.A,{disabled:!i})})})]}),(0,Z.jsxs)(un.A,{children:[(0,Z.jsx)(xn.A,{span:8,children:(0,Z.jsx)(Dn,{label:(0,Z.jsx)(Z.Fragment,{children:(0,Z.jsx)(Dn,{noStyle:!0,name:["yAxis","isZeroLine"],valuePropName:"checked",children:(0,Z.jsx)(pn.A,{children:e("\u96f6\u7ebf")})})}),name:["yAxis","zeroLineType"],initialValue:ze["\u2014\u2014\u2014\u2014\u2014\u2014"],children:(0,Z.jsx)(hn.A,{disabled:!t,options:Ke(ze)})})}),(0,Z.jsx)(xn.A,{span:8,children:(0,Z.jsx)(Dn,{name:["yAxis","zeroLineThickness"],label:e("\u5bbd\u5ea6"),initialValue:1,children:(0,Z.jsx)(fn.A,{disabled:!t,min:1})})}),(0,Z.jsx)(xn.A,{span:8,children:(0,Z.jsx)(Dn,{name:["yAxis","zeroLineColor"],label:"",initialValue:"#000",children:(0,Z.jsx)(Tn.A,{disabled:!t})})})]})]})]})})),{Item:Xn}=nn.A,{Group:Yn}=Pn.Ay,Un=(0,t.forwardRef)((()=>{const{t:e}=(0,tn.Bd)(),n=nn.A.useWatch(["y2Axis","proportionType"]),i=nn.A.useWatch(["y2Axis","isGrid"]),t=nn.A.useWatch(["y2Axis","isZeroLine"]),l=[He["\u6570\u636e\u8303\u56f4"],He["\u626b\u63cf\u8303\u56f4"]];return(0,Z.jsxs)("div",{className:"step-axis",children:[(0,Z.jsxs)("div",{className:"centre",children:[(0,Z.jsx)("div",{className:"title",children:e("\u8f74\u7ebf")}),(0,Z.jsx)(un.A,{children:(0,Z.jsx)(xn.A,{span:8,children:(0,Z.jsx)(Xn,{name:["y2Axis","name"],label:e("\u540d\u79f0"),children:(0,Z.jsx)(vn.A,{})})})})]}),(0,Z.jsxs)("div",{className:"centre",children:[(0,Z.jsx)("div",{className:"title",children:e("\u8f74\u663e\u793a")}),(0,Z.jsxs)(un.A,{children:[(0,Z.jsx)(xn.A,{span:8,children:(0,Z.jsx)(Xn,{name:["y2Axis","proportionType"],label:e("\u81ea\u52a8\u8c03\u6574\u6bd4\u4f8b"),children:(0,Z.jsx)(hn.A,{options:Ke(He)})})}),(0,Z.jsx)(xn.A,{span:8,children:(0,Z.jsx)(Xn,{name:["y2Axis","lowLimit"],label:e("\u4e0b\u9650"),children:(0,Z.jsx)(fn.A,{disabled:l.includes(n)})})}),(0,Z.jsx)(xn.A,{span:8,children:(0,Z.jsx)(Xn,{name:["y2Axis","upLimit"],label:e("\u4e0a\u9650"),children:(0,Z.jsx)(fn.A,{disabled:l.includes(n)})})})]}),(0,Z.jsxs)(un.A,{children:[(0,Z.jsx)(xn.A,{span:8,children:(0,Z.jsx)(Xn,{name:["y2Axis","lastRange"],label:e("\u626b\u63cf\u8303\u56f4"),children:(0,Z.jsx)(fn.A,{style:{width:"100%"},disabled:n!==He["\u626b\u63cf\u8303\u56f4"]})})}),(0,Z.jsx)(xn.A,{span:8,children:(0,Z.jsx)(Xn,{name:["y2Axis","isLog"],label:"",valuePropName:"checked",children:(0,Z.jsx)(pn.A,{children:e("\u5bf9\u6570")})})})]})]}),(0,Z.jsxs)("div",{className:"centre",children:[(0,Z.jsx)("div",{className:"title",children:e("\u663e\u793a")}),(0,Z.jsxs)(un.A,{children:[(0,Z.jsx)(xn.A,{span:8,children:(0,Z.jsx)(Xn,{name:["y2Axis","type"],label:e("\u8f74\u7ebf"),initialValue:"solid",children:(0,Z.jsx)(hn.A,{options:Ke(ze)})})}),(0,Z.jsx)(xn.A,{span:8,children:(0,Z.jsx)(Xn,{name:["y2Axis","thickness"],label:e("\u5bbd\u5ea6"),initialValue:1,children:(0,Z.jsx)(fn.A,{min:1})})}),(0,Z.jsx)(xn.A,{span:8,children:(0,Z.jsx)(Xn,{name:["y2Axis","color"],label:"",initialValue:"#000",children:(0,Z.jsx)(Tn.A,{})})})]}),(0,Z.jsxs)(un.A,{children:[(0,Z.jsx)(xn.A,{span:8,children:(0,Z.jsx)(Xn,{name:["y2Axis","gridType"],label:(0,Z.jsx)(Z.Fragment,{children:(0,Z.jsx)(Xn,{noStyle:!0,name:["y2Axis","isGrid"],valuePropName:"checked",children:(0,Z.jsx)(pn.A,{children:e("\u7f51\u683c\u7ebf")})})}),initialValue:"solid",children:(0,Z.jsx)(hn.A,{disabled:!i,options:Ke(ze)})})}),(0,Z.jsx)(xn.A,{span:8,children:(0,Z.jsx)(Xn,{name:["y2Axis","gridThickness"],label:e("\u5bbd\u5ea6"),initialValue:1,children:(0,Z.jsx)(fn.A,{disabled:!i,min:1})})}),(0,Z.jsx)(xn.A,{span:8,children:(0,Z.jsx)(Xn,{name:["y2Axis","gridColor"],label:"",initialValue:"#000",children:(0,Z.jsx)(Tn.A,{disabled:!i})})})]}),(0,Z.jsxs)(un.A,{children:[(0,Z.jsx)(xn.A,{span:8,children:(0,Z.jsx)(Xn,{name:["y2Axis","zeroLineType"],label:(0,Z.jsx)(Z.Fragment,{children:(0,Z.jsx)(Xn,{noStyle:!0,name:["y2Axis","isZeroLine"],valuePropName:"checked",children:(0,Z.jsx)(pn.A,{children:e("\u96f6\u7ebf")})})}),initialValue:"solid",children:(0,Z.jsx)(hn.A,{disabled:!t,options:Ke(ze)})})}),(0,Z.jsx)(xn.A,{span:8,children:(0,Z.jsx)(Xn,{name:["y2Axis","zeroLineThickness"],label:e("\u5bbd\u5ea6"),initialValue:1,children:(0,Z.jsx)(fn.A,{disabled:!t,min:1})})}),(0,Z.jsx)(xn.A,{span:8,children:(0,Z.jsx)(Xn,{name:["y2Axis","zeroLineColor"],label:"",initialValue:"#000",children:(0,Z.jsx)(Tn.A,{disabled:!t})})})]})]})]})}));var zn=i(10202),Hn=i(232);const{Item:Wn}=nn.A,Kn=(0,t.forwardRef)((e=>{let{isBufferCurve:n}=e;const{t:i}=(0,tn.Bd)(),{auxiliaryLineArrayList:t,auxiliaryLineSignalList:l}=(0,Hn.A)(),r=nn.A.useFormInstance();return(0,Z.jsx)("div",{className:"step-auxiliary",children:(0,Z.jsx)(Wn,{noStyle:!0,name:["auxiliary"],valuePropName:"targetKeys",children:(0,Z.jsx)(zn.A,{dataSource:n?l:t,onChangeDelWay:e=>{const n=r.getFieldValue("auxiliary");r.setFieldValue("auxiliary",n.filter((n=>n!==e.id)))},render:e=>e.name,oneWayLabel:"name",oneWay:!0})})})}));var Zn=i(6051),qn=i(9339);const{Item:Qn}=nn.A,Jn=e=>{let{value:n,onChange:i}=e;const{t:l}=(0,tn.Bd)(),s=(0,gn.A)(),o=(0,t.useRef)(),[a,d]=(0,t.useState)(void 0),c=[{title:l("\u6807\u8bb0\u70b9"),width:(0,H.D0)("160px"),dataIndex:"name"},{title:l("\u7ed1\u5b9a\u7684\u53d8\u91cf"),dataIndex:"inputCode",render:(e,t)=>(0,Z.jsx)(hn.A,{options:s,value:e,style:{width:(0,H.D0)("160px")},fieldNames:{label:"name",value:"code"},onChange:e=>((e,t)=>{const l=n.map((n=>e.key===n.key?{...n,inputCode:t}:n));i(l),o.current=l})(t,e)})}],u={type:"radio",onChange:e=>{d(e[0])}};return(0,Z.jsxs)(Z.Fragment,{children:[(0,Z.jsxs)("div",{className:"header",children:[(0,Z.jsx)("div",{children:l("\u6807\u8bb0\u70b9\u8bbe\u7f6e")}),(0,Z.jsxs)(Zn.A,{children:[(0,Z.jsx)(rn.A,{onClick:()=>{const e=[...n,{key:crypto.randomUUID(),name:`\u70b9${(null===n||void 0===n?void 0:n.length)+1}`,inputCode:""}];i(e),o.current=e},children:l("\u65b0\u5efa")}),(0,Z.jsx)(rn.A,{onClick:()=>{if(a){const e=n.filter((e=>e.key!==a)).map(((e,n)=>({...e,name:`\u70b9${n+1}`})));i(e),o.current=e,r.Ay.success(l("\u5220\u9664\u6210\u529f"))}else r.Ay.error(l("\u8bf7\u9009\u62e9\u8981\u5220\u9664\u7684\u6570\u636e"))},children:l("\u5220\u9664")})]})]}),(0,Z.jsx)(qn.A,{rowKey:"key",rowSelection:u,dataSource:n,columns:c,scroll:{y:550},pagination:!1})]})},ei=()=>(0,Z.jsx)("div",{className:"step-marker",children:(0,Z.jsx)(Qn,{noStyle:!0,name:["marker"],children:(0,Z.jsx)(Jn,{})})});var ni=i(51238),ii=i(16133);const ti=`1px solid ${H.o$.borderGray}`,li=a.Ay.div`
    background-color: #fff;
    padding: 10px;
    .title {
        font-size: 13px;
        font-weight: bold;
        margin-bottom: ${(0,H.D0)("15px")};
    }
    >.footer-btns {
        display: flex;
        justify-content: center;
        padding: ${(0,H.D0)("20px")} 0;
        border-top: ${ti};
        button {
            margin-right: ${(0,H.D0)("10px")};
        }
    }

    .content{
        display: flex;
        
    }

    .chunk {
        border: ${ti};
        padding: ${(0,H.D0)("10px")};
        margin-top: 10px;
    }

`,ri={resultVariableId:"",isAbbr:!0,isName:!0,isVal:!0,isChunk:!0,isLine:!0,isSample:!1,sampleCode:"",color:"#000000"},{Item:si}=nn.A,oi=e=>{var n;let{open:i,setOpen:r,value:s,onChange:o,treeData:a,axis:d,setAxis:c,isBufferCurve:u,sourceType:x}=e;const{t:p}=(0,tn.Bd)(),v=(0,l.d4)((e=>e.template.resultData)),h=(0,l.d4)((e=>e.template.resultTestData)),{getCurveResults:f}=(0,ii.A)(),[g]=nn.A.useForm(),[m,y]=(0,t.useState)(!1),A=(0,t.useRef)(),j=(0,t.useMemo)((()=>f()),[v,h]),[b,k]=(0,t.useState)(),[C,S]=(0,t.useState)(),[T,w]=(0,t.useState)(null),[I,L]=(0,t.useState)(null),[M,N]=(0,t.useState)(null);(0,t.useEffect)((()=>{A.current=Cn()(s),L(null),w(null),k(null),S(void 0),N(null),g.resetFields()}),[i]);const $=()=>{r(!1)};return(0,Z.jsx)(ln.A,{open:i,onCancel:$,width:1e3,title:p("\u7ed3\u679c\u6807\u7b7e"),destroyOnClose:!0,footer:null,children:(0,Z.jsxs)(li,{children:[(0,Z.jsx)(un.A,{children:(0,Z.jsx)(xn.A,{span:24,children:(0,Z.jsx)(si,{label:p("\u66f2\u7ebf\u7ec4"),children:(0,Z.jsx)(hn.A,{value:d,options:[{label:"Y1\u8f74",value:"yAxis"},{label:"Y2\u8f74",value:"y2Axis"}],onChange:e=>{c(e),w(null),k(null),S(void 0),L(null),g.resetFields()}})})})}),(0,Z.jsxs)(un.A,{children:[(0,Z.jsx)(xn.A,{span:8,children:(0,Z.jsx)("div",{style:{height:"700px",overflow:"scroll"},children:(0,Z.jsx)(jn.A,{treeData:a,blockNode:!0,defaultExpandAll:!0,selectedKeys:I?[I]:[],onSelect:(e,n)=>{const i=null===e||void 0===e?void 0:e[0];if(L(i),N(null),i&&n.node){var t;const{dataSourceKey:e,index:i,code:l}=n.node;w({dataSourceKey:e,index:i}),S(void 0),N(null),g.resetFields(),k(null!==(t=A.current[d].curves[e].lines[i].pointTags)&&void 0!==t?t:[])}else w(null),k(null),S(void 0),N(null),g.resetFields()}})})}),(0,Z.jsx)(xn.A,{span:16,children:(0,Z.jsxs)("div",{className:"chunk",children:[(0,Z.jsx)(zn.A,{disabled:!b,listStyle:{width:"16vw",height:"45vh"},targetKeys:null!==(n=null===b||void 0===b?void 0:b.map((e=>e.resultVariableId)))&&void 0!==n?n:[],select:M,onChange:e=>{k((n=>{const i=e.map((e=>({...ri,resultVariableId:e,id:(0,ni.A)(),...n.find((n=>n.resultVariableId===e))})));if(T&&A.current&&A.current[d]&&A.current[d].curves){const{dataSourceKey:e,index:n}=T;A.current[d].curves[e]&&A.current[d].curves[e].lines[n]&&(A.current[d].curves[e].lines[n].pointTags=i)}return i}))},dataSource:j,onChangeWay:async e=>{if(N(e),e){const n=b.findIndex((n=>n.resultVariableId===e.result_variable_id));S(n);const i=b.find((n=>n.resultVariableId===e.result_variable_id));g.setFieldsValue(i)}},onChangeDelWay:async e=>{if(e){const n=b.filter((n=>n.resultVariableId!==e.result_variable_id));if(k(n),T&&A.current&&A.current[d]&&A.current[d].curves){const{dataSourceKey:e,index:i}=T;A.current[d].curves[e]&&A.current[d].curves[e].lines[i]&&(A.current[d].curves[e].lines[i].pointTags=n)}void 0!==C&&b[C]&&b[C].resultVariableId===e.result_variable_id&&(S(void 0),N(null),g.resetFields())}},oneWay:!0,oneWayLabel:"variable_name",rowKey:"result_variable_id",render:e=>e.variable_name}),(0,Z.jsxs)(nn.A,{form:g,disabled:void 0===C,onValuesChange:(e,n)=>{if(void 0!==C&&b){const i=[...b];if(i[C]={...i[C],...n},k(i),T&&A.current&&A.current[d]&&A.current[d].curves){const{dataSourceKey:e,index:n}=T;A.current[d].curves[e]&&A.current[d].curves[e].lines[n]&&(A.current[d].curves[e].lines[n].pointTags=i)}if(m&&T){const n=["resultVariableId","id"],t=Object.keys(e).filter((e=>!n.includes(e)));if(t.length>0){const n=i.map((n=>{const i={...n};return t.forEach((n=>{i[n]=e[n]})),i}));k(n);const{dataSourceKey:l,index:r}=T;A.current[d].curves[l]&&A.current[d].curves[l].lines[r]&&(A.current[d].curves[l].lines[r].pointTags=n)}}}},children:[(0,Z.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"3px"},children:[(0,Z.jsx)(si,{children:(0,Z.jsx)(bn.A,{value:m,onChange:e=>y(e)})}),(0,Z.jsx)(si,{label:p("\u5f00\u542f\u65f6\u7684\u540e\u7eed\u4fee\u6539\u4f1a\u540c\u6b65\u5230\u5f53\u524d\u66f2\u7ebf\u6240\u6709\u7ed3\u679c"),colon:!1})]}),(0,Z.jsxs)("div",{className:"chunk",children:[(0,Z.jsxs)(un.A,{children:[(0,Z.jsx)(xn.A,{span:6,children:(0,Z.jsx)(si,{name:"isAbbr",valuePropName:"checked",children:(0,Z.jsx)(pn.A,{children:p("\u7f29\u5199")})})}),(0,Z.jsx)(xn.A,{span:6,children:(0,Z.jsx)(si,{name:"isName",valuePropName:"checked",children:(0,Z.jsx)(pn.A,{children:p("\u540d\u79f0")})})}),(0,Z.jsx)(xn.A,{span:6,children:(0,Z.jsx)(si,{name:"isVal",valuePropName:"checked",children:(0,Z.jsx)(pn.A,{children:p("\u6570\u503c")})})}),(0,Z.jsx)(xn.A,{span:6,children:(0,Z.jsx)(si,{name:"isChunk",valuePropName:"checked",children:(0,Z.jsx)(pn.A,{children:p("\u6846\u67b6")})})})]}),(0,Z.jsxs)(un.A,{children:[(0,Z.jsx)(xn.A,{span:6,children:(0,Z.jsx)(si,{name:"isLine",valuePropName:"checked",children:(0,Z.jsx)(pn.A,{children:p("\u8fde\u63a5\u7ebf")})})}),(0,Z.jsx)(xn.A,{span:6,children:(0,Z.jsx)(si,{name:"color",label:p("\u989c\u8272"),children:(0,Z.jsx)(Tn.A,{})})}),(0,Z.jsx)(xn.A,{span:6,children:(0,Z.jsx)(si,{name:"isSample",valuePropName:"checked",hidden:!u||x===ee.xO.\u5355\u6570\u636e\u6e90,children:(0,Z.jsx)(pn.A,{children:p("\u4ec5\u6807\u6ce8\u5f53\u524d\u8bd5\u6837")})})})]})]})]})]})})]}),(0,Z.jsxs)("div",{className:"footer-btns",children:[(0,Z.jsx)(rn.A,{onClick:async()=>{o(A.current),r(!1)},type:"primary",children:p("\u786e\u8ba4")}),(0,Z.jsx)(rn.A,{onClick:$,children:p("\u53d6\u6d88")})]})]})})},ai=e=>{let{value:n,onChange:i,channels:l,sourceType:r}=e;const{t:s}=(0,tn.Bd)(),[o,a]=(0,t.useState)(!1),[d,c]=(0,t.useState)("yAxis"),{getSamples:u}=(0,$e.A)(),x=(0,t.useMemo)((()=>{if(r===ee.xO.\u5355\u6570\u636e\u6e90){var e,i;const t="optSample",r=(null===(e=n[d].curves)||void 0===e||null===(i=e[t])||void 0===i?void 0:i.lines)||[];return[{title:"\u6240\u9009\u8bd5\u6837",key:t,disabled:!0,children:r.map(((e,n)=>{var i,r;return{dataSourceKey:t,index:n,title:null!==(i=null===(r=l.find((n=>n.code===e.code)))||void 0===r?void 0:r.name)&&void 0!==i?i:e.code,key:`${t}-${n}`,code:e.code}}))}]}return u().map((e=>{var i,t;const r=e.code,s=(null===(i=n[d].curves)||void 0===i||null===(t=i[r])||void 0===t?void 0:t.lines)||[];return{title:e.name,key:r,disabled:!0,children:s.map(((e,n)=>{var i,t;return{dataSourceKey:r,index:n,title:null!==(i=null===(t=l.find((n=>n.code===e.code)))||void 0===t?void 0:t.name)&&void 0!==i?i:e.code,key:`${r}-${n}`,code:e.code}}))}}))}),[n,l,u,r,d]);return(0,Z.jsxs)(Z.Fragment,{children:[(0,Z.jsx)(rn.A,{onClick:()=>a(!0),children:s("\u8bbe\u7f6e")}),o&&(0,Z.jsx)(oi,{isBufferCurve:!0,treeData:x,open:o,setOpen:a,value:n,onChange:i,axis:d,setAxis:c,sourceType:r})]})},di=e=>{let{value:n,onChange:i,channels:l,sourceType:r}=e;const{t:s}=(0,tn.Bd)(),[o,a]=(0,t.useState)(!1),[d,c]=(0,t.useState)("yAxis"),u=(0,t.useMemo)((()=>{var e;const i=null===n||void 0===n||null===(e=n[d])||void 0===e?void 0:e.curves;return i&&"object"===typeof i?Object.keys(i).map((e=>{const n=i[e],t=(null===n||void 0===n?void 0:n.lines)||[];return{title:`\u4e8c\u7ef4\u6570\u7ec4[${e}]`,key:`${e}`,disabled:!0,children:t.map(((n,i)=>{var t,r;return{dataSourceKey:e,index:i,title:null!==(t=null===(r=l.find((e=>e.code===n.code)))||void 0===r?void 0:r.name)&&void 0!==t?t:n.code,key:`${e}-${i}`,code:n.code}}))}})):[]}),[n,l,d]);return(0,Z.jsxs)(Z.Fragment,{children:[(0,Z.jsx)(rn.A,{onClick:()=>a(!0),children:s("\u8bbe\u7f6e")}),o&&(0,Z.jsx)(oi,{treeData:u,channels:l,open:o,setOpen:a,value:n,onChange:i,axis:d,setAxis:c,sourceType:r})]})},ci=e=>{let{isBufferCurve:n,channels:i,value:t,onChange:l}=e;const r={channels:i,value:t,onChange:l,sourceType:nn.A.useWatch(["base","sourceType"])};return n?(0,Z.jsx)(ai,{...r}):(0,Z.jsx)(di,{...r})};var ui=i(95206),xi=i(54522),pi=i(75337),vi=i(61966);const hi=a.Ay.div`
    padding: 20px;
    
    .block-list-container {
        border: 1px solid #d9d9d9;
        border-radius: 6px;
        height: 500px;
        
        .block-list-header {
            padding: 12px 16px;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background-color: #fafafa;
            
            span {
                font-weight: 500;
                color: #262626;
            }
        }
        
        .block-list {
            height: calc(100% - 49px);
            overflow-y: auto;
            
            .block-item {
                cursor: pointer;
                transition: background-color 0.2s;
                border-bottom: 1px solid #f0f0f0;
                
                &:hover {
                    background-color: #f5f5f5;
                }
                
                &.selected {
                    background-color: #e6f7ff;
                    border-color: #91d5ff;
                }
                
                .block-item-content {
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    
                    .block-color {
                        width: 16px;
                        height: 16px;
                        border-radius: 2px;
                        border: 1px solid #d9d9d9;
                        flex-shrink: 0;
                    }
                    
                    .block-name {
                        font-weight: 500;
                        color: #262626;
                        flex: 1;
                    }
                }
            }
        }
    }
    
    .block-config-container {
        padding-left: 16px;
        
        .transfer-container {
            margin-bottom: 24px;
            
            .ant-transfer {
                .ant-transfer-list {
                    border-radius: 6px;
                }
            }
        }
        
        .block-config-form {
            .checkbox-group {
                margin-top: 16px;
                padding: 16px;
                background-color: #fafafa;
                border-radius: 6px;
                
                .ant-checkbox-wrapper {
                    margin-bottom: 8px;
                }
            }
        }
        
        .no-selection {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 300px;
            color: #8c8c8c;
            font-size: 14px;
            background-color: #fafafa;
            border-radius: 6px;
            border: 1px dashed #d9d9d9;
        }
    }
    
    .footer-btns {
        display: flex;
        justify-content: flex-end;
        gap: 8px;
        margin-top: 24px;
        padding-top: 16px;
        border-top: 1px solid #f0f0f0;
    }

    /* 表单样式调整 */
    .ant-form-item {
        margin-bottom: 16px;
    }

    .ant-form-item-label {
        padding-bottom: 4px;
        
        label {
            font-size: 13px;
            color: #333;
        }
    }

    /* 穿梭框样式调整 */
    .ant-transfer {
        .ant-transfer-list {
            border-radius: 4px;
        }
    }

    /* 颜色选择器样式 */
    .ant-form-item-control-input {
        .color-selector {
            width: 100%;
        }
    }
`,fi={id:"",color:"#000000",isName:!0,name:"",isChunk:!0,isLine:!0,isSample:!0,isVal:!0,isAbbr:!0,curveIndex:0,results:[]},{Item:gi}=nn.A,mi=e=>{let{open:n,setOpen:i,value:r=[],onChange:s}=e;const{t:o}=(0,tn.Bd)(),a=(0,l.d4)((e=>e.template.resultData)),d=(0,l.d4)((e=>e.template.resultTestData)),{getCurveResults:c}=(0,ii.A)(),[u]=nn.A.useForm(),[x,p]=(0,t.useState)(!1),v=(0,t.useRef)(),[h,f]=(0,t.useState)([]),[g,m]=(0,t.useState)(null),[y,A]=(0,t.useState)(null),j=(0,t.useMemo)((()=>c()),[a,d]);(0,t.useEffect)((()=>{n&&(v.current=Cn()(r),f(Cn()(r)),m(null),A(null),u.resetFields())}),[n,r]);const b=()=>{i(!1)};return(0,Z.jsx)(ln.A,{open:n,onCancel:b,width:1200,title:o("\u5757\u6807\u6ce8\u8bbe\u7f6e"),destroyOnClose:!0,footer:null,children:(0,Z.jsxs)(hi,{children:[(0,Z.jsxs)(un.A,{gutter:16,children:[(0,Z.jsx)(xn.A,{span:8,children:(0,Z.jsxs)("div",{className:"block-list-container",children:[(0,Z.jsxs)("div",{className:"block-list-header",children:[(0,Z.jsx)("span",{children:o("\u5757\u5217\u8868")}),(0,Z.jsx)(ui.Ay,{icon:(0,Z.jsx)(pi.A,{}),size:"small",onClick:()=>{const e={...fi,id:(0,ni.A)(),showTitle:!0,title:`\u5757${h.length+1}`},n=[...h,e];f(n),v.current=n,m(n.length-1),A(e),u.setFieldsValue(e)},children:o("\u65b0\u589e")})]}),(0,Z.jsx)(xi.A,{className:"block-list",dataSource:h,renderItem:(e,n)=>(0,Z.jsx)(xi.A.Item,{className:"block-item "+(g===n?"selected":""),onClick:()=>(e=>{m(e);const n=h[e];A(n),u.setFieldsValue(n)})(n),actions:[(0,Z.jsx)(ui.Ay,{type:"text",danger:!0,icon:(0,Z.jsx)(vi.A,{}),size:"small",onClick:e=>{e.stopPropagation(),(e=>{const n=h.filter(((n,i)=>i!==e));f(n),v.current=n,g===e?(m(null),A(null),u.resetFields()):g>e&&m(g-1)})(n)}})],children:(0,Z.jsxs)("div",{className:"block-item-content",children:[(0,Z.jsx)("div",{className:"block-color",style:{backgroundColor:e.color}}),(0,Z.jsx)("span",{className:"block-name",children:e.title})]})})})]})}),(0,Z.jsx)(xn.A,{span:16,children:(0,Z.jsx)("div",{className:"block-config-container",children:y?(0,Z.jsx)(Z.Fragment,{children:(0,Z.jsxs)(nn.A,{form:u,onValuesChange:(e,n)=>{if(null!==g){const i=[...h];if(i[g]={...i[g],...n},x&&Object.keys(e).length>0){const t=Object.keys(e).filter((e=>!["id","name","results","title"].includes(e)));if(t.length>0){const e={};t.forEach((i=>{e[i]=n[i]}));for(let n=0;n<i.length;n+=1)n!==g&&(i[n]={...i[n],...e})}}f(i),A(i[g]),v.current=i}},className:"block-config-form",children:[(0,Z.jsxs)(un.A,{children:[(0,Z.jsx)(xn.A,{span:6,children:(0,Z.jsx)(gi,{name:["showTitle"],label:o("\u5757\u540d\u79f0"),valuePropName:"checked",labelCol:{span:12},children:(0,Z.jsx)(pn.A,{})})}),(0,Z.jsx)(xn.A,{span:6,pull:2,children:(0,Z.jsx)(gi,{name:"title",children:(0,Z.jsx)(vn.A,{placeholder:o("\u8bf7\u8f93\u5165\u5757\u540d\u79f0")})})}),(0,Z.jsx)(xn.A,{span:12,children:(0,Z.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"3px"},children:[(0,Z.jsx)(gi,{children:(0,Z.jsx)(bn.A,{value:x,onChange:e=>p(e)})}),(0,Z.jsx)(gi,{label:o("\u5f00\u542f\u65f6\u7684\u540e\u7eed\u4fee\u6539\u4f1a\u540c\u6b65\u5230\u6240\u6709\u5757\u6807\u6ce8"),colon:!1})]})})]}),(0,Z.jsx)("div",{className:"checkbox-group",children:(0,Z.jsxs)(un.A,{gutter:16,children:[(0,Z.jsx)(xn.A,{span:6,children:(0,Z.jsx)(gi,{name:"isAbbr",valuePropName:"checked",children:(0,Z.jsx)(pn.A,{children:o("\u7f29\u5199")})})}),(0,Z.jsx)(xn.A,{span:6,children:(0,Z.jsx)(gi,{name:"isName",valuePropName:"checked",children:(0,Z.jsx)(pn.A,{children:o("\u540d\u79f0")})})}),(0,Z.jsx)(xn.A,{span:6,children:(0,Z.jsx)(gi,{name:"isVal",valuePropName:"checked",children:(0,Z.jsx)(pn.A,{children:o("\u6570\u503c")})})}),(0,Z.jsx)(xn.A,{span:4,children:(0,Z.jsx)(gi,{name:"color",label:o("\u989c\u8272"),children:(0,Z.jsx)(Tn.A,{})})})]})}),(0,Z.jsx)("div",{className:"transfer-container",children:(0,Z.jsx)(zn.A,{listStyle:{width:"20vw",height:"30vh"},targetKeys:y.results||[],onChange:e=>{if(null!==g){const n=[...h];n[g]={...n[g],results:e},f(n),A(n[g]),v.current=n,u.setFieldsValue({results:e})}},dataSource:j,onChangeWay:e=>{console.log("Selected result:",e)},onChangeDelWay:e=>{if(null!==g&&null!==e&&void 0!==e&&e.result_variable_id){const n=[...h],i=(n[g].results||[]).filter((n=>n!==e.result_variable_id));n[g]={...n[g],results:i},f(n),A(n[g]),v.current=n,u.setFieldsValue({results:i})}},oneWay:!0,oneWayLabel:"variable_name",rowKey:"result_variable_id",render:e=>e.variable_name})})]})}):(0,Z.jsx)("div",{className:"no-selection",children:(0,Z.jsx)("p",{children:o("\u8bf7\u9009\u62e9\u4e00\u4e2a\u5757\u8fdb\u884c\u914d\u7f6e")})})})})]}),(0,Z.jsxs)("div",{className:"footer-btns",children:[(0,Z.jsx)(rn.A,{onClick:()=>{s(v.current),i(!1)},type:"primary",children:o("\u786e\u8ba4")}),(0,Z.jsx)(rn.A,{onClick:b,children:o("\u53d6\u6d88")})]})]})})},yi=e=>{let{value:n,onChange:i}=e;const{t:l}=(0,tn.Bd)(),[r,s]=(0,t.useState)(!1);return(0,Z.jsxs)(Z.Fragment,{children:[(0,Z.jsx)(rn.A,{onClick:()=>s(!0),children:l("\u8bbe\u7f6e")}),r&&(0,Z.jsx)(mi,{open:r,setOpen:s,value:n,onChange:i})]})},{Item:Ai}=nn.A,ji=e=>{let{channels:n,isBufferCurve:i}=e;const{t:t}=(0,tn.Bd)();return(0,Z.jsx)(Z.Fragment,{children:(0,Z.jsxs)("div",{className:"step-tag",children:[(0,Z.jsx)(un.A,{children:(0,Z.jsx)(xn.A,{span:8,children:(0,Z.jsx)(Ai,{name:["legend","open"],label:t("\u663e\u793a\u56fe\u4f8b"),valuePropName:"checked",children:(0,Z.jsx)(pn.A,{})})})}),(0,Z.jsxs)(un.A,{children:[(0,Z.jsx)(xn.A,{span:8,children:(0,Z.jsx)(Ai,{name:["pointTag","open"],label:t("\u6807\u6ce8\u7ed3\u679c"),valuePropName:"checked",children:(0,Z.jsx)(pn.A,{})})}),(0,Z.jsx)(xn.A,{span:8,children:(0,Z.jsx)(Ai,{noStyle:!0,name:["curveGroup"],children:(0,Z.jsx)(ci,{isBufferCurve:i,channels:n})})})]}),(0,Z.jsxs)(un.A,{children:[(0,Z.jsx)(xn.A,{span:8,children:(0,Z.jsx)(Ai,{name:["chunkTag","open"],label:t("\u663e\u793a\u6807\u7b7e\u5757"),valuePropName:"checked",children:(0,Z.jsx)(pn.A,{})})}),(0,Z.jsx)(xn.A,{span:8,children:(0,Z.jsx)(Ai,{noStyle:!0,name:["chunkTag","list"],children:(0,Z.jsx)(yi,{})})})]})]})})};var bi=i(8829);const ki=e=>{let{isBufferCurve:n,channels:i,chartCurveGroup:t,xAxisName:l,yAxisName:r,sourceType:s}=e;const o={},a=(null===t||void 0===t?void 0:t.curves)||{},d=(null===t||void 0===t?void 0:t.ySignal)||[];return Object.keys(a).forEach((e=>{const c=a[e];let u;if(n){const n=(0,Le.w)().find((n=>n.code===e));u=null===n||void 0===n?void 0:n.color}o[e]={name:c.name,lines:d.map(((o,a)=>{var d,x,p,v,h;const f=`${t.name}-${e}-${o}`,g=null===(d=i.find((e=>e.code===t.xSignal)))||void 0===d?void 0:d.name,m=null===(x=i.find((e=>e.code===o)))||void 0===x?void 0:x.name;let y;var A;n?y=s===Ye?`${m}-${g}`:`${null!==(A=null===c||void 0===c?void 0:c.name)&&void 0!==A?A:""}-${m}-${g}`:y=`${r}-${l}`;return{...Ze,color:(0,ye.bE)(),...u?{color:u}:{},...null!==(p=null===c||void 0===c||null===(v=c.lines)||void 0===v?void 0:v.find((e=>(null===e||void 0===e?void 0:e.id)===f)))&&void 0!==p?p:{},yUnit:null===(h=i.find((e=>e.code===o)))||void 0===h?void 0:h.unitId,code:o,xName:g,yName:m,id:f,name:y}}))}})),o},Ci=(e,n,i,t,l)=>{var r,s,a,d,c,u,x,p,v,h,f,g,m,y,A,j,b,k,C,S,T,w,I,L,M,N,$,E,R,O;const P=(0,o.cloneDeep)(n);if(null!==e&&void 0!==e&&null!==(r=e.curveGroup)&&void 0!==r&&null!==(s=r.yAxis)&&void 0!==s&&s.xSignal){var _,V;const n=null===(_=e.curveGroup)||void 0===_?void 0:_.yAxis.xSignal;P.curveGroup.y2Axis.xSignal=n,P.xAxis.unit=null===(V=t.find((e=>e.code===n)))||void 0===V?void 0:V.unitId}if(null!==e&&void 0!==e&&null!==(a=e.curveGroup)&&void 0!==a&&null!==(d=a.y2Axis)&&void 0!==d&&d.xSignal){var B,D;const n=null===(B=e.curveGroup)||void 0===B?void 0:B.y2Axis.xSignal;P.curveGroup.yAxis.xSignal=n,P.xAxis.unit=null===(D=t.find((e=>e.code===n)))||void 0===D?void 0:D.unitId}if(null!==e&&void 0!==e&&null!==(c=e.curveGroup)&&void 0!==c&&null!==(u=c.yAxis)&&void 0!==u&&u.xSignal||null!==e&&void 0!==e&&null!==(x=e.curveGroup)&&void 0!==x&&null!==(p=x.y2Axis)&&void 0!==p&&p.xSignal){var G,F,X,Y;const n=(null===e||void 0===e||null===(G=e.curveGroup)||void 0===G||null===(F=G.yAxis)||void 0===F?void 0:F.xSignal)||(null===e||void 0===e||null===(X=e.curveGroup)||void 0===X||null===(Y=X.y2Axis)||void 0===Y?void 0:Y.xSignal);P.curveGroup.yAxis.xSignal=n,P.curveGroup.y2Axis.xSignal=n}var U,z;null!==e&&void 0!==e&&null!==(v=e.xAxis)&&void 0!==v&&v.unit&&(P.curveGroup.yAxis.xUnit=null===e||void 0===e||null===(U=e.xAxis)||void 0===U?void 0:U.unit,P.curveGroup.y2Axis.xUnit=null===e||void 0===e||null===(z=e.xAxis)||void 0===z?void 0:z.unit);if(null!==e&&void 0!==e&&null!==(h=e.curveGroup)&&void 0!==h&&null!==(f=h.yAxis)&&void 0!==f&&f.xSignal||null!==e&&void 0!==e&&null!==(g=e.curveGroup)&&void 0!==g&&null!==(m=g.y2Axis)&&void 0!==m&&m.xSignal||null!==e&&void 0!==e&&null!==(y=e.xAxis)&&void 0!==y&&y.unit){var H,W,K,Z,q,Q,J,ee,ne,ie;const e=(null===P||void 0===P||null===(H=P.curveGroup)||void 0===H||null===(W=H.yAxis)||void 0===W?void 0:W.xSignal)||(null===P||void 0===P||null===(K=P.curveGroup)||void 0===K||null===(Z=K.y2Axis)||void 0===Z?void 0:Z.xSignal),n=null!==(q=null===(Q=t.find((n=>n.code===e)))||void 0===Q?void 0:Q.name)&&void 0!==q?q:"",i=null!==(J=null===t||void 0===t||null===(ee=t.find((n=>n.code===e)))||void 0===ee||null===(ne=ee.unitList)||void 0===ne||null===(ie=ne.find((e=>{var n;return e.id===(null===P||void 0===P||null===(n=P.xAxis)||void 0===n?void 0:n.unit)})))||void 0===ie?void 0:ie.name)&&void 0!==J?J:"";P.xAxis.name=i?`${n}(${i})`:`${n}`}if(null!==e&&void 0!==e&&null!==(A=e.curveGroup)&&void 0!==A&&null!==(j=A.yAxis)&&void 0!==j&&j.ySignal||null!==e&&void 0!==e&&null!==(b=e.curveGroup)&&void 0!==b&&null!==(k=b.yAxis)&&void 0!==k&&k.xSignal){var te,le,re,se;const e=ki({isBufferCurve:l,channels:t,xAxisName:null===P||void 0===P||null===(te=P.xAxis)||void 0===te?void 0:te.name,yAxisName:null===P||void 0===P||null===(le=P.yAxis)||void 0===le?void 0:le.name,chartCurveGroup:null===(re=P.curveGroup)||void 0===re?void 0:re.yAxis,sourceType:null===P||void 0===P||null===(se=P.base)||void 0===se?void 0:se.sourceType});P.curveGroup.yAxis.curves=e}if(null!==e&&void 0!==e&&null!==(C=e.curveGroup)&&void 0!==C&&null!==(S=C.y2Axis)&&void 0!==S&&S.ySignal||null!==e&&void 0!==e&&null!==(T=e.curveGroup)&&void 0!==T&&null!==(w=T.y2Axis)&&void 0!==w&&w.xSignal){var oe,ae,de;const e=ki({isBufferCurve:l,channels:t,xAxisName:null===P||void 0===P||null===(oe=P.xAxis)||void 0===oe?void 0:oe.name,yAxisName:null===P||void 0===P||null===(ae=P.y2Axis)||void 0===ae?void 0:ae.name,chartCurveGroup:null===(de=P.curveGroup)||void 0===de?void 0:de.y2Axis});P.curveGroup.y2Axis.curves=e}if(null!==e&&void 0!==e&&null!==(I=e.curveGroup)&&void 0!==I&&null!==(L=I.yAxis)&&void 0!==L&&L.ySignal||null!==e&&void 0!==e&&null!==(M=e.curveGroup)&&void 0!==M&&null!==(N=M.yAxis)&&void 0!==N&&N.curves){var ce,ue,xe;const e=null===(ce=Object.values(P.curveGroup.yAxis.curves))||void 0===ce?void 0:ce[0].lines,n=(e,n)=>`${e}(${n})`,i=null===P||void 0===P||null===(ue=P.curveGroup)||void 0===ue||null===(xe=ue.yAxis)||void 0===xe?void 0:xe.ySignal.map((i=>{var l;const r=e.find((e=>e.code===i)),s=(0,Qe.c)(null===r||void 0===r?void 0:r.yUnit),o=null===(l=t.find((e=>e.code===i)))||void 0===l?void 0:l.name;return s?n(o,null===s||void 0===s?void 0:s.name):o})).join("/");P.yAxis.name=i}if(null!==e&&void 0!==e&&null!==($=e.curveGroup)&&void 0!==$&&null!==(E=$.y2Axis)&&void 0!==E&&E.ySignal||null!==e&&void 0!==e&&null!==(R=e.curveGroup)&&void 0!==R&&null!==(O=R.y2Axis)&&void 0!==O&&O.curves){var pe,ve,he;const e=null===(pe=Object.values(P.curveGroup.y2Axis.curves))||void 0===pe?void 0:pe[0].lines,n=(e,n)=>`${e}(${n})`,i=null===P||void 0===P||null===(ve=P.curveGroup)||void 0===ve||null===(he=ve.y2Axis)||void 0===he?void 0:he.ySignal.map((i=>{var l;const r=e.find((e=>e.code===i)),s=(0,Qe.c)(null===r||void 0===r?void 0:r.yUnit),o=null===(l=t.find((e=>e.code===i)))||void 0===l?void 0:l.name;return s?n(o,null===s||void 0===s?void 0:s.name):o})).join("/");P.y2Axis.name=i}i.setFieldsValue(P)},{Item:Si,useWatch:Ti,useFormInstance:wi}=nn.A,Ii=e=>{let{channels:n=[],value:i=[],onChange:t,sourceType:r,isBufferCurve:s,yAxisCurveGroup:o}=e;const{t:a}=(0,tn.Bd)(),d=(0,l.d4)((e=>e.global.unitList)),c=e=>{if(!e||!n||!d)return[];const i=n.find((n=>n.code===e));if(!i||!i.dimensionId)return[];const t=d.find((e=>e.id===i.dimensionId));return(null===t||void 0===t?void 0:t.units)||[]},u=(e,i)=>{if(!e)return"";const t=n.find((n=>n.code===e)),l=(null===t||void 0===t?void 0:t.name)||"";if(!i)return l;const r=c(e).find((e=>e.id===i)),s=(null===r||void 0===r?void 0:r.name)||"";return s?`${l}(${s})`:l},x=(e,l,r,a)=>{const d=[...i];if(d[e]={...d[e],[l]:r},"xSignal"===l&&(d[e].xUnit=null===a||void 0===a?void 0:a.unitId),"xSignal"===l||"xUnit"===l){const n="xSignal"===l?r:d[e].xSignal,i="xUnit"===l?r:d[e].xUnit;d[e].xName=u(n,i)}if("ySignal"===l){const i="ySignal"===l?r:d[e].ySignal;d[e].yName=(e=>{if(!e||0===e.length)return"";if(Array.isArray(e)&&e.length>1)return e.map((e=>{const i=n.find((n=>n.code===e));return(null===i||void 0===i?void 0:i.name)||""})).filter(Boolean).join("/");const i=Array.isArray(e)?e[0]:e;return u(i)})(i)}if(["ySignal","xSignal","xName","yName","xUnit","yUnit"].includes(l)){const i=d[e],t=ki({isBufferCurve:s,channels:n,xAxisName:i.xName,yAxisName:i.yName,chartCurveGroup:{...o,curves:i.curves||o.curves,xSignal:i.xSignal,xUnit:i.xUnit,ySignal:i.ySignal}});d[e].curves=t}t(d)},p=[{title:a("\u9009\u9879"),dataIndex:"label",width:120,render:e=>e},{title:a("X\u8f74\u901a\u9053"),dataIndex:"xSignal",width:150,render:(e,i,t)=>(0,Z.jsx)(hn.A,{options:n,fieldNames:{label:"name",value:"code"},value:e,style:{width:"100%"},onChange:(e,n)=>x(t,"xSignal",e,n),placeholder:a("\u8bf7\u9009\u62e9X\u8f74\u901a\u9053")})},{title:a("X\u8f74\u5355\u4f4d"),dataIndex:"xUnit",width:120,render:(e,n,i)=>{const t=c(n.xSignal);return(0,Z.jsx)(hn.A,{options:t,fieldNames:{label:"name",value:"id"},value:e,style:{width:"100%"},onChange:e=>x(i,"xUnit",e),placeholder:a("\u8bf7\u9009\u62e9X\u8f74\u5355\u4f4d"),allowClear:!0})}},{title:a("X\u8f74\u540d\u79f0"),dataIndex:"xName",width:120,render:(e,n,i)=>(0,Z.jsx)(vn.A,{value:e,onChange:e=>x(i,"xName",e.target.value),placeholder:a("\u8bf7\u8f93\u5165X\u8f74\u540d\u79f0")})},{title:a("Y1\u8f74\u901a\u9053"),dataIndex:"ySignal",width:300,render:(e,i,t)=>(0,Z.jsx)(hn.A,{mode:"multiple",options:n,fieldNames:{label:"name",value:"code"},value:Array.isArray(e)?e:e?[e]:[],style:{width:"100%"},onChange:(e,n)=>{x(t,"ySignal",e,n)},placeholder:a("\u8bf7\u9009\u62e9Y\u8f74\u901a\u9053"),maxTagCount:"responsive",maxCount:r===ee.xO.\u591a\u6570\u636e\u6e90?1:void 0})},{title:a("Y1\u8f74\u540d\u79f0"),dataIndex:"yName",width:150,render:(e,n,i)=>(0,Z.jsx)(vn.A,{value:e,onChange:e=>x(i,"yName",e.target.value),placeholder:a("\u8bf7\u8f93\u5165Y\u8f74\u540d\u79f0")})},{title:a("\u66f2\u7ebf"),dataIndex:"curves",width:120,render:(e,i,t)=>(0,Z.jsx)($n,{value:e,onChange:e=>{x(t,"curves",e)},channels:n,isBufferCurve:s})}];return(0,Z.jsx)("div",{className:"table-layout",style:{width:"100%"},children:(0,Z.jsx)(qn.A,{rowKey:"id",dataSource:i,bordered:!0,columns:p,scroll:{y:400,x:"max-content"},pagination:!1,style:{width:"100%"}})})},Li={"\u66f2\u7ebf\u56fe":An,"\u66f2\u7ebf\u7ec4":On,"X-\u8f74":Bn,"Y-\u8f741":Fn,"Y-\u8f742":Un,"\u8f85\u52a9\u7ebf":Kn,"\u6807\u7b7e":ji,"\u6807\u8bb0\u70b9\u8bbe\u7f6e":ei,"\u5b9a\u4e49\u5750\u6807\u6e90":e=>{let{channels:n,isBufferCurve:i}=e;const{t:l}=(0,tn.Bd)(),r=(0,bi.A)(),s=wi(),o=nn.A.useWatch(["base"],s),a=nn.A.useWatch(["curveGroup","yAxis"],s),d=(0,t.useMemo)((()=>r.filter((e=>e.select_tab.selection===pe.P$.\u5217\u8868\u4e2d\u7684\u5355\u9009\u9879.value))),[]);return(0,Z.jsxs)("div",{className:"step-define-axis",children:[(0,Z.jsx)(Si,{name:["defineAxis","isDefineAxis"],valuePropName:"checked",children:(0,Z.jsx)(pn.A,{children:l("\u542f\u52a8\u66f2\u7ebf\u9009\u9879(\u52fe\u9009\u540e\u5355\u72ec\u8bbe\u7f6e\u7684XY\u8f74\u663e\u793a\u5c06\u5931\u6548)")})}),(0,Z.jsx)(un.A,{children:(0,Z.jsx)(xn.A,{span:24,children:(0,Z.jsx)(Si,{name:["defineAxis","inputCode"],label:l("\u66f2\u7ebf\u53d8\u91cf\u6e90"),labelCol:{span:3},wrapperCol:{span:8},children:(0,Z.jsx)(hn.A,{options:d,fieldNames:{label:"name",value:"code"},onChange:(e,n)=>{var i,t,l;const r=null!==(i=s.getFieldValue(["defineAxis","source"]))&&void 0!==i?i:[],o=null===n||void 0===n||null===(t=n.select_tab)||void 0===t||null===(l=t.items)||void 0===l?void 0:l.map(((e,n)=>({id:e.id||Date.now()+n,label:e.label,value:e.value,ySignal:[],yName:"",xSignal:"",xUnit:"",xName:"",...r.find((n=>n.value===e.value))})));s.setFieldValue(["defineAxis","source"],o)}})})})}),(0,Z.jsx)(un.A,{style:{marginTop:16},children:(0,Z.jsx)(xn.A,{span:24,children:(0,Z.jsx)(Si,{name:["defineAxis","source"],noStyle:!0,children:(0,Z.jsx)(Ii,{channels:n,isBufferCurve:i,sourceType:null===o||void 0===o?void 0:o.sourceType,yAxisCurveGroup:a})})})})]})}},Mi=e=>{let{channels:n,currentStep:i,isBufferCurve:t}=e;return Xe.map((e=>{const l=Li[e];return(0,Z.jsx)("div",{style:{display:i===e?"block":"none"},children:(0,Z.jsx)(l,{channels:n,isBufferCurve:t})})}))},Ni=e=>{let{channels:n,isBufferCurve:i,currentStep:l,onStepChange:r}=e;const{t:s}=(0,tn.Bd)(),[o,a]=(0,t.useState)("\u66f2\u7ebf\u56fe"),[d,c]=(0,t.useState)(["\u66f2\u7ebf\u56fe"]),u=(0,t.useRef)("\u66f2\u7ebf\u56fe"),x=void 0!==l?Xe[l]||"\u66f2\u7ebf\u56fe":o;(0,t.useEffect)((()=>{void 0!==l&&x&&c((e=>[...new Set([...e,x])]))}),[l,x]);return(0,Z.jsxs)("div",{className:"container",children:[(0,Z.jsx)(cn,{currentStep:x,visitedSteps:d,steps:Xe,handleClickStep:async e=>{if(void 0!==l&&r){const n=Xe.indexOf(e);-1!==n&&(r(n),c([...new Set([...d,e])]))}else a(e),u.current=e,c([...new Set([...d,e])])}}),(0,Z.jsx)("div",{className:"form",children:(0,Z.jsx)(Mi,{currentStep:x,channels:n,isBufferCurve:i})})]})},$i=`1px solid ${H.o$.borderGray}`,Ei=(0,H.D0)("20px"),Ri=a.Ay.div`
    padding: ${(0,H.D0)("20px")} ${(0,H.D0)("40px")};

    .container-box{
        border: ${$i};
        padding: ${(0,H.D0)("10px")};
        margin-top: ${Ei};
        .title {
            font-size: 13px;
            font-weight: bold;
            margin-bottom: ${(0,H.D0)("15px")};
        }
        .ant-form-item{
            margin-bottom: ${(0,H.D0)("15px")};
        }

    }
    
    .footer-btns {
        display: flex;
        justify-content: center;
        margin-top: ${(0,H.D0)("40px")};
        button {
            margin-right: ${(0,H.D0)("10px")};
        }
    }
`,{Item:Oi}=nn.A,Pi=e=>{let{channels:n,isBufferCurve:i}=e;const{t:r}=(0,tn.Bd)(),s=(0,l.d4)((e=>e.global.unitList)),o=nn.A.useFormInstance(),a=nn.A.useWatch(["base","sourceType"],o),d=nn.A.useWatch(["curveGroup","yAxis","xSignal"],o),c=nn.A.useWatch(["xAxis","proportionType"],o),u=nn.A.useWatch(["yAxis","proportionType"],o),x=nn.A.useWatch(["y2Axis","proportionType"],o),p=nn.A.useWatch(["curveGroup","y2Axis","isEnable"],o),v=(0,t.useMemo)((()=>{var e,i,t,l;const r=null!==(e=null===n||void 0===n||null===(i=n.find((e=>e.code===d)))||void 0===i?void 0:i.dimensionId)&&void 0!==e?e:{dimensionId:""};return null!==(t=null===(l=s.find((e=>e.id===r)))||void 0===l?void 0:l.units)&&void 0!==t?t:[]}),[n,s,d]),h=[He["\u6570\u636e\u8303\u56f4"],He["\u626b\u63cf\u8303\u56f4"]];return(0,Z.jsxs)(Ri,{children:[(0,Z.jsx)("div",{children:(0,Z.jsx)(Oi,{style:{marginBottom:"0px"},name:["defineAxis","isDefineAxis"],valuePropName:"checked",children:(0,Z.jsx)(pn.A,{children:r("\u542f\u52a8\u53d8\u91cf\u7ed1\u5b9a\u5750\u6807\u6e90")})})}),(0,Z.jsxs)("div",{className:"container-box",children:[(0,Z.jsx)("div",{className:"title",children:r("X\u8f74\u8bbe\u7f6e")}),(0,Z.jsxs)(un.A,{children:[(0,Z.jsx)(xn.A,{span:12,children:(0,Z.jsx)(Oi,{name:["curveGroup","yAxis","xSignal"],label:r("\u901a\u9053"),children:(0,Z.jsx)(hn.A,{options:n,fieldNames:{label:"name",value:"code"}})})}),(0,Z.jsx)(xn.A,{span:12,children:(0,Z.jsx)(Oi,{name:["xAxis","name"],label:r("\u663e\u793a\u540d\u79f0"),children:(0,Z.jsx)(vn.A,{})})})]}),(0,Z.jsxs)(un.A,{children:[(0,Z.jsx)(xn.A,{span:12,children:(0,Z.jsx)(Oi,{name:["xAxis","unit"],label:r("\u5355\u4f4d"),children:(0,Z.jsx)(hn.A,{options:v,fieldNames:{label:"name",value:"id"}})})}),(0,Z.jsx)(xn.A,{span:12,children:(0,Z.jsx)(Oi,{name:["xAxis","proportionType"],label:r("\u81ea\u52a8\u8c03\u6574\u6bd4\u4f8b"),children:(0,Z.jsx)(hn.A,{options:Ke(He)})})})]}),(0,Z.jsxs)(un.A,{children:[(0,Z.jsx)(xn.A,{span:12,children:(0,Z.jsx)(Oi,{name:["xAxis","lowLimit"],label:r("\u4e0b\u9650"),children:(0,Z.jsx)(fn.A,{style:{width:"100%"},disabled:h.includes(c)})})}),(0,Z.jsx)(xn.A,{span:12,children:(0,Z.jsx)(Oi,{name:["xAxis","upLimit"],label:r("\u4e0a\u9650"),children:(0,Z.jsx)(fn.A,{style:{width:"100%"},disabled:h.includes(c)})})})]}),(0,Z.jsxs)(un.A,{children:[(0,Z.jsx)(xn.A,{span:12,children:(0,Z.jsx)(Oi,{name:["xAxis","lastRange"],label:r("\u626b\u63cf\u8303\u56f4"),children:(0,Z.jsx)(fn.A,{style:{width:"100%"},disabled:c!==He["\u626b\u63cf\u8303\u56f4"]})})}),(0,Z.jsx)(xn.A,{span:12,children:(0,Z.jsx)(Oi,{name:["xAxis","isLog"],label:r("\u5bf9\u6570"),valuePropName:"checked",children:(0,Z.jsx)(pn.A,{})})})]})]}),(0,Z.jsxs)("div",{className:"container-box",children:[(0,Z.jsx)("div",{className:"title",children:r("Y1\u8f74\u8bbe\u7f6e")}),(0,Z.jsxs)(un.A,{children:[(0,Z.jsx)(xn.A,{span:12,children:(0,Z.jsx)(Oi,{name:["curveGroup","yAxis","ySignal"],label:r("\u901a\u9053"),children:(0,Z.jsx)(hn.A,{mode:"multiple",options:n,maxCount:a===Ue?1:void 0,fieldNames:{label:"name",value:"code"}})})}),(0,Z.jsx)(xn.A,{span:12,children:(0,Z.jsx)(Oi,{name:["yAxis","name"],label:r("\u663e\u793a\u540d\u79f0"),children:(0,Z.jsx)(vn.A,{})})})]}),(0,Z.jsxs)(un.A,{children:[(0,Z.jsx)(xn.A,{span:12,children:(0,Z.jsx)(Oi,{name:["yAxis","proportionType"],label:r("\u81ea\u52a8\u8c03\u6574\u6bd4\u4f8b"),children:(0,Z.jsx)(hn.A,{options:Ke(He)})})}),(0,Z.jsx)(xn.A,{span:12,children:(0,Z.jsx)(Oi,{name:["yAxis","lowLimit"],label:r("\u4e0b\u9650"),children:(0,Z.jsx)(fn.A,{style:{width:"100%"},disabled:h.includes(u)})})})]}),(0,Z.jsxs)(un.A,{children:[(0,Z.jsx)(xn.A,{span:12,children:(0,Z.jsx)(Oi,{name:["yAxis","upLimit"],label:r("\u4e0a\u9650"),children:(0,Z.jsx)(fn.A,{style:{width:"100%"},disabled:h.includes(u)})})}),(0,Z.jsx)(xn.A,{span:12,children:(0,Z.jsx)(Oi,{name:["yAxis","lastRange"],label:r("\u626b\u63cf\u8303\u56f4"),children:(0,Z.jsx)(fn.A,{style:{width:"100%"},disabled:u!==He["\u626b\u63cf\u8303\u56f4"]})})})]}),(0,Z.jsx)(un.A,{children:(0,Z.jsx)(xn.A,{span:12,children:(0,Z.jsx)(Oi,{name:["yAxis","isLog"],label:r("\u5bf9\u6570"),valuePropName:"checked",children:(0,Z.jsx)(pn.A,{})})})})]}),(0,Z.jsxs)("div",{className:"container-box",children:[(0,Z.jsx)("div",{className:"title",children:r("Y2\u8f74\u8bbe\u7f6e")}),(0,Z.jsxs)(un.A,{children:[(0,Z.jsx)(xn.A,{span:12,children:(0,Z.jsx)(Oi,{name:["curveGroup","y2Axis","ySignal"],label:r("\u901a\u9053"),children:(0,Z.jsx)(hn.A,{mode:"multiple",options:n,maxCount:a===Ue?1:void 0,fieldNames:{label:"name",value:"code"},disabled:!p})})}),(0,Z.jsx)(xn.A,{span:12,children:(0,Z.jsx)(Oi,{name:["y2Axis","name"],label:r("\u663e\u793a\u540d\u79f0"),children:(0,Z.jsx)(vn.A,{disabled:!p})})})]}),(0,Z.jsxs)(un.A,{children:[(0,Z.jsx)(xn.A,{span:12,children:(0,Z.jsx)(Oi,{name:["y2Axis","proportionType"],label:r("\u81ea\u52a8\u8c03\u6574\u6bd4\u4f8b"),children:(0,Z.jsx)(hn.A,{options:Ke(He),disabled:!p})})}),(0,Z.jsx)(xn.A,{span:12,children:(0,Z.jsx)(Oi,{name:["y2Axis","lowLimit"],label:r("\u4e0b\u9650"),children:(0,Z.jsx)(fn.A,{style:{width:"100%"},disabled:h.includes(x)||!p})})})]}),(0,Z.jsxs)(un.A,{children:[(0,Z.jsx)(xn.A,{span:12,children:(0,Z.jsx)(Oi,{name:["y2Axis","upLimit"],label:r("\u4e0a\u9650"),children:(0,Z.jsx)(fn.A,{style:{width:"100%"},disabled:h.includes(x)||!p})})}),(0,Z.jsx)(xn.A,{span:12,children:(0,Z.jsx)(Oi,{name:["y2Axis","lastRange"],label:r("\u626b\u63cf\u8303\u56f4"),children:(0,Z.jsx)(fn.A,{style:{width:"100%"},disabled:x!==He["\u626b\u63cf\u8303\u56f4"]||!p})})})]}),(0,Z.jsx)(un.A,{children:(0,Z.jsx)(xn.A,{span:12,children:(0,Z.jsx)(Oi,{name:["y2Axis","isLog"],label:r("\u5bf9\u6570"),valuePropName:"checked",children:(0,Z.jsx)(pn.A,{disabled:!p})})})})]})]})};var _i=i(68406);const{useForm:Vi,useWatch:Bi}=nn.A,Di=e=>{let{open:n,setOpen:i,config:l,updateConfig:r,isBufferCurve:s}=e;const{t:o}=(0,tn.Bd)(),[a]=Vi(),[d,c]=(0,t.useState)(!1),[u,x]=(0,t.useState)(0);(0,t.useEffect)((()=>{a.setFieldsValue({...l})}),[n]),(0,t.useEffect)((()=>{d||x(0)}),[d]);const p=nn.A.useWatch(["base"],a),v=Je({sourceType:null===p||void 0===p?void 0:p.sourceType,sourceInputCode:null===p||void 0===p?void 0:p.sourceInputCode,isBufferCurve:s});return(0,Z.jsx)(ln.A,{open:n,title:o("\u66f2\u7ebf\u8bbe\u7f6e"),onCancel:()=>i(!1),style:{top:"10px"},width:(0,H.D0)("1400px"),footer:null,children:(0,Z.jsx)(nn.A,{style:{height:"100%"},form:a,initialValues:_i.c,labelAlign:"left",labelCol:{span:10},wrapperCol:{span:12},onValuesChange:(e,n)=>Ci(e,n,a,v,s),children:(0,Z.jsxs)(on,{children:[(0,Z.jsx)("div",{style:{display:d?"none":"block"},children:(0,Z.jsx)(Pi,{channels:v,isBufferCurve:s})}),(0,Z.jsx)("div",{style:{display:d?"block":"none"},children:(0,Z.jsx)(Ni,{channels:v,isBufferCurve:s,currentStep:u,onStepChange:e=>{x(e)}})}),(0,Z.jsxs)("div",{className:"footer-btns",children:[(0,Z.jsx)(rn.A,{onClick:d?()=>{c(!1)}:()=>{c(!0),x(0)},type:d?"default":"primary",children:o(d?"\u57fa\u7840":"\u9ad8\u7ea7")}),d&&(0,Z.jsxs)(Z.Fragment,{children:[(0,Z.jsx)(rn.A,{onClick:()=>{u>0&&x(u-1)},disabled:0===u,children:o("\u8fd4\u56de")}),(0,Z.jsx)(rn.A,{onClick:()=>{u<Xe.length-1&&x(u+1)},disabled:u>=Xe.length-1,children:o("\u524d\u8fdb")})]}),(0,Z.jsx)(rn.A,{onClick:async()=>{const e=await a.validateFields();r(e),i(!1)},type:"primary",children:o("\u786e\u5b9a")}),(0,Z.jsx)(rn.A,{onClick:()=>i(!1),children:o("\u53d6\u6d88")}),(0,Z.jsx)(rn.A,{children:o("\u5e2e\u52a9")})]})]})})})};var Gi=i(63942);const{Option:Fi}=hn.A,Xi=e=>{let{open:n,setOpen:i,onOk:s}=e;const{t:o}=(0,tn.Bd)(),[a,d]=t.useState(null),c=(0,l.d4)((e=>e.template.resultData)),u=(0,t.useMemo)((()=>c.filter((e=>e.marking_flag&&(null===e||void 0===e?void 0:e.marking_count)>0))),[c]),x=()=>{i(!1),d(null)};return(0,Z.jsx)(Gi.A,{title:o("\u9009\u62e9\u7ed3\u679c\u53d8\u91cf"),open:n,onCancel:x,footer:[(0,Z.jsx)(ui.Ay,{onClick:x,children:o("\u53d6\u6d88")},"cancel"),(0,Z.jsx)(ui.Ay,{type:"primary",onClick:()=>{var e;const n=c.find((e=>e.code===a));null!==(e=null===n||void 0===n?void 0:n.marking_flag)&&void 0!==e&&e?(null===n||void 0===n?void 0:n.marking_count)>0?(a&&s&&s(n.marking_count,n.marking_action),i(!1),d(null)):r.Ay.error("\u5f53\u524d\u7ed3\u679c\u53d8\u91cf\uff0c\u6807\u8bb0\u70b9\u4e0d\u80fd\u5c0f\u4e8e0"):r.Ay.error("\u5f53\u524d\u7ed3\u679c\u53d8\u91cf\uff0c\u672a\u5f00\u542f\u624b\u5de5\u6807\u8bb0")},disabled:!a,children:o("\u786e\u5b9a")},"ok")],width:400,children:(0,Z.jsxs)("div",{style:{padding:"20px 0"},children:[(0,Z.jsx)("div",{style:{marginBottom:"10px"},children:o("\u8bf7\u9009\u62e9\u8981\u6807\u8bb0\u7684\u7ed3\u679c\u53d8\u91cf\uff1a")}),(0,Z.jsx)(hn.A,{style:{width:"100%"},placeholder:o("\u8bf7\u9009\u62e9\u7ed3\u679c\u53d8\u91cf"),value:a,onChange:d,showSearch:!0,filterOption:(e,n)=>n.children.toLowerCase().indexOf(e.toLowerCase())>=0,children:u.map((e=>(0,Z.jsx)(Fi,{value:e.code,children:e.variable_name},e.code)))})]})})};var Yi=i(72295),Ui=i(16090);const zi=e=>{const{updateInputVariableValueDB:n}=(0,Yi.A)(),{startAction:i}=(0,Ui.A)(),[l,s]=(0,t.useState)(!1),[o,a]=(0,t.useState)(!1),[d,c]=(0,t.useState)(0),[u,x]=(0,t.useState)(0),[p,v]=(0,t.useState)([]),h=(0,t.useRef)(),f=(0,t.useCallback)((()=>{a(!0)}),[]),g=(0,t.useCallback)((()=>{s(!1),a(!1),c(0),x(0),v([]),r.Ay.info("\u5df2\u505c\u6b62\u624b\u5de5\u6807\u5b9a")}),[]),m=(0,t.useCallback)(((e,n)=>{h.current=n,c(e),x(0),v([]),s(!0),a(!1),r.Ay.info(`\u5f00\u59cb\u624b\u5de5\u6807\u5b9a\uff0c\u9700\u8981\u6807\u5b9a ${e} \u4e2a\u70b9\uff0c\u8bf7\u6309 Enter \u952e\u8fdb\u884c\u6807\u5b9a`)}),[]),y=(0,t.useCallback)((async t=>{var o;if(!l||u>=d)return;if(!t)return void r.Ay.warning("\u65e0\u6cd5\u83b7\u53d6\u5f53\u524d\u5341\u5b57\u7ebf\u4f4d\u7f6e\uff0c\u8bf7\u786e\u4fdd\u5341\u5b57\u7ebf\u5df2\u6fc0\u6d3b");const a=null===e||void 0===e||null===(o=e[u])||void 0===o?void 0:o.inputCode;a?await n({code:a,value:null===t||void 0===t?void 0:t.index}):r.Ay.warning(`\u7b2c ${u+1} \u4e2a\u70b9\u66f2\u7ebf\u6ca1\u6709\u6307\u5b9a\u8f93\u5165\u53d8\u91cf`);const c=u+1,f=[...p,t];x(c),v(f),c>=d?(s(!1),r.Ay.success(`\u624b\u5de5\u6807\u5b9a\u5b8c\u6210\uff01\u5df2\u6807\u5b9a ${d} \u4e2a\u70b9`),console.log("\u6807\u5b9a\u5b8c\u6210\uff0c\u6807\u5b9a\u70b9\uff1a",f),h.current?(await i({action_id:String(h.current)}),r.Ay.success("\u52a8\u4f5c\u6267\u884c\u5b8c\u6210\uff01")):r.Ay.warning("\u672a\u6307\u5b9a\u52a8\u4f5c")):r.Ay.info(`\u5df2\u6807\u5b9a\u7b2c ${c} \u4e2a\u70b9\uff0c\u8fd8\u9700\u6807\u5b9a ${d-c} \u4e2a\u70b9`)}),[l,u,d,p]),A=(0,t.useCallback)((()=>{s(!1),a(!1),c(0),x(0),v([])}),[]);return{isMarking:l,markingModalOpen:o,markingCount:d,currentMarkingStep:u,markingPoints:p,setMarkingModalOpen:a,handleActivateMarking:f,handleStopMarking:g,handleMarkingOk:m,handleMarkingStep:y,resetMarking:A}},Hi=e=>{var n,i,a,c;let{id:u,layoutConfig:x,compName:p,config:v,compStatus:h,updateConfig:f,isBufferCurve:g,isRightClick:m}=e;const y=(0,l.d4)((e=>e.subTask.openExperiment)),A=(0,l.d4)((e=>e.project.optSample)),j=null===v||void 0===v||null===(n=v.defineAxis)||void 0===n?void 0:n.isDefineAxis,b=(0,s.A)(j?null===v||void 0===v||null===(i=v.defineAxis)||void 0===i?void 0:i.inputCode:null),k=(0,t.useMemo)((()=>{if(!v)return null;if(y&&!j)return v;const e=(0,o.cloneDeep)(v);var n,i,t;y||(e.xAxis.proportionType=(null===v||void 0===v||null===(n=v.xAxis)||void 0===n?void 0:n.proportionType)===ee.g3.\u4e0a\u4e0b\u9650\u8303\u56f4?ee.g3.\u4e0a\u4e0b\u9650\u8303\u56f4:ee.g3.\u6570\u636e\u8303\u56f4,e.yAxis.proportionType=(null===v||void 0===v||null===(i=v.yAxis)||void 0===i?void 0:i.proportionType)===ee.g3.\u4e0a\u4e0b\u9650\u8303\u56f4?ee.g3.\u4e0a\u4e0b\u9650\u8303\u56f4:ee.g3.\u6570\u636e\u8303\u56f4,e.y2Axis.proportionType=(null===v||void 0===v||null===(t=v.y2Axis)||void 0===t?void 0:t.proportionType)===ee.g3.\u4e0a\u4e0b\u9650\u8303\u56f4?ee.g3.\u4e0a\u4e0b\u9650\u8303\u56f4:ee.g3.\u6570\u636e\u8303\u56f4);if(j){const n=e.defineAxis.source.find((e=>e.value===b));if(!n)return e;if(null===n||void 0===n||!n.xSignal)return r.Ay.error("\u5b9a\u4e49\u5750\u6807\u6e90 - \u5f53\u524d\u914d\u7f6e\u672a\u627e\u5230x\u8f74\u4fe1\u53f7"),e;if(null===n||void 0===n||!n.ySignal||0===(null===n||void 0===n?void 0:n.ySignal.length))return r.Ay.error("\u5b9a\u4e49\u5750\u6807\u6e90 - \u5f53\u524d\u914d\u7f6e\u672a\u627e\u5230y\u8f74\u4fe1\u53f7"),e;e.xAxis.name=null===n||void 0===n?void 0:n.xName,e.yAxis.name=null===n||void 0===n?void 0:n.yName,e.curveGroup.yAxis.xSignal=n.xSignal,e.curveGroup.yAxis.xUnit=n.xUnit,e.curveGroup.yAxis.ySignal=n.ySignal,null!==n&&void 0!==n&&n.curves?e.curveGroup.yAxis.curves=n.curves:Object.keys(e.curveGroup.yAxis.curves).forEach((i=>{e.curveGroup.yAxis.curves[i].lines=e.curveGroup.yAxis.curves[i].lines.map(((i,t)=>({...i,yUnit:(null===e||void 0===e?void 0:e.base.sourceType)===ee.xO.\u5355\u6570\u636e\u6e90?null:n.yUnit,code:n.ySignal[t]})))})),e.curveGroup.y2Axis.isEnable=!1}return e}),[y,v,j,b]),C=(0,t.useRef)(),[S,T]=(0,t.useState)(!1),[w,I]=(0,t.useState)(!1),[L,M]=(0,t.useState)(!1),N=(null===k||void 0===k||null===(a=k.pointTag)||void 0===a?void 0:a.open)&&!y||!1,$=(null===k||void 0===k||null===(c=k.chunkTag)||void 0===c?void 0:c.open)&&!y||!1,[E,R]=(0,t.useState)(!1),{isMarking:O,markingModalOpen:P,setMarkingModalOpen:_,handleActivateMarking:V,handleStopMarking:B,handleMarkingOk:D,handleMarkingStep:G,resetMarking:F}=zi(null===k||void 0===k?void 0:k.marker),[X,Y]=(0,t.useState)(!1);(0,t.useEffect)((()=>{U()}),[v,y,F]),(0,t.useEffect)((()=>{U()}),[A]);const U=()=>{T(!1),I(!1),M(!1),R(!1),F()};return(0,Z.jsxs)(d,{id:u,children:[k&&(0,Z.jsx)(Oe,{ref:C,isBufferCurve:g,id:u,config:k,compStatus:h,updateCompStatus:e=>{if(v){const n={...v,compStatus:e};f(n)}},openCross:S,openBreak:w,showPointTag:N,showChunkTag:$,setOpenBreakPoint:M,isLocked:E,isMarking:O,onMarkingStep:G}),m&&(0,Z.jsx)(en,{compName:p,domId:u,layoutConfig:x,config:v,isBufferCurve:g,setOpen:Y,openCross:S,setOpenCross:T,openBreak:w,setOpenBreak:I,showPointTag:N,setShowPointTag:e=>{if(v&&N!==e){const n={...v,compStatus:h,pointTag:{...v.pointTag,open:e}};f(n)}},showChunkTag:$,setShowChunkTag:e=>{if(v&&$!==e){const n={...v,compStatus:h,chunkTag:{...v.chunkTag,open:e}};f(n)}},openBreakPoint:L,setOpenBreakPoint:M,onRestore:()=>{var e,n;return null===(e=C.current)||void 0===e||null===(n=e.restore)||void 0===n?void 0:n.call(e)},onClearBreakPoint:()=>{var e,n;return null===(e=C.current)||void 0===e||null===(n=e.clearBreakPoint)||void 0===n?void 0:n.call(e)},isLocked:E,setIsLocked:R,isMarking:O,onActivateMarking:V,onStopMarking:B,capture:!0}),X&&(0,Z.jsx)(Di,{open:X,setOpen:Y,config:v,updateConfig:e=>{f({...e,compStatus:h})},isBufferCurve:g}),P&&(0,Z.jsx)(Xi,{open:P,setOpen:_,onOk:D})]})}},41780:(e,n,i)=>{i.d(n,{C:()=>r,c:()=>l});var t=i(754);const l=e=>{let n;return t.A.getState().global.unitList.forEach((i=>{i.units.find((n=>n.id===e))&&(n=i.units.find((n=>n.id===e)))})),n},r=e=>{var n,i;return null!==(n=null===(i=t.A.getState().global.unitList.find((n=>n.id===e)))||void 0===i?void 0:i.units)&&void 0!==n?n:[]}},42999:(e,n,i)=>{i.d(n,{A:()=>x});var t=i(65043),l=i(37097),r=i(6051),s=i(36282),o=i(18650),a=i(81143),d=i(68374);const c=a.Ay.div`
    .color-layout {
        display: flex;
        align-items: center;
    }
 
    .background-layout {
        min-width: ${(0,d.D0)("25px")} !important;
        min-height: ${(0,d.D0)("25px")} !important;
        background-color: #000;
        border-radius: 2px;
    }
    .background-img {
        width: ${(0,d.D0)("23px")};
        height: ${(0,d.D0)("23px")};
        display: flex;
        align-items:center ;
        justify-content: center;
        cursor: pointer;
    }
    .allowed {
        cursor: not-allowed;
    }

`;var u=i(70579);const x=e=>{let{onChange:n,value:i,disabled:a=!1}=e;const[d,x]=(0,t.useState)(i);(0,t.useEffect)((()=>{x(i||"#000")}),[i]);const p=(0,u.jsx)(u.Fragment,{children:(0,u.jsx)(l.Xq,{color:d,showMoreColor:!1,onChangeComplete:e=>{const{rgb:i}=e,t=`rgba(${i.r},${i.g},${i.b},${i.a})`;x(t),n&&n(t)}})});return(0,u.jsx)(c,{children:(0,u.jsx)("div",{className:"color-layout",children:(0,u.jsxs)(r.A,{children:[(0,u.jsx)("div",{className:"background-layout",style:{backgroundColor:d}}),!a&&(0,u.jsx)(s.A,{overlayClassName:"popover-sketch-picker",content:p,title:"",trigger:"click",placement:"bottom",destroyOnHidden:!0,arrow:!1,children:(0,u.jsx)("img",{className:"background-img "+(a?"allowed":""),src:o.Dp,alt:""})})]})})})}},63804:(e,n,i)=>{i.d(n,{d:()=>d,A:()=>c});var t=i(65043),l=i(67208),r=i(36950),s=i(19853),o=i.n(s);function a(){let{threshold:e=0,rootMargin:n="0px",enableIntersectionObserver:i=!0,enablePageVisibility:l=!0,enableMutationObserver:r=!0,onVisibilityChange:s=null}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const o=(0,t.useRef)(null),a=(0,t.useRef)({isIntersecting:!1,isPageVisible:!0,displayStyle:"block",position:{top:0,left:0}}),d=(0,t.useRef)(!1),c=(0,t.useCallback)((()=>{const e=a.current,n=e.isIntersecting&&e.isPageVisible&&"none"!==e.displayStyle;n!==d.current&&(d.current=n,s&&s(n))}),[s]);return(0,t.useEffect)((()=>{if(!o.current)return()=>{};const t=[];if(i){const i=new IntersectionObserver((e=>{e.forEach((e=>{const n=a.current.isIntersecting,i=e.isIntersecting;n!==i&&(a.current={...a.current,isIntersecting:i},c(),i?console.log("\ud83d\udd0d \u5143\u7d20\u8fdb\u5165\u89c6\u53e3"):console.log("\ud83d\udc7b \u5143\u7d20\u79bb\u5f00\u89c6\u53e3"))}))}),{threshold:e,rootMargin:n});i.observe(o.current),t.push((()=>i.disconnect()))}if(l){const e=()=>{const e=!document.hidden;a.current={...a.current,isPageVisible:e},c(),e?console.log("\ud83d\udc41\ufe0f \u9875\u9762\u53d8\u4e3a\u53ef\u89c1"):console.log("\ud83d\ude48 \u9875\u9762\u53d8\u4e3a\u4e0d\u53ef\u89c1")};document.addEventListener("visibilitychange",e),t.push((()=>document.removeEventListener("visibilitychange",e)))}if(r){const e=new MutationObserver((e=>{e.forEach((e=>{if("attributes"===e.type&&"style"===e.attributeName){const e=window.getComputedStyle(o.current).display;a.current={...a.current,displayStyle:e},c(),"none"===e?console.log("\ud83d\udeab \u5143\u7d20\u88ab\u9690\u85cf (display: none)"):console.log("\u2705 \u5143\u7d20\u663e\u793a\u6837\u5f0f\u6062\u590d")}}))}));e.observe(o.current,{attributes:!0,attributeFilter:["style"]}),t.push((()=>e.disconnect()))}return c(),()=>{t.forEach((e=>e()))}}),[e,n,i,l,r,c]),{targetRef:o}}const d={daqbuffer:"daqbuffer","\u4e8c\u7ef4\u6570\u7ec4":"doubleArray","\u4e8c\u7ef4\u6570\u7ec4\u96c6\u5408":"doubleArraySet"},c=(e,n)=>{let{controlCompId:i,dataSourceType:s,dataSourceCode:d,dataCodes:c,timer:u=-1,number:x=-1,testStatus:p=1,daqCurveSelectedSampleCodes:v}=e;const h=(0,t.useRef)(!1),f=(0,t.useRef)(!1),g=(0,t.useRef)(null),m=(0,t.useRef)(!1),y=(0,t.useRef)(!1),A=(0,t.useRef)();(0,t.useRef)(n).current=n,(0,t.useEffect)((()=>{if(!d||!i||!s||!c||0===c.length)return;const e={templateName:(0,r.n1)(),controlCompId:i,dataSourceType:s,dataSourceCode:d,dataCodes:c,timer:u,number:x,testStatus:p,daqCurveSelectedSampleCodes:null!==v&&void 0!==v?v:[]};o()(e,A.current)||(null===n||void 0===n||n(),A.current=e,m.current?h.current?(0,l.pj8)({...A.current}):(0,l.i_N)({...A.current}).then((()=>{h.current=!0,f.current=!0})):h.current&&(y.current=!0))}),[i,s,d,c,u,x,p,v]);const{targetRef:j}=a({onVisibilityChange:(0,t.useCallback)((async e=>{var n,i,t;if(m.current=e,e&&A.current){if(!h.current)return await(0,l.i_N)({...A.current}),h.current=!0,void(f.current=!0);if(y.current)return(0,l.pj8)({...A.current}),void(y.current=!1)}(g.current&&clearTimeout(g.current),e&&!f.current&&null!==(n=A.current)&&void 0!==n&&n.controlCompId)&&(await(0,l.pkF)(null===(t=A.current)||void 0===t?void 0:t.controlCompId),f.current=!0);!e&&f.current&&null!==(i=A.current)&&void 0!==i&&i.controlCompId&&(g.current=setTimeout((async()=>{await(0,l.UVQ)(A.current.controlCompId),f.current=!1}),3e3))}),[])});return(0,t.useEffect)((()=>()=>{g.current&&clearTimeout(g.current),h.current&&(0,l.UWZ)(A.current.controlCompId)}),[]),{targetRef:j}}},66966:(e,n,i)=>{i.d(n,{L:()=>s});var t=i(754),l=i(72238),r=i(41780);const s=e=>{let{sourceType:n,sourceInputCode:i}=e;const{inputVariableMap:s}=t.A.getState().inputVariable;let o=[];if(n===l.xO.\u5355\u6570\u636e\u6e90){var a,d;const e=s.get(i);o=null!==(a=null===e||void 0===e||null===(d=e.double_array_tab)||void 0===d?void 0:d.columns)&&void 0!==a?a:[]}if(n===l.xO.\u591a\u6570\u636e\u6e90){var c,u,x;const e=s.get(i),n=s.get(null===e||void 0===e||null===(c=e.double_array_list_tab)||void 0===c?void 0:c.dataSourceCode);o=null!==(u=null===n||void 0===n||null===(x=n.double_array_tab)||void 0===x?void 0:x.columns)&&void 0!==u?u:[]}return o.filter((e=>"Number"===e.type)).map((e=>{var n,i,t;const l=(0,r.C)(null===e||void 0===e||null===(n=e.typeParam)||void 0===n?void 0:n.unitId);return{code:e.code,name:e.showName,dimensionId:null===e||void 0===e||null===(i=e.typeParam)||void 0===i?void 0:i.dimensionId,unitId:null===e||void 0===e||null===(t=e.typeParam)||void 0===t?void 0:t.unitId,unitList:l}}))}},68406:(e,n,i)=>{i.d(n,{c:()=>t});const t={base:{isName:!0,name:"\u66f2\u7ebf",sourceType:i(72238).xO.\u5355\u6570\u636e\u6e90,sourceInputCode:"",updateFreq:180,crossInputCode:"",xOffset:0,yOffset:0},curveGroup:{yAxis:{isEnable:!0,index:0,name:"Y1\u8f74\u66f2\u7ebf\u7ec4",xSignal:"",xUnit:"",ySignal:[],curves:{}},y2Axis:{isEnable:!1,index:1,name:"Y2\u8f74\u66f2\u7ebf\u7ec4",xSignal:"",xUnit:"",ySignal:[],curves:{}}},xAxis:{name:"x\u8f74",unit:"",proportionType:"not",lowLimit:0,upLimit:10,lastRange:10,isLog:!1,type:"solid",thickness:2,color:"#000000",isGrid:!1,gridType:"solid",gridThickness:1,gridColor:"#000000",isZeroLine:!1,zeroLineType:"solid",zeroLineThickness:1,zeroLineColor:"#000000"},yAxis:{name:"y\u8f74",proportionType:"not",lowLimit:0,upLimit:10,lastRange:10,isLog:!1,type:"solid",thickness:2,color:"#000000",isGrid:!1,gridType:"solid",gridThickness:1,gridColor:"#000000",isZeroLine:!1,zeroLineType:"solid",zeroLineThickness:1,zeroLineColor:"#000000"},y2Axis:{name:"y2\u8f74",proportionType:"not",lowLimit:0,upLimit:10,lastRange:10,isLog:!1,type:"solid",thickness:2,color:"#000000",isGrid:!1,gridType:"solid",gridThickness:1,gridColor:"#000000",isZeroLine:!1,zeroLineType:"solid",zeroLineThickness:1,zeroLineColor:"#000000"},auxiliary:[],legend:{open:!1},pointTag:{open:!1},chunkTag:{open:!0,list:[]},tag:{isChunkTag:!1,chunkTags:[]},breakPoint:{},marker:[],defineAxis:{isDefineAxis:!1,inputCode:"",source:[]}}},72238:(e,n,i)=>{i.d(n,{g3:()=>l,xO:()=>t});const t={"\u5355\u6570\u636e\u6e90":"single","\u591a\u6570\u636e\u6e90":"multi"},l={"\u4e0a\u4e0b\u9650\u8303\u56f4":"not","\u6b63\u5411\u4f4d\u7f6e\u6269\u5c55":"extend","\u8d1f\u5411\u4f4d\u7f6e\u6269\u5c55":"min-extend","\u5e94\u7528\u8303\u56f4":"all","\u6570\u636e\u8303\u56f4":"data-range","\u626b\u63cf\u8303\u56f4":"last-range"}},74448:(e,n,i)=>{i.d(n,{C:()=>r});var t=i(72238),l=i(30263);const r=e=>{let{sourceType:n}=e;if(n===t.xO.\u5355\u6570\u636e\u6e90)return{optSample:{name:"\u5f53\u524d\u8bd5\u6837",lines:[]}};const i={};return(0,l.w)().forEach((e=>{i[e.code]={name:null===e||void 0===e?void 0:e.name,lines:[]}})),i}},84617:(e,n,i)=>{i.d(n,{A:()=>d});var t=i(65043),l=i(60383),r=i(80077),s=i(39713),o=i(36950),a=i(91465);const d=e=>{let{controlCompId:n,onMessage:i}=e;const d=(0,r.wA)(),{useSubscriber:c}=(0,s.A)(),u=(0,t.useRef)(),x=(0,t.useRef)(i),p=(0,t.useRef)();(0,t.useEffect)((()=>{x.current=i}),[i]),(0,t.useEffect)((()=>(v(),()=>{var e,n;null===(e=u.current)||void 0===e||null===(n=e.close)||void 0===n||n.call(e)})),[n]);const v=async()=>{const e=`${(0,o.n1)()}-ControlCompUIData-${n}-UIData`;u.current=await c(e);for await(const[n,r]of u.current){let e;try{e=l.D(r)}catch(i){try{e=JSON.parse(r)}catch(t){console.error("GridLayout\u6570\u636e\u89e3\u6790\u5931\u8d25",t)}}2===e.mode?p.current=d((0,a.J_)("\u5927\u6570\u636e\u91cf\u52a0\u8f7d\u4e2d...")):3===e.mode?d((0,a.ge)(p.current)):x.current(e)}}}}}]);
//# sourceMappingURL=1234.f197759c.chunk.js.map