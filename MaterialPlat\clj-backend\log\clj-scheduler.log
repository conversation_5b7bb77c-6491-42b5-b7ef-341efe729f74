25-09-11 11:35:19:231 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_36-3601749f-b58b-4018-b8c3-e1bfc760f8e0 初始化
25-09-11 11:35:19:335 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 11:35:19:335 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-3601749f-b58b-4018-b8c3-e1bfc760f8e0","SubTaskID":"start-node-action","MsgBody":{}}
25-09-11 11:35:19:341 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 11:35:19:342 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-3601749f-b58b-4018-b8c3-e1bfc760f8e0","SubTaskID":"action-69805eae-7d00-4c8b-b639-6728658d0621","MsgBody":{}}
25-09-11 11:35:19:383 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 11:35:19:384 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-3601749f-b58b-4018-b8c3-e1bfc760f8e0","SubTaskID":"end-node-action","MsgBody":{}}
25-09-11 11:35:19:385 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_36-3601749f-b58b-4018-b8c3-e1bfc760f8e0  Stop: {:parent nil}
25-09-11 11:35:19:592 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-11 11:36:08:915 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 11:36:08:916 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 10,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 10,\r\n  \u0022Code\u0022: \u0022input_yzlwjsjgzq\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: true,\r\n  \u0022IsCheck\u0022: true,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":false,"SendToUI":true,"Type":"input"}}
25-09-11 11:36:09:067 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 11:36:09:068 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 0.1,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 0.1,\r\n  \u0022Code\u0022: \u0022input_yzlwjsjglwcd\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 11:36:09:073 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - RUN_SCRIPT_RSP
25-09-11 11:36:09:074 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"project_36","ScriptId":"12a4f83e-01f1-49b3-b116-f8b68c965654","Result":false}
25-09-11 11:36:14:465 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_36-fbc2d565-2a68-473a-a2b5-7e932c5ae3a7 初始化
25-09-11 11:36:14:505 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 11:36:14:506 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-fbc2d565-2a68-473a-a2b5-7e932c5ae3a7","SubTaskID":"start-node-action","MsgBody":{}}
25-09-11 11:36:14:513 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 11:36:14:514 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"process-action","ClassName":"project_36","ProcessID":"project_36-fbc2d565-2a68-473a-a2b5-7e932c5ae3a7","SubTaskID":"onlyAction-eafe88f9-58ec-4721-aeb9-248ff2ff9a3e","MsgBody":{"Cmd":"abort","InstCode":"sample_14785d372","ActionID":"ab56adb1-93c8-4ca0-a353-39b5c7a3ed16"}}
25-09-11 11:36:14:516 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-11 11:36:14:516 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_36-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16  Stop: {:parent nil}
25-09-11 11:36:14:518 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-11 11:36:14:519 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 11:36:14:520 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-fbc2d565-2a68-473a-a2b5-7e932c5ae3a7","SubTaskID":"onlyAction-eafe88f9-58ec-4721-aeb9-248ff2ff9a3e","MsgBody":{}}
25-09-11 11:36:14:859 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 11:36:14:859 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 13312,\r\n  \u0022Dimension\u0022: \u0022dimension_cycle\u0022,\r\n  \u0022Unit\u0022: \u0022unit_cdw178e4a3\u0022,\r\n  \u0022DisplayUnitName\u0022: \u0022cycle\u0022,\r\n  \u0022DisplayValue\u0022: 13312,\r\n  \u0022Code\u0022: \u0022input_starting_cycle\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 11:36:14:864 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 11:36:14:864 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-fbc2d565-2a68-473a-a2b5-7e932c5ae3a7","SubTaskID":"SubTaskTimer-e8fc4f71-e7b1-40a4-9dec-85498fcdec3b","MsgBody":{}}
25-09-11 11:36:14:890 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 11:36:14:891 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 13310,\r\n  \u0022Dimension\u0022: \u0022dimension_cycle\u0022,\r\n  \u0022Unit\u0022: \u0022unit_cdw178e4a3\u0022,\r\n  \u0022DisplayUnitName\u0022: \u0022cycle\u0022,\r\n  \u0022DisplayValue\u0022: 13310,\r\n  \u0022Code\u0022: \u0022input_starting_cycle\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 11:36:14:896 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 11:36:14:897 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_yzlwcxksbs\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: true,\r\n  \u0022IsCheck\u0022: true,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 11:36:14:903 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 11:36:14:904 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: \u0022RESTART\u0022,\r\n  \u0022Code\u0022: \u0022input_yzlwhcq\u0022,\r\n  \u0022Type\u0022: \u0022Select\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 11:36:14:908 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 11:36:14:909 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: false,\r\n  \u0022Code\u0022: \u0022input_yzlwbs\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 11:36:14:913 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 11:36:14:914 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 0,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 0,\r\n  \u0022Code\u0022: \u0022input_kf\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 11:36:14:918 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 11:36:14:918 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 870.1947192045508,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 870.1947192045508,\r\n  \u0022Code\u0022: \u0022input_allKmax\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 11:36:14:922 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 11:36:14:923 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 0,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 0,\r\n  \u0022Code\u0022: \u0022input_kf\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 11:36:14:929 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 11:36:14:930 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 870.1947192045508,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 870.1947192045508,\r\n  \u0022Code\u0022: \u0022input_zzzdylqdyz\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 11:36:14:940 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 11:36:14:941 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 15.680025100708008,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 15.680025100708008,\r\n  \u0022Code\u0022: \u0022input_zzzdl\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 11:36:14:950 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 11:36:14:952 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 47.28572965796207,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 47.28572965796207,\r\n  \u0022Code\u0022: \u0022input_yzlwjglwcd\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 11:36:14:959 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 11:36:14:960 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-fbc2d565-2a68-473a-a2b5-7e932c5ae3a7","SubTaskID":"SubTaskEvalScript-4d9f4ca2-4006-412b-a5ff-cfcdfc18f23b","MsgBody":{}}
25-09-11 11:36:14:990 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 11:36:14:991 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-fbc2d565-2a68-473a-a2b5-7e932c5ae3a7","SubTaskID":"SubTaskEvalScript-57665592-8645-4b41-820a-b3eb30770c0c","MsgBody":{}}
25-09-11 11:36:15:093 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 11:36:15:094 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_yzlwcxksbs\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: true,\r\n  \u0022IsCheck\u0022: true,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 11:36:15:098 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 11:36:15:099 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: \u0022RESTART\u0022,\r\n  \u0022Code\u0022: \u0022input_yzlwhcq\u0022,\r\n  \u0022Type\u0022: \u0022Select\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 11:36:15:102 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 11:36:15:103 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: false,\r\n  \u0022Code\u0022: \u0022input_yzlwbs\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 11:36:15:106 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 11:36:15:107 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 12.857620525360108,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 12.857620525360108,\r\n  \u0022Code\u0022: \u0022input_yzlw_fzx1\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 11:36:15:110 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 11:36:15:111 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 4.390406799316406,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 4.390406799316406,\r\n  \u0022Code\u0022: \u0022input_yzlw_fzx2\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 11:36:15:114 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 11:36:15:114 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 2.568002223968506,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 2.568002223968506,\r\n  \u0022Code\u0022: \u0022input_yzlw_fzx_qd\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 11:36:15:119 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 11:36:15:120 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 16.680025100708008,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 16.680025100708008,\r\n  \u0022Code\u0022: \u0022input_yzlw_fzx_zd\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 11:36:15:124 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 11:36:15:124 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-fbc2d565-2a68-473a-a2b5-7e932c5ae3a7","SubTaskID":"SubTaskEvalScript-cc8952a1-430c-45c2-bb47-4082987dda7a","MsgBody":{}}
25-09-11 11:36:15:176 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 11:36:15:178 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"process-action","ClassName":"project_36","ProcessID":"project_36-fbc2d565-2a68-473a-a2b5-7e932c5ae3a7","SubTaskID":"onlyAction-04d036d1-723f-47e0-a8b9-5fd50c3aa312","MsgBody":{"Cmd":"start","InstCode":"sample_14785d372","ActionID":"a5112cfc-f9ee-4232-86e0-ff1e81e9df45"}}
25-09-11 11:36:15:181 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_36-a5112cfc-f9ee-4232-86e0-ff1e81e9df45 初始化
25-09-11 11:36:15:241 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 11:36:15:241 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-a5112cfc-f9ee-4232-86e0-ff1e81e9df45","SubTaskID":"start-node-action","MsgBody":{}}
25-09-11 11:36:15:426 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SAVE_PROJECT
25-09-11 11:36:15:426 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProjectID":36}
25-09-11 11:36:15:427 DESKTOP-3BSREDP INFO [clj-scheduler.mq:47] - Received SAVE_PROJECT ZMQ message, dispatching to project channel: {:ProjectID 36}
25-09-11 11:36:15:428 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 11:36:15:428 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-a5112cfc-f9ee-4232-86e0-ff1e81e9df45","SubTaskID":"SubTaskEvalScript-c9b5b399-c9dd-439e-b815-683c9bdfc8b4","MsgBody":{}}
25-09-11 11:36:15:492 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 11:36:15:493 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-a5112cfc-f9ee-4232-86e0-ff1e81e9df45","SubTaskID":"end-node-action","MsgBody":{}}
25-09-11 11:36:15:494 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_36-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16 初始化
25-09-11 11:36:15:494 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_36-a5112cfc-f9ee-4232-86e0-ff1e81e9df45  Stop: {:parent {:Type "process-action", :ClassName "project_36", :ProcessID "project_36-fbc2d565-2a68-473a-a2b5-7e932c5ae3a7", :SubTaskID "onlyAction-04d036d1-723f-47e0-a8b9-5fd50c3aa312", :MsgBody {:Cmd "start", :InstCode "sample_14785d372", :ActionID "a5112cfc-f9ee-4232-86e0-ff1e81e9df45"}}}
25-09-11 11:36:15:563 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-11 11:36:15:573 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 11:36:15:573 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-fbc2d565-2a68-473a-a2b5-7e932c5ae3a7","SubTaskID":"onlyAction-04d036d1-723f-47e0-a8b9-5fd50c3aa312","MsgBody":{}}
25-09-11 11:36:15:573 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 11:36:15:575 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"start-node-action","MsgBody":{}}
25-09-11 11:36:15:576 DESKTOP-3BSREDP INFO [clj-scheduler.script:9] - 执行脚本:int flag = 0;

int deviceId = 0;
int ret = Model.station.Ccss_Connected(deviceId);
if (ret == 0) {
  /* 已连接 */ 
  flag++;
} else { 
  /* 未连接 */ 
  Model.GetVarByName<TextInputVar>("input_yzlwcsts").Value = "平面应变断裂韧度试验 预制疲劳裂纹检查操作试验运行"+Model.CurrentInst.Name+"异常信息：控制器连接失败，请重新连接控制器。";
  return false;
}
int ret1 = Model.station.Ccss_DriveReady(deviceId);
if (ret1 == 0) { 
  /* 准备就绪 */ 
  flag++;
} else {
  /* 未就绪 */ 
  Model.GetVarByName<TextInputVar>("input_yzlwcsts").Value = "控制器未在启动状态";
  return false;
}
if(flag == 2){
  Model.GetVarByName<TextInputVar>("input_yzlwcsts").Value = "请确认是否使用"+Model.CurrentInst.Name+"进行平面应变断裂韧度试验 预制疲劳裂纹试验？";
  return true;
}
return false;
25-09-11 11:36:15:583 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 11:36:15:585 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-fbc2d565-2a68-473a-a2b5-7e932c5ae3a7","SubTaskID":"end-node-action","MsgBody":{}}
25-09-11 11:36:15:587 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_36-fbc2d565-2a68-473a-a2b5-7e932c5ae3a7  Stop: {:parent nil}
25-09-11 11:36:15:630 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 11:36:15:631 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: \u0022\\u8BF7\\u786E\\u8BA4\\u662F\\u5426\\u4F7F\\u7528\\u8BD5\\u6837147\\u8FDB\\u884C\\u5E73\\u9762\\u5E94\\u53D8\\u65AD\\u88C2\\u97E7\\u5EA6\\u8BD5\\u9A8C \\u9884\\u5236\\u75B2\\u52B3\\u88C2\\u7EB9\\u8BD5\\u9A8C\\uFF1F\u0022,\r\n  \u0022Code\u0022: \u0022input_yzlwcsts\u0022,\r\n  \u0022Type\u0022: \u0022Text\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 11:36:15:641 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - RUN_SCRIPT_RSP
25-09-11 11:36:15:642 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"project_36","ScriptId":"e9ff0038-bc14-448f-8f6c-10d6259f73c3","Result":true}
25-09-11 11:36:15:642 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-11 11:36:16:645 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 11:36:16:646 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_three\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 11:36:16:653 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 11:36:16:653 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubtaskDialogBox-60715637-4d10-4965-aff6-da8166a38c78","MsgBody":{}}
25-09-11 11:36:16:655 DESKTOP-3BSREDP INFO [clj-scheduler.script:9] - 执行脚本:return Model.GetVarByName<BooleanInputVar>("input_three").Value;
25-09-11 11:36:16:683 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - RUN_SCRIPT_RSP
25-09-11 11:36:16:684 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"project_36","ScriptId":"f519a313-4e60-435e-9270-1c6851269c45","Result":true}
25-09-11 11:36:16:713 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 11:36:16:714 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_yzlwbs\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 11:36:16:717 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 11:36:16:718 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubTaskEvalScript-d676b27c-cb55-4186-b751-0d80789aef79","MsgBody":{}}
25-09-11 11:36:16:720 DESKTOP-3BSREDP INFO [clj-scheduler.script:9] - 执行脚本:var flag = Model.GetVarByName<BooleanInputVar>("input_yzlwcxksbs").Value;
//是否为重新开始
return flag;
25-09-11 11:36:16:745 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - RUN_SCRIPT_RSP
25-09-11 11:36:16:746 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"project_36","ScriptId":"54f0854c-5db6-40db-8c7c-4716b58e2b29","Result":true}
25-09-11 11:36:16:786 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 11:36:16:787 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubTaskEvalScript-9567e092-27d1-4397-87eb-144b0531a173","MsgBody":{}}
25-09-11 11:36:17:427 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 11:36:17:428 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 0,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 0,\r\n  \u0022Code\u0022: \u0022input_dqzszqs\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 11:36:17:432 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 11:36:17:433 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 0,\r\n  \u0022Dimension\u0022: \u0022dimension_cycle\u0022,\r\n  \u0022Unit\u0022: \u0022unit_cdw178e4a3\u0022,\r\n  \u0022DisplayUnitName\u0022: \u0022cycle\u0022,\r\n  \u0022DisplayValue\u0022: 0,\r\n  \u0022Code\u0022: \u0022input_starting_cycle\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 11:36:17:437 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 11:36:17:437 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubTaskEvalScript-f3c6e835-6291-45be-bd63-350f12f45f9a","MsgBody":{}}
25-09-11 11:36:17:458 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 11:36:17:459 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubTaskEvalScript-304442c2-62c7-4f92-bca4-5425989dd1cd","MsgBody":{}}
25-09-11 11:36:17:483 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 11:36:17:484 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubTaskEvalScript-7d35a2c8-7abb-4dbb-a88d-93a7760edc8b","MsgBody":{}}
25-09-11 11:36:17:506 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 11:36:17:507 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubTaskEvalScript-0d487418-fdc3-4d88-b707-10257acb369d","MsgBody":{}}
25-09-11 11:36:18:024 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 11:36:18:025 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubTaskTimer-878d0189-0bb6-4c27-8cb5-42eecfa315b6","MsgBody":{}}
25-09-11 11:36:18:025 DESKTOP-3BSREDP INFO [clj-scheduler.script:9] - 执行脚本:
//终止kmax
double abort_kmax = Model.GetVarByName<NumberInputVar>("input_abort_kmax").Value;
//初始kmax
double start_kmax = Model.GetVarByName<NumberInputVar>("input_start_kmax").Value;
//力值比
double force_ratio = Model.GetVarByName<NumberInputVar>("input_force_ratio").Value;
//试验频率
double prefabricated_crack_frequency =  Model.GetVarByName<NumberInputVar>("input_prefabricated_crack_frequency").Value;
//预制循环周次
double prefabrication_cycle = Model.GetVarByName<NumberInputVar>("input_prefabrication_cycle").Value;
//起始循环周次
double starting_cycle = Model.GetVarByName<NumberInputVar>("input_starting_cycle").Value;
//到达均值速度
double average_speed_arrival = Model.GetVarByName<NumberInputVar>("input_average_speed_arrival").Value;
//预制裂纹长度
double precast_crack_length = Model.GetVarByName<NumberInputVar>("input_precast_crack_length").Value;
//线性拟合数据上限
double upper_limit_of_linear_fitting_data = Model.GetVarByName<NumberInputVar>("input_upper_limit_of_linear_fitting_data").Value;
//线性拟合数据下限
double lower_bound_of_linear_fitting_data = Model.GetVarByName<NumberInputVar>("input_lower_bound_of_linear_fitting_data").Value;
//未萌生裂纹循环
double undeveloped_crack_cycle = Model.GetVarByName<NumberInputVar>("input_undeveloped_crack_cycle").Value;
//Kmax增幅比
double increase_ratio_kmax = Model.GetVarByName<NumberInputVar>("input_increase_ratio_kmax").Value;

//获取下位机量程
double? NominalValue = 0.0; //标称值
double? MaxValue = 0.0; //最大值
double? MinValue = 0.0; //最小值
double? UpperSoftLimit = 0.0; //软件上限位值
double? LowerSoftLimit = 0.0; //软件下限位值
int? SoftLimitReaction = 0;  //软件限位响应类型
double? BasicTare = 0.0;  //基础长时清零
double? Tare = 0.0;   //内存清零
double? McFilterTime = 0.0;  // 显示滤波
double? CtrlFilterTime = 0.0;   //控制滤波
//传入轴号、传感器号
int? ret = Model.station.Ccss_ReadSensorPara(0, 1, ref NominalValue, ref MaxValue, ref MinValue, ref UpperSoftLimit,
ref LowerSoftLimit, ref SoftLimitReaction, ref BasicTare, ref Tare, ref McFilterTime, ref CtrlFilterTime);
//打印量程
FuncLibs.Logger.Error("传感器信息：");
FuncLibs.Logger.Error($"ret : {ret}, NominalValue: {NominalValue}, MaxValue: {MaxValue}, MinValue: {MinValue}, UpperSoftLimit : {UpperSoftLimit}, LowerSoftLimit: {LowerSoftLimit}, SoftLimitReaction: {SoftLimitReaction}, BasicTare: {BasicTare},Tare: {Tare}, McFilterTime: {McFilterTime}, CtrlFilterTime: {CtrlFilterTime}");

// 计算周期波所用的参数
//峰谷值
double forceMax = 0;
double forceMin = 0;
TestExpert.PlaneStrainFractureToughnessMethod.Commons.PlaneStrainFractureToughnessMethod
.GetForce( start_kmax,  precast_crack_length, out forceMax);
forceMin = forceMax * force_ratio;


Console.WriteLine($"预制计算出的最大力:{forceMax} ");

if(forceMax<MaxValue && forceMax>MinValue && forceMin<MaxValue && forceMin>MinValue && forceMax>forceMin){
  // 赋值峰谷值变量
  Model.GetVarByName<NumberInputVar>("input_yzlw_valley").Value = forceMin;
  Model.GetVarByName<NumberInputVar>("input_yzlw_peak").Value = forceMax;
}else{
  Model.GetVarByName<NumberInputVar>("input_yzlw_valley").Value = 0;
  Model.GetVarByName<NumberInputVar>("input_yzlw_peak").Value = 0;
  return false;
}


Console.WriteLine($"预制裂纹峰值:{Model.GetVarByName<NumberInputVar>("input_yzlw_peak").Value} ");
Console.WriteLine($"预制裂纹谷值:{Model.GetVarByName<NumberInputVar>("input_yzlw_valley").Value} ");

//赋值循环次数
Model.GetVarByName<NumberInputVar>("input_yzlwxhcs").Value = prefabrication_cycle - starting_cycle;

Model.iVariable["moniliewen"]=0;
Model.dVariableArray["tenLength"] = new double[10];

return true;
25-09-11 11:36:18:054 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 11:36:18:055 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 1.568002635231078,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 1.568002635231078,\r\n  \u0022Code\u0022: \u0022input_yzlw_valley\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 11:36:18:060 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 11:36:18:061 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 15.68002635231078,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 15.68002635231078,\r\n  \u0022Code\u0022: \u0022input_yzlw_peak\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 11:36:18:065 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 11:36:18:065 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 1000000,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 1000000,\r\n  \u0022Code\u0022: \u0022input_yzlwxhcs\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 11:36:18:069 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - RUN_SCRIPT_RSP
25-09-11 11:36:18:070 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"project_36","ScriptId":"98839914-42eb-4254-a208-3e4263940a60","Result":true}
25-09-11 11:36:18:237 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 11:36:18:237 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_one\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 11:36:20:429 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 11:36:20:431 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 15.11165088785825,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 15.11165088785825,\r\n  \u0022Code\u0022: \u0022input_allKmax\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 11:36:20:458 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 11:36:20:459 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 19.287011527464525,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 19.287011527464525,\r\n  \u0022Code\u0022: \u0022input_allKmax\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 11:36:20:464 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 11:36:20:464 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 24.97584148635491,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 24.97584148635491,\r\n  \u0022Code\u0022: \u0022input_allKmax\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 11:36:20:660 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 11:36:20:660 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 33.891597436808524,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 33.891597436808524,\r\n  \u0022Code\u0022: \u0022input_allKmax\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 11:36:20:665 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 11:36:20:666 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 49.97722700836602,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 49.97722700836602,\r\n  \u0022Code\u0022: \u0022input_allKmax\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 11:36:20:689 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 11:36:20:690 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 84.46280537717269,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 84.46280537717269,\r\n  \u0022Code\u0022: \u0022input_allKmax\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 11:36:20:693 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 11:36:20:694 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 184.24547543994308,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 184.24547543994308,\r\n  \u0022Code\u0022: \u0022input_allKmax\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 11:36:20:871 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 11:36:20:872 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 870.1947192045508,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 870.1947192045508,\r\n  \u0022Code\u0022: \u0022input_allKmax\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 11:38:28:177 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 11:38:28:178 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: false,\r\n  \u0022Code\u0022: \u0022input_ZQCSDS\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":false,"SendToUI":true,"Type":"input"}}
25-09-11 11:38:28:238 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 11:38:28:238 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: false,\r\n  \u0022Code\u0022: \u0022input_ZQCSDS\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":false,"SendToUI":true,"Type":"input"}}
25-09-11 11:38:28:402 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 11:38:28:402 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: false,\r\n  \u0022Code\u0022: \u0022input_ZQCSDS\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":false,"SendToUI":true,"Type":"input"}}
25-09-11 11:38:38:150 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_36-fbc2d565-2a68-473a-a2b5-7e932c5ae3a7 初始化
25-09-11 11:38:38:198 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 11:38:38:198 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-fbc2d565-2a68-473a-a2b5-7e932c5ae3a7","SubTaskID":"start-node-action","MsgBody":{}}
25-09-11 11:38:38:215 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 11:38:38:215 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"process-action","ClassName":"project_36","ProcessID":"project_36-fbc2d565-2a68-473a-a2b5-7e932c5ae3a7","SubTaskID":"onlyAction-eafe88f9-58ec-4721-aeb9-248ff2ff9a3e","MsgBody":{"Cmd":"abort","InstCode":"sample_14785d372","ActionID":"ab56adb1-93c8-4ca0-a353-39b5c7a3ed16"}}
25-09-11 11:38:38:217 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-11 11:38:38:217 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_36-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16  Stop: {:parent nil}
25-09-11 11:38:38:392 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-11 11:38:38:393 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 11:38:38:393 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-fbc2d565-2a68-473a-a2b5-7e932c5ae3a7","SubTaskID":"onlyAction-eafe88f9-58ec-4721-aeb9-248ff2ff9a3e","MsgBody":{}}
25-09-11 11:38:38:412 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 11:38:38:413 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"peakGatherData-96560ef5-387c-45b7-850a-b53d5e44f2f6","MsgBody":{}}
25-09-11 11:38:38:418 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 11:38:38:419 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"arrayDataHandler-3c66b213-0124-415a-ab07-5063a2b97bcc","MsgBody":{}}
25-09-11 11:38:38:420 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 11:38:38:421 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"hightGatherData-6b550731-994e-4162-8a77-5a5c97e872d7","MsgBody":{}}
25-09-11 11:38:38:422 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 11:38:38:422 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"daqCycle-55422a18-1e18-4163-96d7-92687306d8e7","MsgBody":{}}
25-09-11 11:38:38:471 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 11:38:38:472 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 1916,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 1916,\r\n  \u0022Code\u0022: \u0022input_dqzszqs\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 11:38:38:477 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 11:38:38:478 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 1916,\r\n  \u0022Dimension\u0022: \u0022dimension_cycle\u0022,\r\n  \u0022Unit\u0022: \u0022unit_cdw178e4a3\u0022,\r\n  \u0022DisplayUnitName\u0022: \u0022cycle\u0022,\r\n  \u0022DisplayValue\u0022: 1916,\r\n  \u0022Code\u0022: \u0022input_starting_cycle\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 11:38:38:625 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 11:38:38:627 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 1916,\r\n  \u0022Dimension\u0022: \u0022dimension_cycle\u0022,\r\n  \u0022Unit\u0022: \u0022unit_cdw178e4a3\u0022,\r\n  \u0022DisplayUnitName\u0022: \u0022cycle\u0022,\r\n  \u0022DisplayValue\u0022: 1916,\r\n  \u0022Code\u0022: \u0022input_starting_cycle\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 11:38:38:631 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 11:38:38:632 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-fbc2d565-2a68-473a-a2b5-7e932c5ae3a7","SubTaskID":"SubTaskTimer-e8fc4f71-e7b1-40a4-9dec-85498fcdec3b","MsgBody":{}}
25-09-11 11:38:38:660 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 11:38:38:661 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 1915,\r\n  \u0022Dimension\u0022: \u0022dimension_cycle\u0022,\r\n  \u0022Unit\u0022: \u0022unit_cdw178e4a3\u0022,\r\n  \u0022DisplayUnitName\u0022: \u0022cycle\u0022,\r\n  \u0022DisplayValue\u0022: 1915,\r\n  \u0022Code\u0022: \u0022input_starting_cycle\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 11:38:38:667 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 11:38:38:668 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_yzlwcxksbs\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: true,\r\n  \u0022IsCheck\u0022: true,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 11:38:38:672 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 11:38:38:673 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: \u0022RESTART\u0022,\r\n  \u0022Code\u0022: \u0022input_yzlwhcq\u0022,\r\n  \u0022Type\u0022: \u0022Select\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 11:38:38:693 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 11:38:38:693 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: false,\r\n  \u0022Code\u0022: \u0022input_yzlwbs\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 11:38:38:698 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 11:38:38:699 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 0,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 0,\r\n  \u0022Code\u0022: \u0022input_kf\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 11:38:38:704 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 11:38:38:705 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 870.1947192045508,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 870.1947192045508,\r\n  \u0022Code\u0022: \u0022input_allKmax\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 11:38:38:717 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 11:38:38:717 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 0,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 0,\r\n  \u0022Code\u0022: \u0022input_kf\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 11:38:38:723 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 11:38:38:725 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 870.1947192045508,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 870.1947192045508,\r\n  \u0022Code\u0022: \u0022input_zzzdylqdyz\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 11:38:38:735 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 11:38:38:736 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 15.680025100708008,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 15.680025100708008,\r\n  \u0022Code\u0022: \u0022input_zzzdl\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 11:38:38:745 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 11:38:38:746 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 47.28572965796207,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 47.28572965796207,\r\n  \u0022Code\u0022: \u0022input_yzlwjglwcd\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 11:38:38:756 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 11:38:38:757 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-fbc2d565-2a68-473a-a2b5-7e932c5ae3a7","SubTaskID":"SubTaskEvalScript-4d9f4ca2-4006-412b-a5ff-cfcdfc18f23b","MsgBody":{}}
25-09-11 11:38:38:775 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 11:38:38:775 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-fbc2d565-2a68-473a-a2b5-7e932c5ae3a7","SubTaskID":"SubTaskEvalScript-57665592-8645-4b41-820a-b3eb30770c0c","MsgBody":{}}
25-09-11 11:38:38:798 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 11:38:38:800 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_yzlwcxksbs\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: true,\r\n  \u0022IsCheck\u0022: true,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 11:38:38:804 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 11:38:38:804 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: \u0022RESTART\u0022,\r\n  \u0022Code\u0022: \u0022input_yzlwhcq\u0022,\r\n  \u0022Type\u0022: \u0022Select\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 11:38:38:808 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 11:38:38:808 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: false,\r\n  \u0022Code\u0022: \u0022input_yzlwbs\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 11:38:38:812 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 11:38:38:812 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 12.857620525360108,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 12.857620525360108,\r\n  \u0022Code\u0022: \u0022input_yzlw_fzx1\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 11:38:38:817 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 11:38:38:818 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 4.390406799316406,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 4.390406799316406,\r\n  \u0022Code\u0022: \u0022input_yzlw_fzx2\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 11:38:38:822 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 11:38:38:822 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 2.568002223968506,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 2.568002223968506,\r\n  \u0022Code\u0022: \u0022input_yzlw_fzx_qd\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 11:38:38:825 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 11:38:38:826 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 16.680025100708008,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 16.680025100708008,\r\n  \u0022Code\u0022: \u0022input_yzlw_fzx_zd\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 11:38:38:830 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 11:38:38:830 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-fbc2d565-2a68-473a-a2b5-7e932c5ae3a7","SubTaskID":"SubTaskEvalScript-cc8952a1-430c-45c2-bb47-4082987dda7a","MsgBody":{}}
25-09-11 11:38:38:836 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 11:38:38:837 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"process-action","ClassName":"project_36","ProcessID":"project_36-fbc2d565-2a68-473a-a2b5-7e932c5ae3a7","SubTaskID":"onlyAction-04d036d1-723f-47e0-a8b9-5fd50c3aa312","MsgBody":{"Cmd":"start","InstCode":"sample_14785d372","ActionID":"a5112cfc-f9ee-4232-86e0-ff1e81e9df45"}}
25-09-11 11:38:38:843 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_36-a5112cfc-f9ee-4232-86e0-ff1e81e9df45 初始化
25-09-11 11:38:38:907 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 11:38:38:907 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-a5112cfc-f9ee-4232-86e0-ff1e81e9df45","SubTaskID":"start-node-action","MsgBody":{}}
25-09-11 11:38:38:965 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SAVE_PROJECT
25-09-11 11:38:38:966 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProjectID":36}
25-09-11 11:38:38:967 DESKTOP-3BSREDP INFO [clj-scheduler.mq:47] - Received SAVE_PROJECT ZMQ message, dispatching to project channel: {:ProjectID 36}
25-09-11 11:38:38:967 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 11:38:38:968 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-a5112cfc-f9ee-4232-86e0-ff1e81e9df45","SubTaskID":"SubTaskEvalScript-c9b5b399-c9dd-439e-b815-683c9bdfc8b4","MsgBody":{}}
25-09-11 11:38:38:975 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 11:38:38:975 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-a5112cfc-f9ee-4232-86e0-ff1e81e9df45","SubTaskID":"end-node-action","MsgBody":{}}
25-09-11 11:38:38:976 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_36-a5112cfc-f9ee-4232-86e0-ff1e81e9df45  Stop: {:parent {:Type "process-action", :ClassName "project_36", :ProcessID "project_36-fbc2d565-2a68-473a-a2b5-7e932c5ae3a7", :SubTaskID "onlyAction-04d036d1-723f-47e0-a8b9-5fd50c3aa312", :MsgBody {:Cmd "start", :InstCode "sample_14785d372", :ActionID "a5112cfc-f9ee-4232-86e0-ff1e81e9df45"}}}
25-09-11 11:38:39:035 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-11 11:38:39:039 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 11:38:39:040 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-fbc2d565-2a68-473a-a2b5-7e932c5ae3a7","SubTaskID":"onlyAction-04d036d1-723f-47e0-a8b9-5fd50c3aa312","MsgBody":{}}
25-09-11 11:38:39:045 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 11:38:39:046 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-fbc2d565-2a68-473a-a2b5-7e932c5ae3a7","SubTaskID":"end-node-action","MsgBody":{}}
25-09-11 11:38:39:047 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_36-fbc2d565-2a68-473a-a2b5-7e932c5ae3a7  Stop: {:parent nil}
25-09-11 11:38:39:101 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-11 11:38:42:374 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_36-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16 初始化
25-09-11 11:38:42:516 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 11:38:42:516 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"start-node-action","MsgBody":{}}
25-09-11 11:38:42:518 DESKTOP-3BSREDP INFO [clj-scheduler.script:9] - 执行脚本:int flag = 0;

int deviceId = 0;
int ret = Model.station.Ccss_Connected(deviceId);
if (ret == 0) {
  /* 已连接 */ 
  flag++;
} else { 
  /* 未连接 */ 
  Model.GetVarByName<TextInputVar>("input_yzlwcsts").Value = "平面应变断裂韧度试验 预制疲劳裂纹检查操作试验运行"+Model.CurrentInst.Name+"异常信息：控制器连接失败，请重新连接控制器。";
  return false;
}
int ret1 = Model.station.Ccss_DriveReady(deviceId);
if (ret1 == 0) { 
  /* 准备就绪 */ 
  flag++;
} else {
  /* 未就绪 */ 
  Model.GetVarByName<TextInputVar>("input_yzlwcsts").Value = "控制器未在启动状态";
  return false;
}
if(flag == 2){
  Model.GetVarByName<TextInputVar>("input_yzlwcsts").Value = "请确认是否使用"+Model.CurrentInst.Name+"进行平面应变断裂韧度试验 预制疲劳裂纹试验？";
  return true;
}
return false;
25-09-11 11:38:42:548 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 11:38:42:549 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: \u0022\\u8BF7\\u786E\\u8BA4\\u662F\\u5426\\u4F7F\\u7528\\u8BD5\\u6837147\\u8FDB\\u884C\\u5E73\\u9762\\u5E94\\u53D8\\u65AD\\u88C2\\u97E7\\u5EA6\\u8BD5\\u9A8C \\u9884\\u5236\\u75B2\\u52B3\\u88C2\\u7EB9\\u8BD5\\u9A8C\\uFF1F\u0022,\r\n  \u0022Code\u0022: \u0022input_yzlwcsts\u0022,\r\n  \u0022Type\u0022: \u0022Text\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 11:38:42:554 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - RUN_SCRIPT_RSP
25-09-11 11:38:42:555 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"project_36","ScriptId":"09e01187-015f-4410-9290-ad3c46890efd","Result":true}
25-09-11 11:38:43:773 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 11:38:43:775 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_three\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 11:38:43:778 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 11:38:43:778 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubtaskDialogBox-60715637-4d10-4965-aff6-da8166a38c78","MsgBody":{}}
25-09-11 11:38:43:779 DESKTOP-3BSREDP INFO [clj-scheduler.script:9] - 执行脚本:return Model.GetVarByName<BooleanInputVar>("input_three").Value;
25-09-11 11:38:43:800 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - RUN_SCRIPT_RSP
25-09-11 11:38:43:800 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"project_36","ScriptId":"8b099bc7-97fa-4b66-9dd9-32a60656f739","Result":true}
25-09-11 11:38:43:829 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 11:38:43:830 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_yzlwbs\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 11:38:43:834 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 11:38:43:835 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubTaskEvalScript-d676b27c-cb55-4186-b751-0d80789aef79","MsgBody":{}}
25-09-11 11:38:43:836 DESKTOP-3BSREDP INFO [clj-scheduler.script:9] - 执行脚本:var flag = Model.GetVarByName<BooleanInputVar>("input_yzlwcxksbs").Value;
//是否为重新开始
return flag;
25-09-11 11:38:43:859 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - RUN_SCRIPT_RSP
25-09-11 11:38:43:860 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"project_36","ScriptId":"d18d618d-2ba0-4434-9d24-fc07fadc1090","Result":true}
25-09-11 11:38:43:885 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 11:38:43:885 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubTaskEvalScript-9567e092-27d1-4397-87eb-144b0531a173","MsgBody":{}}
25-09-11 11:38:44:006 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 11:38:44:007 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 0,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 0,\r\n  \u0022Code\u0022: \u0022input_dqzszqs\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 11:38:44:011 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 11:38:44:012 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 0,\r\n  \u0022Dimension\u0022: \u0022dimension_cycle\u0022,\r\n  \u0022Unit\u0022: \u0022unit_cdw178e4a3\u0022,\r\n  \u0022DisplayUnitName\u0022: \u0022cycle\u0022,\r\n  \u0022DisplayValue\u0022: 0,\r\n  \u0022Code\u0022: \u0022input_starting_cycle\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 11:38:44:016 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 11:38:44:017 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubTaskEvalScript-f3c6e835-6291-45be-bd63-350f12f45f9a","MsgBody":{}}
25-09-11 11:38:44:041 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 11:38:44:043 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubTaskEvalScript-304442c2-62c7-4f92-bca4-5425989dd1cd","MsgBody":{}}
25-09-11 11:38:44:074 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 11:38:44:075 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubTaskEvalScript-7d35a2c8-7abb-4dbb-a88d-93a7760edc8b","MsgBody":{}}
25-09-11 11:38:44:096 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 11:38:44:097 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubTaskEvalScript-0d487418-fdc3-4d88-b707-10257acb369d","MsgBody":{}}
25-09-11 11:38:44:619 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 11:38:44:621 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubTaskTimer-878d0189-0bb6-4c27-8cb5-42eecfa315b6","MsgBody":{}}
25-09-11 11:38:44:622 DESKTOP-3BSREDP INFO [clj-scheduler.script:9] - 执行脚本:
//终止kmax
double abort_kmax = Model.GetVarByName<NumberInputVar>("input_abort_kmax").Value;
//初始kmax
double start_kmax = Model.GetVarByName<NumberInputVar>("input_start_kmax").Value;
//力值比
double force_ratio = Model.GetVarByName<NumberInputVar>("input_force_ratio").Value;
//试验频率
double prefabricated_crack_frequency =  Model.GetVarByName<NumberInputVar>("input_prefabricated_crack_frequency").Value;
//预制循环周次
double prefabrication_cycle = Model.GetVarByName<NumberInputVar>("input_prefabrication_cycle").Value;
//起始循环周次
double starting_cycle = Model.GetVarByName<NumberInputVar>("input_starting_cycle").Value;
//到达均值速度
double average_speed_arrival = Model.GetVarByName<NumberInputVar>("input_average_speed_arrival").Value;
//预制裂纹长度
double precast_crack_length = Model.GetVarByName<NumberInputVar>("input_precast_crack_length").Value;
//线性拟合数据上限
double upper_limit_of_linear_fitting_data = Model.GetVarByName<NumberInputVar>("input_upper_limit_of_linear_fitting_data").Value;
//线性拟合数据下限
double lower_bound_of_linear_fitting_data = Model.GetVarByName<NumberInputVar>("input_lower_bound_of_linear_fitting_data").Value;
//未萌生裂纹循环
double undeveloped_crack_cycle = Model.GetVarByName<NumberInputVar>("input_undeveloped_crack_cycle").Value;
//Kmax增幅比
double increase_ratio_kmax = Model.GetVarByName<NumberInputVar>("input_increase_ratio_kmax").Value;

//获取下位机量程
double? NominalValue = 0.0; //标称值
double? MaxValue = 0.0; //最大值
double? MinValue = 0.0; //最小值
double? UpperSoftLimit = 0.0; //软件上限位值
double? LowerSoftLimit = 0.0; //软件下限位值
int? SoftLimitReaction = 0;  //软件限位响应类型
double? BasicTare = 0.0;  //基础长时清零
double? Tare = 0.0;   //内存清零
double? McFilterTime = 0.0;  // 显示滤波
double? CtrlFilterTime = 0.0;   //控制滤波
//传入轴号、传感器号
int? ret = Model.station.Ccss_ReadSensorPara(0, 1, ref NominalValue, ref MaxValue, ref MinValue, ref UpperSoftLimit,
ref LowerSoftLimit, ref SoftLimitReaction, ref BasicTare, ref Tare, ref McFilterTime, ref CtrlFilterTime);
//打印量程
FuncLibs.Logger.Error("传感器信息：");
FuncLibs.Logger.Error($"ret : {ret}, NominalValue: {NominalValue}, MaxValue: {MaxValue}, MinValue: {MinValue}, UpperSoftLimit : {UpperSoftLimit}, LowerSoftLimit: {LowerSoftLimit}, SoftLimitReaction: {SoftLimitReaction}, BasicTare: {BasicTare},Tare: {Tare}, McFilterTime: {McFilterTime}, CtrlFilterTime: {CtrlFilterTime}");

// 计算周期波所用的参数
//峰谷值
double forceMax = 0;
double forceMin = 0;
TestExpert.PlaneStrainFractureToughnessMethod.Commons.PlaneStrainFractureToughnessMethod
.GetForce( start_kmax,  precast_crack_length, out forceMax);
forceMin = forceMax * force_ratio;


Console.WriteLine($"预制计算出的最大力:{forceMax} ");

if(forceMax<MaxValue && forceMax>MinValue && forceMin<MaxValue && forceMin>MinValue && forceMax>forceMin){
  // 赋值峰谷值变量
  Model.GetVarByName<NumberInputVar>("input_yzlw_valley").Value = forceMin;
  Model.GetVarByName<NumberInputVar>("input_yzlw_peak").Value = forceMax;
}else{
  Model.GetVarByName<NumberInputVar>("input_yzlw_valley").Value = 0;
  Model.GetVarByName<NumberInputVar>("input_yzlw_peak").Value = 0;
  return false;
}


Console.WriteLine($"预制裂纹峰值:{Model.GetVarByName<NumberInputVar>("input_yzlw_peak").Value} ");
Console.WriteLine($"预制裂纹谷值:{Model.GetVarByName<NumberInputVar>("input_yzlw_valley").Value} ");

//赋值循环次数
Model.GetVarByName<NumberInputVar>("input_yzlwxhcs").Value = prefabrication_cycle - starting_cycle;

Model.iVariable["moniliewen"]=0;
Model.dVariableArray["tenLength"] = new double[10];

return true;
25-09-11 11:38:44:651 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 11:38:44:653 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 1.568002635231078,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 1.568002635231078,\r\n  \u0022Code\u0022: \u0022input_yzlw_valley\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 11:38:44:657 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 11:38:44:657 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 15.68002635231078,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 15.68002635231078,\r\n  \u0022Code\u0022: \u0022input_yzlw_peak\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 11:38:44:661 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 11:38:44:661 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 1000000,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 1000000,\r\n  \u0022Code\u0022: \u0022input_yzlwxhcs\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 11:38:44:665 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - RUN_SCRIPT_RSP
25-09-11 11:38:44:667 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"project_36","ScriptId":"d68da690-14b1-4aef-98f9-f652d1f1c261","Result":true}
25-09-11 11:38:44:782 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 11:38:44:783 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_one\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 11:38:48:739 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 11:38:48:740 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 15.11165088785825,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 15.11165088785825,\r\n  \u0022Code\u0022: \u0022input_allKmax\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 11:38:49:635 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 11:38:49:637 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 19.287011527464525,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 19.287011527464525,\r\n  \u0022Code\u0022: \u0022input_allKmax\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 11:38:50:155 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 11:38:50:156 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 24.97584148635491,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 24.97584148635491,\r\n  \u0022Code\u0022: \u0022input_allKmax\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 11:38:50:918 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 11:38:50:919 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 33.891597436808524,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 33.891597436808524,\r\n  \u0022Code\u0022: \u0022input_allKmax\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 11:38:51:610 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 11:38:51:611 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 49.97722700836602,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 49.97722700836602,\r\n  \u0022Code\u0022: \u0022input_allKmax\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 11:38:52:296 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 11:38:52:297 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 84.46280537717269,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 84.46280537717269,\r\n  \u0022Code\u0022: \u0022input_allKmax\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 11:38:53:052 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 11:38:53:053 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 184.24547543994308,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 184.24547543994308,\r\n  \u0022Code\u0022: \u0022input_allKmax\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 11:38:53:750 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 11:38:53:750 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 870.1947192045508,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 870.1947192045508,\r\n  \u0022Code\u0022: \u0022input_allKmax\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 11:39:13:488 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_36-fbc2d565-2a68-473a-a2b5-7e932c5ae3a7 初始化
25-09-11 11:39:13:543 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 11:39:13:544 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-fbc2d565-2a68-473a-a2b5-7e932c5ae3a7","SubTaskID":"start-node-action","MsgBody":{}}
25-09-11 11:39:13:551 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 11:39:13:552 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"process-action","ClassName":"project_36","ProcessID":"project_36-fbc2d565-2a68-473a-a2b5-7e932c5ae3a7","SubTaskID":"onlyAction-eafe88f9-58ec-4721-aeb9-248ff2ff9a3e","MsgBody":{"Cmd":"abort","InstCode":"sample_14785d372","ActionID":"ab56adb1-93c8-4ca0-a353-39b5c7a3ed16"}}
25-09-11 11:39:13:553 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-11 11:39:13:553 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_36-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16  Stop: {:parent nil}
25-09-11 11:39:13:616 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-11 11:39:13:616 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 11:39:13:617 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-fbc2d565-2a68-473a-a2b5-7e932c5ae3a7","SubTaskID":"onlyAction-eafe88f9-58ec-4721-aeb9-248ff2ff9a3e","MsgBody":{}}
25-09-11 11:39:13:617 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 11:39:13:618 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"peakGatherData-96560ef5-387c-45b7-850a-b53d5e44f2f6","MsgBody":{}}
25-09-11 11:39:13:618 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 11:39:13:619 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"arrayDataHandler-3c66b213-0124-415a-ab07-5063a2b97bcc","MsgBody":{}}
25-09-11 11:39:13:619 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 11:39:13:620 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"hightGatherData-6b550731-994e-4162-8a77-5a5c97e872d7","MsgBody":{}}
25-09-11 11:39:13:620 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 11:39:13:620 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"daqCycle-55422a18-1e18-4163-96d7-92687306d8e7","MsgBody":{}}
25-09-11 11:39:13:761 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 11:39:13:761 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 373,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 373,\r\n  \u0022Code\u0022: \u0022input_dqzszqs\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 11:39:13:768 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 11:39:13:769 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 373,\r\n  \u0022Dimension\u0022: \u0022dimension_cycle\u0022,\r\n  \u0022Unit\u0022: \u0022unit_cdw178e4a3\u0022,\r\n  \u0022DisplayUnitName\u0022: \u0022cycle\u0022,\r\n  \u0022DisplayValue\u0022: 373,\r\n  \u0022Code\u0022: \u0022input_starting_cycle\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 11:39:13:773 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 11:39:13:773 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 371,\r\n  \u0022Dimension\u0022: \u0022dimension_cycle\u0022,\r\n  \u0022Unit\u0022: \u0022unit_cdw178e4a3\u0022,\r\n  \u0022DisplayUnitName\u0022: \u0022cycle\u0022,\r\n  \u0022DisplayValue\u0022: 371,\r\n  \u0022Code\u0022: \u0022input_starting_cycle\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 11:39:13:778 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 11:39:13:779 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-fbc2d565-2a68-473a-a2b5-7e932c5ae3a7","SubTaskID":"SubTaskTimer-e8fc4f71-e7b1-40a4-9dec-85498fcdec3b","MsgBody":{}}
25-09-11 11:39:13:801 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 11:39:13:802 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 371,\r\n  \u0022Dimension\u0022: \u0022dimension_cycle\u0022,\r\n  \u0022Unit\u0022: \u0022unit_cdw178e4a3\u0022,\r\n  \u0022DisplayUnitName\u0022: \u0022cycle\u0022,\r\n  \u0022DisplayValue\u0022: 371,\r\n  \u0022Code\u0022: \u0022input_starting_cycle\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 11:39:13:806 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 11:39:13:807 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_yzlwcxksbs\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: true,\r\n  \u0022IsCheck\u0022: true,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 11:39:13:811 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 11:39:13:811 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: \u0022RESTART\u0022,\r\n  \u0022Code\u0022: \u0022input_yzlwhcq\u0022,\r\n  \u0022Type\u0022: \u0022Select\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 11:39:13:814 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 11:39:13:815 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: false,\r\n  \u0022Code\u0022: \u0022input_yzlwbs\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 11:39:13:823 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 11:39:13:825 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 0,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 0,\r\n  \u0022Code\u0022: \u0022input_kf\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 11:39:13:835 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 11:39:13:836 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 870.1947192045508,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 870.1947192045508,\r\n  \u0022Code\u0022: \u0022input_allKmax\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 11:39:13:846 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 11:39:13:847 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 0,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 0,\r\n  \u0022Code\u0022: \u0022input_kf\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 11:39:13:856 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 11:39:13:857 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 870.1947192045508,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 870.1947192045508,\r\n  \u0022Code\u0022: \u0022input_zzzdylqdyz\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 11:39:13:865 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 11:39:13:865 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 15.680025100708008,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 15.680025100708008,\r\n  \u0022Code\u0022: \u0022input_zzzdl\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 11:39:13:873 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 11:39:13:874 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 47.28572965796207,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 47.28572965796207,\r\n  \u0022Code\u0022: \u0022input_yzlwjglwcd\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 11:39:13:883 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 11:39:13:884 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-fbc2d565-2a68-473a-a2b5-7e932c5ae3a7","SubTaskID":"SubTaskEvalScript-4d9f4ca2-4006-412b-a5ff-cfcdfc18f23b","MsgBody":{}}
25-09-11 11:39:13:907 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 11:39:13:908 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-fbc2d565-2a68-473a-a2b5-7e932c5ae3a7","SubTaskID":"SubTaskEvalScript-57665592-8645-4b41-820a-b3eb30770c0c","MsgBody":{}}
25-09-11 11:39:13:931 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 11:39:13:932 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_yzlwcxksbs\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: true,\r\n  \u0022IsCheck\u0022: true,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 11:39:13:936 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 11:39:13:936 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: \u0022RESTART\u0022,\r\n  \u0022Code\u0022: \u0022input_yzlwhcq\u0022,\r\n  \u0022Type\u0022: \u0022Select\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 11:39:13:940 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 11:39:13:941 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: false,\r\n  \u0022Code\u0022: \u0022input_yzlwbs\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 11:39:13:945 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 11:39:13:946 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 12.857620525360108,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 12.857620525360108,\r\n  \u0022Code\u0022: \u0022input_yzlw_fzx1\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 11:39:13:950 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 11:39:13:950 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 4.390406799316406,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 4.390406799316406,\r\n  \u0022Code\u0022: \u0022input_yzlw_fzx2\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 11:39:13:954 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 11:39:13:954 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 2.568002223968506,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 2.568002223968506,\r\n  \u0022Code\u0022: \u0022input_yzlw_fzx_qd\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 11:39:13:958 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 11:39:13:959 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 16.680025100708008,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 16.680025100708008,\r\n  \u0022Code\u0022: \u0022input_yzlw_fzx_zd\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 11:39:13:962 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 11:39:13:962 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-fbc2d565-2a68-473a-a2b5-7e932c5ae3a7","SubTaskID":"SubTaskEvalScript-cc8952a1-430c-45c2-bb47-4082987dda7a","MsgBody":{}}
25-09-11 11:39:13:969 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 11:39:13:970 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"process-action","ClassName":"project_36","ProcessID":"project_36-fbc2d565-2a68-473a-a2b5-7e932c5ae3a7","SubTaskID":"onlyAction-04d036d1-723f-47e0-a8b9-5fd50c3aa312","MsgBody":{"Cmd":"start","InstCode":"sample_14785d372","ActionID":"a5112cfc-f9ee-4232-86e0-ff1e81e9df45"}}
25-09-11 11:39:13:973 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_36-a5112cfc-f9ee-4232-86e0-ff1e81e9df45 初始化
25-09-11 11:39:14:031 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 11:39:14:032 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-a5112cfc-f9ee-4232-86e0-ff1e81e9df45","SubTaskID":"start-node-action","MsgBody":{}}
25-09-11 11:39:14:061 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SAVE_PROJECT
25-09-11 11:39:14:062 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProjectID":36}
25-09-11 11:39:14:062 DESKTOP-3BSREDP INFO [clj-scheduler.mq:47] - Received SAVE_PROJECT ZMQ message, dispatching to project channel: {:ProjectID 36}
25-09-11 11:39:14:063 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 11:39:14:063 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-a5112cfc-f9ee-4232-86e0-ff1e81e9df45","SubTaskID":"SubTaskEvalScript-c9b5b399-c9dd-439e-b815-683c9bdfc8b4","MsgBody":{}}
25-09-11 11:39:14:070 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 11:39:14:070 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-a5112cfc-f9ee-4232-86e0-ff1e81e9df45","SubTaskID":"end-node-action","MsgBody":{}}
25-09-11 11:39:14:071 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_36-a5112cfc-f9ee-4232-86e0-ff1e81e9df45  Stop: {:parent {:Type "process-action", :ClassName "project_36", :ProcessID "project_36-fbc2d565-2a68-473a-a2b5-7e932c5ae3a7", :SubTaskID "onlyAction-04d036d1-723f-47e0-a8b9-5fd50c3aa312", :MsgBody {:Cmd "start", :InstCode "sample_14785d372", :ActionID "a5112cfc-f9ee-4232-86e0-ff1e81e9df45"}}}
25-09-11 11:39:14:125 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-11 11:39:14:130 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 11:39:14:131 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-fbc2d565-2a68-473a-a2b5-7e932c5ae3a7","SubTaskID":"onlyAction-04d036d1-723f-47e0-a8b9-5fd50c3aa312","MsgBody":{}}
25-09-11 11:39:14:136 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 11:39:14:137 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-fbc2d565-2a68-473a-a2b5-7e932c5ae3a7","SubTaskID":"end-node-action","MsgBody":{}}
25-09-11 11:39:14:137 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_36-fbc2d565-2a68-473a-a2b5-7e932c5ae3a7  Stop: {:parent nil}
25-09-11 11:39:14:204 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-11 13:47:57:995 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_36-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16 初始化
25-09-11 13:47:58:091 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 13:47:58:092 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"start-node-action","MsgBody":{}}
25-09-11 13:47:58:093 DESKTOP-3BSREDP INFO [clj-scheduler.script:9] - 执行脚本:int flag = 0;

int deviceId = 0;
int ret = Model.station.Ccss_Connected(deviceId);
if (ret == 0) {
  /* 已连接 */ 
  flag++;
} else { 
  /* 未连接 */ 
  Model.GetVarByName<TextInputVar>("input_yzlwcsts").Value = "平面应变断裂韧度试验 预制疲劳裂纹检查操作试验运行"+Model.CurrentInst.Name+"异常信息：控制器连接失败，请重新连接控制器。";
  return false;
}
int ret1 = Model.station.Ccss_DriveReady(deviceId);
if (ret1 == 0) { 
  /* 准备就绪 */ 
  flag++;
} else {
  /* 未就绪 */ 
  Model.GetVarByName<TextInputVar>("input_yzlwcsts").Value = "控制器未在启动状态";
  return false;
}
if(flag == 2){
  Model.GetVarByName<TextInputVar>("input_yzlwcsts").Value = "请确认是否使用"+Model.CurrentInst.Name+"进行平面应变断裂韧度试验 预制疲劳裂纹试验？";
  return true;
}
return false;
25-09-11 13:47:58:125 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 13:47:58:126 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: \u0022\\u8BF7\\u786E\\u8BA4\\u662F\\u5426\\u4F7F\\u7528\\u8BD5\\u6837147\\u8FDB\\u884C\\u5E73\\u9762\\u5E94\\u53D8\\u65AD\\u88C2\\u97E7\\u5EA6\\u8BD5\\u9A8C \\u9884\\u5236\\u75B2\\u52B3\\u88C2\\u7EB9\\u8BD5\\u9A8C\\uFF1F\u0022,\r\n  \u0022Code\u0022: \u0022input_yzlwcsts\u0022,\r\n  \u0022Type\u0022: \u0022Text\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 13:47:58:132 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - RUN_SCRIPT_RSP
25-09-11 13:47:58:132 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"project_36","ScriptId":"79ea0179-6082-40d2-9b97-7a49c55ecb13","Result":true}
25-09-11 13:48:00:389 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 13:48:00:390 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_three\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 13:48:00:395 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 13:48:00:396 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubtaskDialogBox-60715637-4d10-4965-aff6-da8166a38c78","MsgBody":{}}
25-09-11 13:48:00:399 DESKTOP-3BSREDP INFO [clj-scheduler.script:9] - 执行脚本:return Model.GetVarByName<BooleanInputVar>("input_three").Value;
25-09-11 13:48:00:417 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - RUN_SCRIPT_RSP
25-09-11 13:48:00:418 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"project_36","ScriptId":"c54a80bb-ac0d-4944-a115-5a8d5a8f1e55","Result":true}
25-09-11 13:48:00:438 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 13:48:00:439 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_yzlwbs\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 13:48:00:444 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 13:48:00:444 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubTaskEvalScript-d676b27c-cb55-4186-b751-0d80789aef79","MsgBody":{}}
25-09-11 13:48:00:445 DESKTOP-3BSREDP INFO [clj-scheduler.script:9] - 执行脚本:var flag = Model.GetVarByName<BooleanInputVar>("input_yzlwcxksbs").Value;
//是否为重新开始
return flag;
25-09-11 13:48:00:466 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - RUN_SCRIPT_RSP
25-09-11 13:48:00:467 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"project_36","ScriptId":"4a89c809-5e7a-4663-bccb-f398d67e7388","Result":true}
25-09-11 13:48:00:506 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 13:48:00:506 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubTaskEvalScript-9567e092-27d1-4397-87eb-144b0531a173","MsgBody":{}}
25-09-11 13:48:00:583 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 13:48:00:584 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 0,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 0,\r\n  \u0022Code\u0022: \u0022input_dqzszqs\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 13:48:00:590 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 13:48:00:590 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 0,\r\n  \u0022Dimension\u0022: \u0022dimension_cycle\u0022,\r\n  \u0022Unit\u0022: \u0022unit_cdw178e4a3\u0022,\r\n  \u0022DisplayUnitName\u0022: \u0022cycle\u0022,\r\n  \u0022DisplayValue\u0022: 0,\r\n  \u0022Code\u0022: \u0022input_starting_cycle\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 13:48:00:594 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 13:48:00:595 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubTaskEvalScript-f3c6e835-6291-45be-bd63-350f12f45f9a","MsgBody":{}}
25-09-11 13:48:00:631 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 13:48:00:632 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubTaskEvalScript-304442c2-62c7-4f92-bca4-5425989dd1cd","MsgBody":{}}
25-09-11 13:48:00:667 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 13:48:00:667 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubTaskEvalScript-7d35a2c8-7abb-4dbb-a88d-93a7760edc8b","MsgBody":{}}
25-09-11 13:48:00:706 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 13:48:00:707 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubTaskEvalScript-0d487418-fdc3-4d88-b707-10257acb369d","MsgBody":{}}
25-09-11 13:48:01:223 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 13:48:01:224 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubTaskTimer-878d0189-0bb6-4c27-8cb5-42eecfa315b6","MsgBody":{}}
25-09-11 13:48:01:225 DESKTOP-3BSREDP INFO [clj-scheduler.script:9] - 执行脚本:
//终止kmax
double abort_kmax = Model.GetVarByName<NumberInputVar>("input_abort_kmax").Value;
//初始kmax
double start_kmax = Model.GetVarByName<NumberInputVar>("input_start_kmax").Value;
//力值比
double force_ratio = Model.GetVarByName<NumberInputVar>("input_force_ratio").Value;
//试验频率
double prefabricated_crack_frequency =  Model.GetVarByName<NumberInputVar>("input_prefabricated_crack_frequency").Value;
//预制循环周次
double prefabrication_cycle = Model.GetVarByName<NumberInputVar>("input_prefabrication_cycle").Value;
//起始循环周次
double starting_cycle = Model.GetVarByName<NumberInputVar>("input_starting_cycle").Value;
//到达均值速度
double average_speed_arrival = Model.GetVarByName<NumberInputVar>("input_average_speed_arrival").Value;
//预制裂纹长度
double precast_crack_length = Model.GetVarByName<NumberInputVar>("input_precast_crack_length").Value;
//线性拟合数据上限
double upper_limit_of_linear_fitting_data = Model.GetVarByName<NumberInputVar>("input_upper_limit_of_linear_fitting_data").Value;
//线性拟合数据下限
double lower_bound_of_linear_fitting_data = Model.GetVarByName<NumberInputVar>("input_lower_bound_of_linear_fitting_data").Value;
//未萌生裂纹循环
double undeveloped_crack_cycle = Model.GetVarByName<NumberInputVar>("input_undeveloped_crack_cycle").Value;
//Kmax增幅比
double increase_ratio_kmax = Model.GetVarByName<NumberInputVar>("input_increase_ratio_kmax").Value;

//获取下位机量程
double? NominalValue = 0.0; //标称值
double? MaxValue = 0.0; //最大值
double? MinValue = 0.0; //最小值
double? UpperSoftLimit = 0.0; //软件上限位值
double? LowerSoftLimit = 0.0; //软件下限位值
int? SoftLimitReaction = 0;  //软件限位响应类型
double? BasicTare = 0.0;  //基础长时清零
double? Tare = 0.0;   //内存清零
double? McFilterTime = 0.0;  // 显示滤波
double? CtrlFilterTime = 0.0;   //控制滤波
//传入轴号、传感器号
int? ret = Model.station.Ccss_ReadSensorPara(0, 1, ref NominalValue, ref MaxValue, ref MinValue, ref UpperSoftLimit,
ref LowerSoftLimit, ref SoftLimitReaction, ref BasicTare, ref Tare, ref McFilterTime, ref CtrlFilterTime);
//打印量程
FuncLibs.Logger.Error("传感器信息：");
FuncLibs.Logger.Error($"ret : {ret}, NominalValue: {NominalValue}, MaxValue: {MaxValue}, MinValue: {MinValue}, UpperSoftLimit : {UpperSoftLimit}, LowerSoftLimit: {LowerSoftLimit}, SoftLimitReaction: {SoftLimitReaction}, BasicTare: {BasicTare},Tare: {Tare}, McFilterTime: {McFilterTime}, CtrlFilterTime: {CtrlFilterTime}");

// 计算周期波所用的参数
//峰谷值
double forceMax = 0;
double forceMin = 0;
TestExpert.PlaneStrainFractureToughnessMethod.Commons.PlaneStrainFractureToughnessMethod
.GetForce( start_kmax,  precast_crack_length, out forceMax);
forceMin = forceMax * force_ratio;


Console.WriteLine($"预制计算出的最大力:{forceMax} ");

if(forceMax<MaxValue && forceMax>MinValue && forceMin<MaxValue && forceMin>MinValue && forceMax>forceMin){
  // 赋值峰谷值变量
  Model.GetVarByName<NumberInputVar>("input_yzlw_valley").Value = forceMin;
  Model.GetVarByName<NumberInputVar>("input_yzlw_peak").Value = forceMax;
}else{
  Model.GetVarByName<NumberInputVar>("input_yzlw_valley").Value = 0;
  Model.GetVarByName<NumberInputVar>("input_yzlw_peak").Value = 0;
  return false;
}


Console.WriteLine($"预制裂纹峰值:{Model.GetVarByName<NumberInputVar>("input_yzlw_peak").Value} ");
Console.WriteLine($"预制裂纹谷值:{Model.GetVarByName<NumberInputVar>("input_yzlw_valley").Value} ");

//赋值循环次数
Model.GetVarByName<NumberInputVar>("input_yzlwxhcs").Value = prefabrication_cycle - starting_cycle;

Model.iVariable["moniliewen"]=0;
Model.dVariableArray["tenLength"] = new double[10];

return true;
25-09-11 13:48:01:247 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 13:48:01:249 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 1.568002635231078,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 1.568002635231078,\r\n  \u0022Code\u0022: \u0022input_yzlw_valley\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 13:48:01:253 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 13:48:01:253 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 15.68002635231078,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 15.68002635231078,\r\n  \u0022Code\u0022: \u0022input_yzlw_peak\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 13:48:01:259 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 13:48:01:259 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 1000000,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 1000000,\r\n  \u0022Code\u0022: \u0022input_yzlwxhcs\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 13:48:01:263 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - RUN_SCRIPT_RSP
25-09-11 13:48:01:263 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"project_36","ScriptId":"0007fde1-f269-4b2c-9798-a99371b238fb","Result":true}
25-09-11 13:48:01:273 DESKTOP-3BSREDP INFO [clj-scheduler.script:9] - 执行脚本:return true;
25-09-11 13:48:01:433 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - RUN_SCRIPT_RSP
25-09-11 13:48:01:434 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"project_36","ScriptId":"5e429857-9abd-48c4-b92e-4ff7d938081c","Result":true}
25-09-11 13:48:01:458 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 13:48:01:459 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_one\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 13:48:05:480 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 13:48:05:481 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 15.11165088785825,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 15.11165088785825,\r\n  \u0022Code\u0022: \u0022input_allKmax\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 13:48:06:374 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 13:48:06:375 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 19.287011527464525,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 19.287011527464525,\r\n  \u0022Code\u0022: \u0022input_allKmax\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 13:48:07:122 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 13:48:07:122 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 24.97584148635491,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 24.97584148635491,\r\n  \u0022Code\u0022: \u0022input_allKmax\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 13:48:07:649 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 13:48:07:649 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 33.891597436808524,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 33.891597436808524,\r\n  \u0022Code\u0022: \u0022input_allKmax\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 13:48:08:530 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 13:48:08:532 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 49.97722700836602,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 49.97722700836602,\r\n  \u0022Code\u0022: \u0022input_allKmax\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 13:48:09:318 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 13:48:09:319 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 84.46280537717269,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 84.46280537717269,\r\n  \u0022Code\u0022: \u0022input_allKmax\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 13:48:09:875 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 13:48:09:875 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 184.24547543994308,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 184.24547543994308,\r\n  \u0022Code\u0022: \u0022input_allKmax\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 13:48:10:609 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 13:48:10:610 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 870.1947192045508,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 870.1947192045508,\r\n  \u0022Code\u0022: \u0022input_allKmax\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:23:40:963 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_36-fbc2d565-2a68-473a-a2b5-7e932c5ae3a7 初始化
25-09-11 14:23:41:065 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 14:23:41:066 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-fbc2d565-2a68-473a-a2b5-7e932c5ae3a7","SubTaskID":"start-node-action","MsgBody":{}}
25-09-11 14:23:41:082 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 14:23:41:083 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"process-action","ClassName":"project_36","ProcessID":"project_36-fbc2d565-2a68-473a-a2b5-7e932c5ae3a7","SubTaskID":"onlyAction-eafe88f9-58ec-4721-aeb9-248ff2ff9a3e","MsgBody":{"Cmd":"abort","InstCode":"sample_14785d372","ActionID":"ab56adb1-93c8-4ca0-a353-39b5c7a3ed16"}}
25-09-11 14:23:41:086 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-11 14:23:41:086 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_36-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16  Stop: {:parent nil}
25-09-11 14:23:41:399 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-11 14:23:41:401 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 14:23:41:401 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-fbc2d565-2a68-473a-a2b5-7e932c5ae3a7","SubTaskID":"onlyAction-eafe88f9-58ec-4721-aeb9-248ff2ff9a3e","MsgBody":{}}
25-09-11 14:23:41:402 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 14:23:41:402 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"peakGatherData-96560ef5-387c-45b7-850a-b53d5e44f2f6","MsgBody":{}}
25-09-11 14:23:41:403 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 14:23:41:404 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"arrayDataHandler-3c66b213-0124-415a-ab07-5063a2b97bcc","MsgBody":{}}
25-09-11 14:23:41:404 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 14:23:41:405 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"hightGatherData-2d77f01c-f5da-465e-896c-a8d322f7687c","MsgBody":{}}
25-09-11 14:23:41:406 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 14:23:41:408 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"daqCycle-55422a18-1e18-4163-96d7-92687306d8e7","MsgBody":{}}
25-09-11 14:23:41:465 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:23:41:466 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 29456,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 29456,\r\n  \u0022Code\u0022: \u0022input_dqzszqs\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:23:41:473 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:23:41:473 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 29456,\r\n  \u0022Dimension\u0022: \u0022dimension_cycle\u0022,\r\n  \u0022Unit\u0022: \u0022unit_cdw178e4a3\u0022,\r\n  \u0022DisplayUnitName\u0022: \u0022cycle\u0022,\r\n  \u0022DisplayValue\u0022: 29456,\r\n  \u0022Code\u0022: \u0022input_starting_cycle\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:23:41:545 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:23:41:546 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 29456,\r\n  \u0022Dimension\u0022: \u0022dimension_cycle\u0022,\r\n  \u0022Unit\u0022: \u0022unit_cdw178e4a3\u0022,\r\n  \u0022DisplayUnitName\u0022: \u0022cycle\u0022,\r\n  \u0022DisplayValue\u0022: 29456,\r\n  \u0022Code\u0022: \u0022input_starting_cycle\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:23:41:552 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 14:23:41:553 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-fbc2d565-2a68-473a-a2b5-7e932c5ae3a7","SubTaskID":"SubTaskTimer-e8fc4f71-e7b1-40a4-9dec-85498fcdec3b","MsgBody":{}}
25-09-11 14:23:41:574 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:23:41:576 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 29454,\r\n  \u0022Dimension\u0022: \u0022dimension_cycle\u0022,\r\n  \u0022Unit\u0022: \u0022unit_cdw178e4a3\u0022,\r\n  \u0022DisplayUnitName\u0022: \u0022cycle\u0022,\r\n  \u0022DisplayValue\u0022: 29454,\r\n  \u0022Code\u0022: \u0022input_starting_cycle\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:23:41:583 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:23:41:584 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_yzlwcxksbs\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: true,\r\n  \u0022IsCheck\u0022: true,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:23:41:590 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:23:41:590 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: \u0022RESTART\u0022,\r\n  \u0022Code\u0022: \u0022input_yzlwhcq\u0022,\r\n  \u0022Type\u0022: \u0022Select\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:23:41:596 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:23:41:597 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: false,\r\n  \u0022Code\u0022: \u0022input_yzlwbs\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:23:41:603 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:23:41:604 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 0,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 0,\r\n  \u0022Code\u0022: \u0022input_kf\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:23:41:616 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:23:41:620 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 870.1947192045508,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 870.1947192045508,\r\n  \u0022Code\u0022: \u0022input_allKmax\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:23:41:629 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:23:41:630 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 0,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 0,\r\n  \u0022Code\u0022: \u0022input_kf\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:23:41:639 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:23:41:640 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 870.1947192045508,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 870.1947192045508,\r\n  \u0022Code\u0022: \u0022input_zzzdylqdyz\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:23:41:652 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:23:41:653 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 15.680025100708008,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 15.680025100708008,\r\n  \u0022Code\u0022: \u0022input_zzzdl\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:23:41:662 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:23:41:664 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 47.28572965796207,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 47.28572965796207,\r\n  \u0022Code\u0022: \u0022input_yzlwjglwcd\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:23:41:673 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 14:23:41:674 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-fbc2d565-2a68-473a-a2b5-7e932c5ae3a7","SubTaskID":"SubTaskEvalScript-4d9f4ca2-4006-412b-a5ff-cfcdfc18f23b","MsgBody":{}}
25-09-11 14:23:41:694 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 14:23:41:695 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-fbc2d565-2a68-473a-a2b5-7e932c5ae3a7","SubTaskID":"SubTaskEvalScript-57665592-8645-4b41-820a-b3eb30770c0c","MsgBody":{}}
25-09-11 14:23:41:720 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:23:41:721 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_yzlwcxksbs\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: true,\r\n  \u0022IsCheck\u0022: true,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:23:41:726 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:23:41:726 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: \u0022RESTART\u0022,\r\n  \u0022Code\u0022: \u0022input_yzlwhcq\u0022,\r\n  \u0022Type\u0022: \u0022Select\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:23:41:729 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:23:41:730 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: false,\r\n  \u0022Code\u0022: \u0022input_yzlwbs\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:23:41:734 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:23:41:735 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 12.857620525360108,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 12.857620525360108,\r\n  \u0022Code\u0022: \u0022input_yzlw_fzx1\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:23:41:740 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:23:41:740 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 4.390406799316406,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 4.390406799316406,\r\n  \u0022Code\u0022: \u0022input_yzlw_fzx2\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:23:41:744 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:23:41:745 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 2.568002223968506,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 2.568002223968506,\r\n  \u0022Code\u0022: \u0022input_yzlw_fzx_qd\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:23:41:750 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:23:41:750 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 16.680025100708008,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 16.680025100708008,\r\n  \u0022Code\u0022: \u0022input_yzlw_fzx_zd\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:23:41:757 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 14:23:41:758 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-fbc2d565-2a68-473a-a2b5-7e932c5ae3a7","SubTaskID":"SubTaskEvalScript-cc8952a1-430c-45c2-bb47-4082987dda7a","MsgBody":{}}
25-09-11 14:23:41:762 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 14:23:41:763 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"process-action","ClassName":"project_36","ProcessID":"project_36-fbc2d565-2a68-473a-a2b5-7e932c5ae3a7","SubTaskID":"onlyAction-04d036d1-723f-47e0-a8b9-5fd50c3aa312","MsgBody":{"Cmd":"start","InstCode":"sample_14785d372","ActionID":"a5112cfc-f9ee-4232-86e0-ff1e81e9df45"}}
25-09-11 14:23:41:768 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_36-a5112cfc-f9ee-4232-86e0-ff1e81e9df45 初始化
25-09-11 14:23:41:827 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 14:23:41:828 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-a5112cfc-f9ee-4232-86e0-ff1e81e9df45","SubTaskID":"start-node-action","MsgBody":{}}
25-09-11 14:23:41:902 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SAVE_PROJECT
25-09-11 14:23:41:902 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProjectID":36}
25-09-11 14:23:41:903 DESKTOP-3BSREDP INFO [clj-scheduler.mq:47] - Received SAVE_PROJECT ZMQ message, dispatching to project channel: {:ProjectID 36}
25-09-11 14:23:41:903 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 14:23:41:904 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-a5112cfc-f9ee-4232-86e0-ff1e81e9df45","SubTaskID":"SubTaskEvalScript-c9b5b399-c9dd-439e-b815-683c9bdfc8b4","MsgBody":{}}
25-09-11 14:23:41:910 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 14:23:41:911 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-a5112cfc-f9ee-4232-86e0-ff1e81e9df45","SubTaskID":"end-node-action","MsgBody":{}}
25-09-11 14:23:41:912 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_36-a5112cfc-f9ee-4232-86e0-ff1e81e9df45  Stop: {:parent {:Type "process-action", :ClassName "project_36", :ProcessID "project_36-fbc2d565-2a68-473a-a2b5-7e932c5ae3a7", :SubTaskID "onlyAction-04d036d1-723f-47e0-a8b9-5fd50c3aa312", :MsgBody {:Cmd "start", :InstCode "sample_14785d372", :ActionID "a5112cfc-f9ee-4232-86e0-ff1e81e9df45"}}}
25-09-11 14:23:41:969 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-11 14:23:41:973 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 14:23:41:974 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-fbc2d565-2a68-473a-a2b5-7e932c5ae3a7","SubTaskID":"onlyAction-04d036d1-723f-47e0-a8b9-5fd50c3aa312","MsgBody":{}}
25-09-11 14:23:41:978 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 14:23:41:979 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-fbc2d565-2a68-473a-a2b5-7e932c5ae3a7","SubTaskID":"end-node-action","MsgBody":{}}
25-09-11 14:23:41:980 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_36-fbc2d565-2a68-473a-a2b5-7e932c5ae3a7  Stop: {:parent nil}
25-09-11 14:23:42:041 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-11 14:26:36:356 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_36-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16 初始化
25-09-11 14:26:36:402 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 14:26:36:403 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"start-node-action","MsgBody":{}}
25-09-11 14:26:36:404 DESKTOP-3BSREDP INFO [clj-scheduler.script:9] - 执行脚本:int flag = 0;

int deviceId = 0;
int ret = Model.station.Ccss_Connected(deviceId);
if (ret == 0) {
  /* 已连接 */ 
  flag++;
} else { 
  /* 未连接 */ 
  Model.GetVarByName<TextInputVar>("input_yzlwcsts").Value = "平面应变断裂韧度试验 预制疲劳裂纹检查操作试验运行"+Model.CurrentInst.Name+"异常信息：控制器连接失败，请重新连接控制器。";
  return false;
}
int ret1 = Model.station.Ccss_DriveReady(deviceId);
if (ret1 == 0) { 
  /* 准备就绪 */ 
  flag++;
} else {
  /* 未就绪 */ 
  Model.GetVarByName<TextInputVar>("input_yzlwcsts").Value = "控制器未在启动状态";
  return false;
}
if(flag == 2){
  Model.GetVarByName<TextInputVar>("input_yzlwcsts").Value = "请确认是否使用"+Model.CurrentInst.Name+"进行平面应变断裂韧度试验 预制疲劳裂纹试验？";
  return true;
}
return false;
25-09-11 14:26:36:437 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:26:36:437 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: \u0022\\u8BF7\\u786E\\u8BA4\\u662F\\u5426\\u4F7F\\u7528\\u8BD5\\u6837147\\u8FDB\\u884C\\u5E73\\u9762\\u5E94\\u53D8\\u65AD\\u88C2\\u97E7\\u5EA6\\u8BD5\\u9A8C \\u9884\\u5236\\u75B2\\u52B3\\u88C2\\u7EB9\\u8BD5\\u9A8C\\uFF1F\u0022,\r\n  \u0022Code\u0022: \u0022input_yzlwcsts\u0022,\r\n  \u0022Type\u0022: \u0022Text\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:26:36:443 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - RUN_SCRIPT_RSP
25-09-11 14:26:36:444 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"project_36","ScriptId":"984b03d5-c08d-4524-877a-0403f7414e71","Result":true}
25-09-11 14:26:37:579 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:26:37:579 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_three\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:26:37:584 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 14:26:37:585 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubtaskDialogBox-60715637-4d10-4965-aff6-da8166a38c78","MsgBody":{}}
25-09-11 14:26:37:586 DESKTOP-3BSREDP INFO [clj-scheduler.script:9] - 执行脚本:return Model.GetVarByName<BooleanInputVar>("input_three").Value;
25-09-11 14:26:37:606 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - RUN_SCRIPT_RSP
25-09-11 14:26:37:607 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"project_36","ScriptId":"2ee02612-5138-4ee3-ae41-a8dbc5600a49","Result":true}
25-09-11 14:26:37:633 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:26:37:633 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_yzlwbs\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:26:37:637 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 14:26:37:638 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubTaskEvalScript-d676b27c-cb55-4186-b751-0d80789aef79","MsgBody":{}}
25-09-11 14:26:37:639 DESKTOP-3BSREDP INFO [clj-scheduler.script:9] - 执行脚本:var flag = Model.GetVarByName<BooleanInputVar>("input_yzlwcxksbs").Value;
//是否为重新开始
return flag;
25-09-11 14:26:37:663 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - RUN_SCRIPT_RSP
25-09-11 14:26:37:664 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"project_36","ScriptId":"732125ab-9ea5-420a-8105-a5076f2ccb12","Result":true}
25-09-11 14:26:37:692 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 14:26:37:692 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubTaskEvalScript-9567e092-27d1-4397-87eb-144b0531a173","MsgBody":{}}
25-09-11 14:26:39:237 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:26:39:239 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 0,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 0,\r\n  \u0022Code\u0022: \u0022input_dqzszqs\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:26:39:244 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:26:39:244 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 0,\r\n  \u0022Dimension\u0022: \u0022dimension_cycle\u0022,\r\n  \u0022Unit\u0022: \u0022unit_cdw178e4a3\u0022,\r\n  \u0022DisplayUnitName\u0022: \u0022cycle\u0022,\r\n  \u0022DisplayValue\u0022: 0,\r\n  \u0022Code\u0022: \u0022input_starting_cycle\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:26:39:249 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 14:26:39:250 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubTaskEvalScript-f3c6e835-6291-45be-bd63-350f12f45f9a","MsgBody":{}}
25-09-11 14:26:39:284 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 14:26:39:285 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubTaskEvalScript-304442c2-62c7-4f92-bca4-5425989dd1cd","MsgBody":{}}
25-09-11 14:26:39:313 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 14:26:39:314 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubTaskEvalScript-7d35a2c8-7abb-4dbb-a88d-93a7760edc8b","MsgBody":{}}
25-09-11 14:26:39:339 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 14:26:39:340 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubTaskEvalScript-0d487418-fdc3-4d88-b707-10257acb369d","MsgBody":{}}
25-09-11 14:26:39:859 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 14:26:39:860 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubTaskTimer-878d0189-0bb6-4c27-8cb5-42eecfa315b6","MsgBody":{}}
25-09-11 14:26:39:861 DESKTOP-3BSREDP INFO [clj-scheduler.script:9] - 执行脚本:
//终止kmax
double abort_kmax = Model.GetVarByName<NumberInputVar>("input_abort_kmax").Value;
//初始kmax
double start_kmax = Model.GetVarByName<NumberInputVar>("input_start_kmax").Value;
//力值比
double force_ratio = Model.GetVarByName<NumberInputVar>("input_force_ratio").Value;
//试验频率
double prefabricated_crack_frequency =  Model.GetVarByName<NumberInputVar>("input_prefabricated_crack_frequency").Value;
//预制循环周次
double prefabrication_cycle = Model.GetVarByName<NumberInputVar>("input_prefabrication_cycle").Value;
//起始循环周次
double starting_cycle = Model.GetVarByName<NumberInputVar>("input_starting_cycle").Value;
//到达均值速度
double average_speed_arrival = Model.GetVarByName<NumberInputVar>("input_average_speed_arrival").Value;
//预制裂纹长度
double precast_crack_length = Model.GetVarByName<NumberInputVar>("input_precast_crack_length").Value;
//线性拟合数据上限
double upper_limit_of_linear_fitting_data = Model.GetVarByName<NumberInputVar>("input_upper_limit_of_linear_fitting_data").Value;
//线性拟合数据下限
double lower_bound_of_linear_fitting_data = Model.GetVarByName<NumberInputVar>("input_lower_bound_of_linear_fitting_data").Value;
//未萌生裂纹循环
double undeveloped_crack_cycle = Model.GetVarByName<NumberInputVar>("input_undeveloped_crack_cycle").Value;
//Kmax增幅比
double increase_ratio_kmax = Model.GetVarByName<NumberInputVar>("input_increase_ratio_kmax").Value;

//获取下位机量程
double? NominalValue = 0.0; //标称值
double? MaxValue = 0.0; //最大值
double? MinValue = 0.0; //最小值
double? UpperSoftLimit = 0.0; //软件上限位值
double? LowerSoftLimit = 0.0; //软件下限位值
int? SoftLimitReaction = 0;  //软件限位响应类型
double? BasicTare = 0.0;  //基础长时清零
double? Tare = 0.0;   //内存清零
double? McFilterTime = 0.0;  // 显示滤波
double? CtrlFilterTime = 0.0;   //控制滤波
//传入轴号、传感器号
int? ret = Model.station.Ccss_ReadSensorPara(0, 1, ref NominalValue, ref MaxValue, ref MinValue, ref UpperSoftLimit,
ref LowerSoftLimit, ref SoftLimitReaction, ref BasicTare, ref Tare, ref McFilterTime, ref CtrlFilterTime);
//打印量程
FuncLibs.Logger.Error("传感器信息：");
FuncLibs.Logger.Error($"ret : {ret}, NominalValue: {NominalValue}, MaxValue: {MaxValue}, MinValue: {MinValue}, UpperSoftLimit : {UpperSoftLimit}, LowerSoftLimit: {LowerSoftLimit}, SoftLimitReaction: {SoftLimitReaction}, BasicTare: {BasicTare},Tare: {Tare}, McFilterTime: {McFilterTime}, CtrlFilterTime: {CtrlFilterTime}");

// 计算周期波所用的参数
//峰谷值
double forceMax = 0;
double forceMin = 0;
TestExpert.PlaneStrainFractureToughnessMethod.Commons.PlaneStrainFractureToughnessMethod
.GetForce( start_kmax,  precast_crack_length, out forceMax);
forceMin = forceMax * force_ratio;


Console.WriteLine($"预制计算出的最大力:{forceMax} ");

if(forceMax<MaxValue && forceMax>MinValue && forceMin<MaxValue && forceMin>MinValue && forceMax>forceMin){
  // 赋值峰谷值变量
  Model.GetVarByName<NumberInputVar>("input_yzlw_valley").Value = forceMin;
  Model.GetVarByName<NumberInputVar>("input_yzlw_peak").Value = forceMax;
}else{
  Model.GetVarByName<NumberInputVar>("input_yzlw_valley").Value = 0;
  Model.GetVarByName<NumberInputVar>("input_yzlw_peak").Value = 0;
  return false;
}


Console.WriteLine($"预制裂纹峰值:{Model.GetVarByName<NumberInputVar>("input_yzlw_peak").Value} ");
Console.WriteLine($"预制裂纹谷值:{Model.GetVarByName<NumberInputVar>("input_yzlw_valley").Value} ");

//赋值循环次数
Model.GetVarByName<NumberInputVar>("input_yzlwxhcs").Value = prefabrication_cycle - starting_cycle;

Model.iVariable["moniliewen"]=0;
Model.dVariableArray["tenLength"] = new double[10];

return true;
25-09-11 14:26:39:889 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:26:39:890 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 1.568002635231078,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 1.568002635231078,\r\n  \u0022Code\u0022: \u0022input_yzlw_valley\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:26:39:895 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:26:39:896 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 15.68002635231078,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 15.68002635231078,\r\n  \u0022Code\u0022: \u0022input_yzlw_peak\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:26:39:899 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:26:39:899 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 1000000,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 1000000,\r\n  \u0022Code\u0022: \u0022input_yzlwxhcs\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:26:39:905 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - RUN_SCRIPT_RSP
25-09-11 14:26:39:906 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"project_36","ScriptId":"19e225ae-5a60-4bee-b2a6-1005dc31534d","Result":true}
25-09-11 14:26:39:915 DESKTOP-3BSREDP INFO [clj-scheduler.script:9] - 执行脚本:return true;
25-09-11 14:26:40:264 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - RUN_SCRIPT_RSP
25-09-11 14:26:40:265 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"project_36","ScriptId":"3da789e3-8045-478b-ad1e-fa239c9c7126","Result":true}
25-09-11 14:26:40:275 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:26:40:276 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_one\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:26:44:137 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:26:44:139 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 15.11165088785825,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 15.11165088785825,\r\n  \u0022Code\u0022: \u0022input_allKmax\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:26:45:055 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:26:45:056 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 19.287011527464525,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 19.287011527464525,\r\n  \u0022Code\u0022: \u0022input_allKmax\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:26:45:757 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:26:45:759 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 24.97584148635491,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 24.97584148635491,\r\n  \u0022Code\u0022: \u0022input_allKmax\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:26:46:369 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:26:46:369 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 33.891597436808524,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 33.891597436808524,\r\n  \u0022Code\u0022: \u0022input_allKmax\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:26:47:150 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:26:47:151 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 49.97722700836602,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 49.97722700836602,\r\n  \u0022Code\u0022: \u0022input_allKmax\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:26:47:787 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:26:47:787 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 84.46280537717269,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 84.46280537717269,\r\n  \u0022Code\u0022: \u0022input_allKmax\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:26:48:712 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:26:48:712 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 184.24547543994308,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 184.24547543994308,\r\n  \u0022Code\u0022: \u0022input_allKmax\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:26:49:260 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:26:49:262 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 870.1947192045508,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 870.1947192045508,\r\n  \u0022Code\u0022: \u0022input_allKmax\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:27:01:611 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_36-fbc2d565-2a68-473a-a2b5-7e932c5ae3a7 初始化
25-09-11 14:27:01:660 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 14:27:01:661 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-fbc2d565-2a68-473a-a2b5-7e932c5ae3a7","SubTaskID":"start-node-action","MsgBody":{}}
25-09-11 14:27:01:673 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 14:27:01:674 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"process-action","ClassName":"project_36","ProcessID":"project_36-fbc2d565-2a68-473a-a2b5-7e932c5ae3a7","SubTaskID":"onlyAction-eafe88f9-58ec-4721-aeb9-248ff2ff9a3e","MsgBody":{"Cmd":"abort","InstCode":"sample_14785d372","ActionID":"ab56adb1-93c8-4ca0-a353-39b5c7a3ed16"}}
25-09-11 14:27:01:676 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-11 14:27:01:677 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_36-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16  Stop: {:parent nil}
25-09-11 14:27:01:796 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-11 14:27:01:796 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 14:27:01:797 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-fbc2d565-2a68-473a-a2b5-7e932c5ae3a7","SubTaskID":"onlyAction-eafe88f9-58ec-4721-aeb9-248ff2ff9a3e","MsgBody":{}}
25-09-11 14:27:01:797 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 14:27:01:797 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"peakGatherData-96560ef5-387c-45b7-850a-b53d5e44f2f6","MsgBody":{}}
25-09-11 14:27:01:799 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 14:27:01:799 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"arrayDataHandler-3c66b213-0124-415a-ab07-5063a2b97bcc","MsgBody":{}}
25-09-11 14:27:01:799 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 14:27:01:800 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"hightGatherData-2d77f01c-f5da-465e-896c-a8d322f7687c","MsgBody":{}}
25-09-11 14:27:01:800 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 14:27:01:801 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"daqCycle-55422a18-1e18-4163-96d7-92687306d8e7","MsgBody":{}}
25-09-11 14:27:01:879 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:27:01:879 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 272,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 272,\r\n  \u0022Code\u0022: \u0022input_dqzszqs\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:27:01:888 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:27:01:888 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 272,\r\n  \u0022Dimension\u0022: \u0022dimension_cycle\u0022,\r\n  \u0022Unit\u0022: \u0022unit_cdw178e4a3\u0022,\r\n  \u0022DisplayUnitName\u0022: \u0022cycle\u0022,\r\n  \u0022DisplayValue\u0022: 272,\r\n  \u0022Code\u0022: \u0022input_starting_cycle\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:27:01:927 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:27:01:929 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 272,\r\n  \u0022Dimension\u0022: \u0022dimension_cycle\u0022,\r\n  \u0022Unit\u0022: \u0022unit_cdw178e4a3\u0022,\r\n  \u0022DisplayUnitName\u0022: \u0022cycle\u0022,\r\n  \u0022DisplayValue\u0022: 272,\r\n  \u0022Code\u0022: \u0022input_starting_cycle\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:27:01:933 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 14:27:01:935 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-fbc2d565-2a68-473a-a2b5-7e932c5ae3a7","SubTaskID":"SubTaskTimer-e8fc4f71-e7b1-40a4-9dec-85498fcdec3b","MsgBody":{}}
25-09-11 14:27:01:953 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:27:01:955 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 270,\r\n  \u0022Dimension\u0022: \u0022dimension_cycle\u0022,\r\n  \u0022Unit\u0022: \u0022unit_cdw178e4a3\u0022,\r\n  \u0022DisplayUnitName\u0022: \u0022cycle\u0022,\r\n  \u0022DisplayValue\u0022: 270,\r\n  \u0022Code\u0022: \u0022input_starting_cycle\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:27:01:959 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:27:01:960 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_yzlwcxksbs\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: true,\r\n  \u0022IsCheck\u0022: true,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:27:01:963 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:27:01:963 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: \u0022RESTART\u0022,\r\n  \u0022Code\u0022: \u0022input_yzlwhcq\u0022,\r\n  \u0022Type\u0022: \u0022Select\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:27:01:967 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:27:01:967 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: false,\r\n  \u0022Code\u0022: \u0022input_yzlwbs\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:27:01:971 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:27:01:972 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 0,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 0,\r\n  \u0022Code\u0022: \u0022input_kf\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:27:01:977 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:27:01:977 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 870.1947192045508,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 870.1947192045508,\r\n  \u0022Code\u0022: \u0022input_allKmax\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:27:01:981 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:27:01:982 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 0,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 0,\r\n  \u0022Code\u0022: \u0022input_kf\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:27:01:992 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:27:01:993 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 870.1947192045508,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 870.1947192045508,\r\n  \u0022Code\u0022: \u0022input_zzzdylqdyz\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:27:02:002 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:27:02:002 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 15.680025100708008,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 15.680025100708008,\r\n  \u0022Code\u0022: \u0022input_zzzdl\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:27:02:012 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:27:02:013 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 47.28572965796207,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 47.28572965796207,\r\n  \u0022Code\u0022: \u0022input_yzlwjglwcd\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:27:02:020 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 14:27:02:021 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-fbc2d565-2a68-473a-a2b5-7e932c5ae3a7","SubTaskID":"SubTaskEvalScript-4d9f4ca2-4006-412b-a5ff-cfcdfc18f23b","MsgBody":{}}
25-09-11 14:27:02:037 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 14:27:02:037 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-fbc2d565-2a68-473a-a2b5-7e932c5ae3a7","SubTaskID":"SubTaskEvalScript-57665592-8645-4b41-820a-b3eb30770c0c","MsgBody":{}}
25-09-11 14:27:02:054 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:27:02:055 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_yzlwcxksbs\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: true,\r\n  \u0022IsCheck\u0022: true,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:27:02:063 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:27:02:064 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: \u0022RESTART\u0022,\r\n  \u0022Code\u0022: \u0022input_yzlwhcq\u0022,\r\n  \u0022Type\u0022: \u0022Select\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:27:02:073 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:27:02:075 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: false,\r\n  \u0022Code\u0022: \u0022input_yzlwbs\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:27:02:084 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:27:02:085 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 12.857620525360108,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 12.857620525360108,\r\n  \u0022Code\u0022: \u0022input_yzlw_fzx1\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:27:02:096 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:27:02:099 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 4.390406799316406,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 4.390406799316406,\r\n  \u0022Code\u0022: \u0022input_yzlw_fzx2\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:27:02:107 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:27:02:107 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 2.568002223968506,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 2.568002223968506,\r\n  \u0022Code\u0022: \u0022input_yzlw_fzx_qd\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:27:02:115 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:27:02:116 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 16.680025100708008,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 16.680025100708008,\r\n  \u0022Code\u0022: \u0022input_yzlw_fzx_zd\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:27:02:122 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 14:27:02:123 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-fbc2d565-2a68-473a-a2b5-7e932c5ae3a7","SubTaskID":"SubTaskEvalScript-cc8952a1-430c-45c2-bb47-4082987dda7a","MsgBody":{}}
25-09-11 14:27:02:132 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 14:27:02:133 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"process-action","ClassName":"project_36","ProcessID":"project_36-fbc2d565-2a68-473a-a2b5-7e932c5ae3a7","SubTaskID":"onlyAction-04d036d1-723f-47e0-a8b9-5fd50c3aa312","MsgBody":{"Cmd":"start","InstCode":"sample_14785d372","ActionID":"a5112cfc-f9ee-4232-86e0-ff1e81e9df45"}}
25-09-11 14:27:02:139 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_36-a5112cfc-f9ee-4232-86e0-ff1e81e9df45 初始化
25-09-11 14:27:02:189 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 14:27:02:190 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-a5112cfc-f9ee-4232-86e0-ff1e81e9df45","SubTaskID":"start-node-action","MsgBody":{}}
25-09-11 14:27:02:214 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SAVE_PROJECT
25-09-11 14:27:02:214 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProjectID":36}
25-09-11 14:27:02:215 DESKTOP-3BSREDP INFO [clj-scheduler.mq:47] - Received SAVE_PROJECT ZMQ message, dispatching to project channel: {:ProjectID 36}
25-09-11 14:27:02:215 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 14:27:02:215 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-a5112cfc-f9ee-4232-86e0-ff1e81e9df45","SubTaskID":"SubTaskEvalScript-c9b5b399-c9dd-439e-b815-683c9bdfc8b4","MsgBody":{}}
25-09-11 14:27:02:220 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 14:27:02:221 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-a5112cfc-f9ee-4232-86e0-ff1e81e9df45","SubTaskID":"end-node-action","MsgBody":{}}
25-09-11 14:27:02:222 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_36-a5112cfc-f9ee-4232-86e0-ff1e81e9df45  Stop: {:parent {:Type "process-action", :ClassName "project_36", :ProcessID "project_36-fbc2d565-2a68-473a-a2b5-7e932c5ae3a7", :SubTaskID "onlyAction-04d036d1-723f-47e0-a8b9-5fd50c3aa312", :MsgBody {:Cmd "start", :InstCode "sample_14785d372", :ActionID "a5112cfc-f9ee-4232-86e0-ff1e81e9df45"}}}
25-09-11 14:27:02:267 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-11 14:27:02:272 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 14:27:02:273 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-fbc2d565-2a68-473a-a2b5-7e932c5ae3a7","SubTaskID":"onlyAction-04d036d1-723f-47e0-a8b9-5fd50c3aa312","MsgBody":{}}
25-09-11 14:27:02:277 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 14:27:02:277 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-fbc2d565-2a68-473a-a2b5-7e932c5ae3a7","SubTaskID":"end-node-action","MsgBody":{}}
25-09-11 14:27:02:279 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_36-fbc2d565-2a68-473a-a2b5-7e932c5ae3a7  Stop: {:parent nil}
25-09-11 14:27:02:334 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-11 14:28:47:530 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_36-a5112cfc-f9ee-4232-86e0-ff1e81e9df45 初始化
25-09-11 14:28:47:583 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_36-63526397-7d5a-4d20-8840-4fa2ceee596d 初始化
25-09-11 14:28:47:586 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 14:28:47:587 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-a5112cfc-f9ee-4232-86e0-ff1e81e9df45","SubTaskID":"start-node-action","MsgBody":{}}
25-09-11 14:28:47:630 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SAVE_PROJECT
25-09-11 14:28:47:630 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProjectID":36}
25-09-11 14:28:47:632 DESKTOP-3BSREDP INFO [clj-scheduler.mq:47] - Received SAVE_PROJECT ZMQ message, dispatching to project channel: {:ProjectID 36}
25-09-11 14:28:47:632 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 14:28:47:633 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-a5112cfc-f9ee-4232-86e0-ff1e81e9df45","SubTaskID":"SubTaskEvalScript-c9b5b399-c9dd-439e-b815-683c9bdfc8b4","MsgBody":{}}
25-09-11 14:28:47:668 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 14:28:47:669 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-a5112cfc-f9ee-4232-86e0-ff1e81e9df45","SubTaskID":"end-node-action","MsgBody":{}}
25-09-11 14:28:47:670 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_36-a5112cfc-f9ee-4232-86e0-ff1e81e9df45  Stop: {:parent nil}
25-09-11 14:28:47:775 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 14:28:47:775 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-63526397-7d5a-4d20-8840-4fa2ceee596d","SubTaskID":"start-node-action","MsgBody":{}}
25-09-11 14:28:47:835 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-11 14:28:47:864 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:28:47:866 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: false,\r\n  \u0022Code\u0022: \u0022input_ifopendivice\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: true,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022GLOBAL\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:28:47:876 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:28:47:876 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: false,\r\n  \u0022Code\u0022: \u0022input_ifstationon\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: true,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022GLOBAL\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:28:47:888 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 14:28:47:889 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-63526397-7d5a-4d20-8840-4fa2ceee596d","SubTaskID":"SubTaskEvalScript-8e9f3ddc-e65c-429c-8106-cfe857f6f11a","MsgBody":{}}
25-09-11 14:28:47:925 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 14:28:47:927 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-63526397-7d5a-4d20-8840-4fa2ceee596d","SubTaskID":"end-node-action","MsgBody":{}}
25-09-11 14:28:47:928 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_36-63526397-7d5a-4d20-8840-4fa2ceee596d  Stop: {:parent nil}
25-09-11 14:28:47:930 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-11 14:28:47:931 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_36-a5112cfc-f9ee-4232-86e0-ff1e81e9df45  Stop: {:parent nil}
25-09-11 14:28:47:933 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-11 14:28:47:934 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-11 14:28:47:935 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_36-952ef7e6-11e2-4006-a1dd-ab5ac9fecd98  Stop: {:parent {:Type "process-action", :ClassName "project_36", :ProcessID "project_36-fd5e6a51-3fd7-4ba9-a3bf-71ddeff5e01f", :SubTaskID "onlyAction-dd4c7394-44e6-4bda-b826-6c7675bc0792", :MsgBody {:Cmd "start", :InstCode "sample_14785d372", :ActionID "952ef7e6-11e2-4006-a1dd-ab5ac9fecd98"}}}
25-09-11 14:28:47:939 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-11 14:28:47:939 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-11 14:28:47:940 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_36-679f70fa-d870-40d9-b121-c759b28044ed  Stop: {:parent nil}
25-09-11 14:28:47:944 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-11 14:28:47:945 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-11 14:28:47:946 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_36-50753527-8b0e-419d-9ba2-c09916053748  Stop: {:parent nil}
25-09-11 14:28:47:949 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-11 14:28:47:950 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-11 14:28:47:951 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_36-d387206f-efe9-4d8b-8020-38ece5121f0f  Stop: {:parent nil}
25-09-11 14:28:47:954 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-11 14:28:47:955 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-11 14:28:47:956 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_36-fd5e6a51-3fd7-4ba9-a3bf-71ddeff5e01f  Stop: {:parent nil}
25-09-11 14:28:47:958 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-11 14:28:47:960 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-11 14:28:47:961 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_36-ec6e0760-ab6d-496e-bdd5-292be45ed606  Stop: {:parent nil}
25-09-11 14:28:47:963 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-11 14:28:47:964 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-11 14:28:47:965 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_36-e3c8ca6f-23bf-4fc6-8495-da6d17924867  Stop: {:parent {:Type "process-action", :ClassName "project_36", :ProcessID "project_36-5a5f09ef-c362-437a-9a8a-8e1d86e455d1", :SubTaskID "online-b1e962d1-dc19-43b3-8067-c7c00caade1a", :MsgBody {:Cmd "start", :InstCode "sample_14785d372", :ActionID "e3c8ca6f-23bf-4fc6-8495-da6d17924867"}}}
25-09-11 14:28:47:969 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-11 14:28:47:970 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-11 14:28:47:971 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_36-5a5f09ef-c362-437a-9a8a-8e1d86e455d1  Stop: {:parent nil}
25-09-11 14:28:48:088 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-11 14:28:48:159 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-11 14:28:48:160 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-11 14:28:48:161 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_36-3601749f-b58b-4018-b8c3-e1bfc760f8e0  Stop: {:parent nil}
25-09-11 14:28:48:163 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-11 14:28:48:163 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-11 14:28:48:164 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_36-6a579b71-e5fb-4bef-9569-33de699c61ea  Stop: {:parent nil}
25-09-11 14:28:48:166 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-11 14:28:48:168 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-11 14:28:48:168 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_36-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16  Stop: {:parent nil}
25-09-11 14:28:48:170 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-11 14:28:48:171 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-11 14:28:48:172 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_36-fbc2d565-2a68-473a-a2b5-7e932c5ae3a7  Stop: {:parent nil}
25-09-11 14:28:48:173 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-11 14:28:48:174 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-11 14:28:48:174 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_36-63526397-7d5a-4d20-8840-4fa2ceee596d  Stop: {:parent nil}
25-09-11 14:28:48:177 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-11 14:28:49:046 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 14:28:49:046 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-5a5f09ef-c362-437a-9a8a-8e1d86e455d1","SubTaskID":"daqRmc-9c1f5247-99e5-4a62-8446-73b344f248a2","MsgBody":{}}
25-09-11 14:28:56:525 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - TEMPLATE_INST_RSP
25-09-11 14:28:56:525 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"67a50ea6-6f5a-41d3-ba03-7b0877572f56","code":0,"msg":"\u6A21\u677F\u751F\u6210\u6210\u529F"}
25-09-11 14:28:57:856 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_38-6a579b71-e5fb-4bef-9569-33de699c61ea 初始化
25-09-11 14:28:57:944 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 14:28:57:944 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_38","ProcessID":"project_38-6a579b71-e5fb-4bef-9569-33de699c61ea","SubTaskID":"start-node-action","MsgBody":{}}
25-09-11 14:28:58:627 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:28:58:629 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_yzlwcxksbs\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: true,\r\n  \u0022IsCheck\u0022: true,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_38\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:28:58:637 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:28:58:639 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: false,\r\n  \u0022Code\u0022: \u0022input_yzlwbs\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_38\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:28:58:643 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 14:28:58:643 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_38","ProcessID":"project_38-6a579b71-e5fb-4bef-9569-33de699c61ea","SubTaskID":"SubTaskEvalScript-b339a41f-5402-476c-99f1-aac17677e715","MsgBody":{}}
25-09-11 14:28:58:728 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 14:28:58:729 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_38","ProcessID":"project_38-6a579b71-e5fb-4bef-9569-33de699c61ea","SubTaskID":"end-node-action","MsgBody":{}}
25-09-11 14:28:58:731 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_38-6a579b71-e5fb-4bef-9569-33de699c61ea  Stop: {:parent nil}
25-09-11 14:28:58:881 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-11 14:29:01:452 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_38-5a5f09ef-c362-437a-9a8a-8e1d86e455d1 初始化
25-09-11 14:29:01:532 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 14:29:01:532 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_38","ProcessID":"project_38-5a5f09ef-c362-437a-9a8a-8e1d86e455d1","SubTaskID":"start-node-action","MsgBody":{}}
25-09-11 14:29:01:559 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 14:29:01:559 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_38","ProcessID":"project_38-5a5f09ef-c362-437a-9a8a-8e1d86e455d1","SubTaskID":"SubTaskEvalScript-38c7f155-9116-43d2-97c4-c0952ddf8d40","MsgBody":{}}
25-09-11 14:29:01:629 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:29:01:630 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_ifopendivice\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: true,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_36\u0022,\r\n  \u0022InstCode\u0022: \u0022GLOBAL\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:29:01:639 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 14:29:01:639 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"process-action","ClassName":"project_36","ProcessID":"project_36-5a5f09ef-c362-437a-9a8a-8e1d86e455d1","SubTaskID":"online-b1e962d1-dc19-43b3-8067-c7c00caade1a","MsgBody":{"Cmd":"start","InstCode":"sample_14785d372","ActionID":"e3c8ca6f-23bf-4fc6-8495-da6d17924867"}}
25-09-11 14:29:01:644 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_36-e3c8ca6f-23bf-4fc6-8495-da6d17924867 初始化
25-09-11 14:29:01:709 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 14:29:01:710 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_38","ProcessID":"project_38-5a5f09ef-c362-437a-9a8a-8e1d86e455d1","SubTaskID":"SubTaskStartOnline-e1e4d469-2cad-4868-83c0-7dad69ab9037","MsgBody":{}}
25-09-11 14:29:01:717 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:29:01:719 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_ifopendivice\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: true,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_38\u0022,\r\n  \u0022InstCode\u0022: \u0022GLOBAL\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:29:01:723 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 14:29:01:724 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-e3c8ca6f-23bf-4fc6-8495-da6d17924867","SubTaskID":"start-node-action","MsgBody":{}}
25-09-11 14:29:01:868 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 14:29:01:869 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-e3c8ca6f-23bf-4fc6-8495-da6d17924867","SubTaskID":"SubTaskEvalScript-71eaf190-eecc-40e6-bbbe-a83c79eddb64","MsgBody":{}}
25-09-11 14:29:01:874 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 14:29:01:875 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_36","ProcessID":"project_36-e3c8ca6f-23bf-4fc6-8495-da6d17924867","SubTaskID":"end-node-action","MsgBody":{}}
25-09-11 14:29:01:876 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_36-e3c8ca6f-23bf-4fc6-8495-da6d17924867  Stop: {:parent {:Type "process-action", :ClassName "project_36", :ProcessID "project_36-5a5f09ef-c362-437a-9a8a-8e1d86e455d1", :SubTaskID "online-b1e962d1-dc19-43b3-8067-c7c00caade1a", :MsgBody {:Cmd "start", :InstCode "sample_14785d372", :ActionID "e3c8ca6f-23bf-4fc6-8495-da6d17924867"}}}
25-09-11 14:29:01:955 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-11 14:29:02:128 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:29:02:129 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_ifstationon\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: true,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_38\u0022,\r\n  \u0022InstCode\u0022: \u0022GLOBAL\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:29:08:680 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_38-a5112cfc-f9ee-4232-86e0-ff1e81e9df45 初始化
25-09-11 14:29:08:749 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_38-63526397-7d5a-4d20-8840-4fa2ceee596d 初始化
25-09-11 14:29:08:759 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 14:29:08:760 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_38","ProcessID":"project_38-a5112cfc-f9ee-4232-86e0-ff1e81e9df45","SubTaskID":"start-node-action","MsgBody":{}}
25-09-11 14:29:08:871 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SAVE_PROJECT
25-09-11 14:29:08:872 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProjectID":38}
25-09-11 14:29:08:873 DESKTOP-3BSREDP INFO [clj-scheduler.mq:47] - Received SAVE_PROJECT ZMQ message, dispatching to project channel: {:ProjectID 38}
25-09-11 14:29:08:873 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 14:29:08:873 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_38","ProcessID":"project_38-a5112cfc-f9ee-4232-86e0-ff1e81e9df45","SubTaskID":"SubTaskEvalScript-c9b5b399-c9dd-439e-b815-683c9bdfc8b4","MsgBody":{}}
25-09-11 14:29:08:874 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 14:29:08:874 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_38","ProcessID":"project_38-63526397-7d5a-4d20-8840-4fa2ceee596d","SubTaskID":"start-node-action","MsgBody":{}}
25-09-11 14:29:08:878 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 14:29:08:879 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_38","ProcessID":"project_38-a5112cfc-f9ee-4232-86e0-ff1e81e9df45","SubTaskID":"end-node-action","MsgBody":{}}
25-09-11 14:29:08:879 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_38-a5112cfc-f9ee-4232-86e0-ff1e81e9df45  Stop: {:parent nil}
25-09-11 14:29:08:901 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:29:08:901 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: false,\r\n  \u0022Code\u0022: \u0022input_ifopendivice\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: true,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_38\u0022,\r\n  \u0022InstCode\u0022: \u0022GLOBAL\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:29:08:921 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:29:08:922 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: false,\r\n  \u0022Code\u0022: \u0022input_ifstationon\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: true,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_38\u0022,\r\n  \u0022InstCode\u0022: \u0022GLOBAL\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:29:08:929 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 14:29:08:929 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_38","ProcessID":"project_38-63526397-7d5a-4d20-8840-4fa2ceee596d","SubTaskID":"SubTaskEvalScript-8e9f3ddc-e65c-429c-8106-cfe857f6f11a","MsgBody":{}}
25-09-11 14:29:08:967 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 14:29:08:968 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_38","ProcessID":"project_38-63526397-7d5a-4d20-8840-4fa2ceee596d","SubTaskID":"end-node-action","MsgBody":{}}
25-09-11 14:29:08:970 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_38-63526397-7d5a-4d20-8840-4fa2ceee596d  Stop: {:parent nil}
25-09-11 14:29:08:971 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-11 14:29:08:972 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_38-6a579b71-e5fb-4bef-9569-33de699c61ea  Stop: {:parent nil}
25-09-11 14:29:08:972 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-11 14:29:08:976 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-11 14:29:08:977 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-11 14:29:08:977 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_38-5a5f09ef-c362-437a-9a8a-8e1d86e455d1  Stop: {:parent nil}
25-09-11 14:29:09:002 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 14:29:09:004 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_38","ProcessID":"project_38-5a5f09ef-c362-437a-9a8a-8e1d86e455d1","SubTaskID":"daqRmc-9c1f5247-99e5-4a62-8446-73b344f248a2","MsgBody":{}}
25-09-11 14:29:09:005 DESKTOP-3BSREDP ERROR [clj-scheduler.context:671] - 当前节点:daqRmc-9c1f5247-99e5-4a62-8446-73b344f248a2不在执行状态, 而是 :aborted
25-09-11 14:29:09:075 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-11 14:29:09:095 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-11 14:29:09:096 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-11 14:29:09:097 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_38-a5112cfc-f9ee-4232-86e0-ff1e81e9df45  Stop: {:parent nil}
25-09-11 14:29:09:100 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-11 14:29:09:101 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-11 14:29:09:101 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_38-63526397-7d5a-4d20-8840-4fa2ceee596d  Stop: {:parent nil}
25-09-11 14:29:09:103 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-11 14:29:54:316 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - TEMPLATE_INST_RSP
25-09-11 14:29:54:318 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"fe56079e-6812-4a8d-8936-bd4124f7be0f","code":0,"msg":"\u6A21\u677F\u751F\u6210\u6210\u529F"}
25-09-11 14:29:55:622 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_38-6a579b71-e5fb-4bef-9569-33de699c61ea 初始化
25-09-11 14:29:55:878 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 14:29:55:879 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_38","ProcessID":"project_38-6a579b71-e5fb-4bef-9569-33de699c61ea","SubTaskID":"start-node-action","MsgBody":{}}
25-09-11 14:29:56:191 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:29:56:192 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_yzlwcxksbs\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: true,\r\n  \u0022IsCheck\u0022: true,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_38\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:29:56:197 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:29:56:198 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: false,\r\n  \u0022Code\u0022: \u0022input_yzlwbs\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_38\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:29:56:201 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 14:29:56:202 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_38","ProcessID":"project_38-6a579b71-e5fb-4bef-9569-33de699c61ea","SubTaskID":"SubTaskEvalScript-b339a41f-5402-476c-99f1-aac17677e715","MsgBody":{}}
25-09-11 14:29:56:238 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 14:29:56:239 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_38","ProcessID":"project_38-6a579b71-e5fb-4bef-9569-33de699c61ea","SubTaskID":"end-node-action","MsgBody":{}}
25-09-11 14:29:56:240 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_38-6a579b71-e5fb-4bef-9569-33de699c61ea  Stop: {:parent nil}
25-09-11 14:29:56:300 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-11 14:29:58:957 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_38-5a5f09ef-c362-437a-9a8a-8e1d86e455d1 初始化
25-09-11 14:29:59:021 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 14:29:59:022 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_38","ProcessID":"project_38-5a5f09ef-c362-437a-9a8a-8e1d86e455d1","SubTaskID":"start-node-action","MsgBody":{}}
25-09-11 14:29:59:070 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 14:29:59:070 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_38","ProcessID":"project_38-5a5f09ef-c362-437a-9a8a-8e1d86e455d1","SubTaskID":"SubTaskEvalScript-38c7f155-9116-43d2-97c4-c0952ddf8d40","MsgBody":{}}
25-09-11 14:30:02:617 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 14:30:02:618 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_38","ProcessID":"project_38-5a5f09ef-c362-437a-9a8a-8e1d86e455d1","SubTaskID":"SubTaskStartOnline-e1e4d469-2cad-4868-83c0-7dad69ab9037","MsgBody":{}}
25-09-11 14:30:02:624 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 14:30:02:624 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_38","ProcessID":"project_38-5a5f09ef-c362-437a-9a8a-8e1d86e455d1","SubTaskID":"SubTaskStartOnline-e1e4d469-2cad-4868-83c0-7dad69ab9037","MsgBody":{}}
25-09-11 14:30:02:626 DESKTOP-3BSREDP ERROR [clj-scheduler.context:671] - 当前节点:SubTaskStartOnline-e1e4d469-2cad-4868-83c0-7dad69ab9037不在执行状态, 而是 :finished
25-09-11 14:30:02:673 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:30:02:673 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_ifopendivice\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: true,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_38\u0022,\r\n  \u0022InstCode\u0022: \u0022GLOBAL\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:30:13:599 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_38-679f70fa-d870-40d9-b121-c759b28044ed 初始化
25-09-11 14:30:13:659 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 14:30:13:660 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_38","ProcessID":"project_38-679f70fa-d870-40d9-b121-c759b28044ed","SubTaskID":"start-node-action","MsgBody":{}}
25-09-11 14:30:13:683 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 14:30:13:683 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_38","ProcessID":"project_38-679f70fa-d870-40d9-b121-c759b28044ed","SubTaskID":"cmdConnection-d024261f-2793-4b35-bc63-6661078f2b6e","MsgBody":{}}
25-09-11 14:30:13:755 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 14:30:13:756 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_38","ProcessID":"project_38-679f70fa-d870-40d9-b121-c759b28044ed","SubTaskID":"end-node-action","MsgBody":{}}
25-09-11 14:30:13:756 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_38-679f70fa-d870-40d9-b121-c759b28044ed  Stop: {:parent nil}
25-09-11 14:30:13:798 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-11 14:30:14:004 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 14:30:14:006 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_38","ProcessID":"project_38-679f70fa-d870-40d9-b121-c759b28044ed","SubTaskID":"cmdConnection-d024261f-2793-4b35-bc63-6661078f2b6e","MsgBody":{}}
25-09-11 14:30:14:323 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:30:14:324 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_ifstationon\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: true,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_38\u0022,\r\n  \u0022InstCode\u0022: \u0022GLOBAL\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:30:24:839 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_38-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16 初始化
25-09-11 14:30:24:917 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 14:30:24:918 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_38","ProcessID":"project_38-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"start-node-action","MsgBody":{}}
25-09-11 14:30:24:919 DESKTOP-3BSREDP INFO [clj-scheduler.script:9] - 执行脚本:int flag = 0;

int deviceId = 0;
int ret = Model.station.Ccss_Connected(deviceId);
if (ret == 0) {
  /* 已连接 */ 
  flag++;
} else { 
  /* 未连接 */ 
  Model.GetVarByName<TextInputVar>("input_yzlwcsts").Value = "平面应变断裂韧度试验 预制疲劳裂纹检查操作试验运行"+Model.CurrentInst.Name+"异常信息：控制器连接失败，请重新连接控制器。";
  return false;
}
int ret1 = Model.station.Ccss_DriveReady(deviceId);
if (ret1 == 0) { 
  /* 准备就绪 */ 
  flag++;
} else {
  /* 未就绪 */ 
  Model.GetVarByName<TextInputVar>("input_yzlwcsts").Value = "控制器未在启动状态";
  return false;
}
if(flag == 2){
  Model.GetVarByName<TextInputVar>("input_yzlwcsts").Value = "请确认是否使用"+Model.CurrentInst.Name+"进行平面应变断裂韧度试验 预制疲劳裂纹试验？";
  return true;
}
return false;
25-09-11 14:30:24:994 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:30:24:994 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: \u0022\\u8BF7\\u786E\\u8BA4\\u662F\\u5426\\u4F7F\\u7528\\u8BD5\\u6837147\\u8FDB\\u884C\\u5E73\\u9762\\u5E94\\u53D8\\u65AD\\u88C2\\u97E7\\u5EA6\\u8BD5\\u9A8C \\u9884\\u5236\\u75B2\\u52B3\\u88C2\\u7EB9\\u8BD5\\u9A8C\\uFF1F\u0022,\r\n  \u0022Code\u0022: \u0022input_yzlwcsts\u0022,\r\n  \u0022Type\u0022: \u0022Text\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_38\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:30:24:998 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - RUN_SCRIPT_RSP
25-09-11 14:30:24:998 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"project_38","ScriptId":"b4d36a31-0717-42bf-80aa-acf78c16abc6","Result":true}
25-09-11 14:30:26:241 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:30:26:242 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_three\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_38\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:30:26:246 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 14:30:26:246 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_38","ProcessID":"project_38-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubtaskDialogBox-60715637-4d10-4965-aff6-da8166a38c78","MsgBody":{}}
25-09-11 14:30:26:247 DESKTOP-3BSREDP INFO [clj-scheduler.script:9] - 执行脚本:return Model.GetVarByName<BooleanInputVar>("input_three").Value;
25-09-11 14:30:26:279 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - RUN_SCRIPT_RSP
25-09-11 14:30:26:280 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"project_38","ScriptId":"6372985c-07c3-466c-89ad-faadf5bf8aa6","Result":true}
25-09-11 14:30:26:476 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:30:26:477 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_yzlwbs\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_38\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:30:26:687 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 14:30:26:688 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_38","ProcessID":"project_38-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubTaskEvalScript-d676b27c-cb55-4186-b751-0d80789aef79","MsgBody":{}}
25-09-11 14:30:26:689 DESKTOP-3BSREDP INFO [clj-scheduler.script:9] - 执行脚本:var flag = Model.GetVarByName<BooleanInputVar>("input_yzlwcxksbs").Value;
//是否为重新开始
return flag;
25-09-11 14:30:26:727 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - RUN_SCRIPT_RSP
25-09-11 14:30:26:727 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"project_38","ScriptId":"b6cac647-3203-48d1-8dd2-5b45110475f2","Result":true}
25-09-11 14:30:26:770 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 14:30:26:771 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_38","ProcessID":"project_38-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubTaskEvalScript-9567e092-27d1-4397-87eb-144b0531a173","MsgBody":{}}
25-09-11 14:30:26:862 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:30:26:863 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 0,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 0,\r\n  \u0022Code\u0022: \u0022input_dqzszqs\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_38\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:30:26:867 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:30:26:867 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 0,\r\n  \u0022Dimension\u0022: \u0022dimension_cycle\u0022,\r\n  \u0022Unit\u0022: \u0022unit_cdw178e4a3\u0022,\r\n  \u0022DisplayUnitName\u0022: \u0022cycle\u0022,\r\n  \u0022DisplayValue\u0022: 0,\r\n  \u0022Code\u0022: \u0022input_starting_cycle\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_38\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:30:26:873 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 14:30:26:873 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_38","ProcessID":"project_38-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubTaskEvalScript-f3c6e835-6291-45be-bd63-350f12f45f9a","MsgBody":{}}
25-09-11 14:30:26:930 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 14:30:26:930 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_38","ProcessID":"project_38-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubTaskEvalScript-304442c2-62c7-4f92-bca4-5425989dd1cd","MsgBody":{}}
25-09-11 14:30:27:036 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 14:30:27:037 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_38","ProcessID":"project_38-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubTaskEvalScript-7d35a2c8-7abb-4dbb-a88d-93a7760edc8b","MsgBody":{}}
25-09-11 14:30:27:177 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 14:30:27:177 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_38","ProcessID":"project_38-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubTaskEvalScript-0d487418-fdc3-4d88-b707-10257acb369d","MsgBody":{}}
25-09-11 14:30:27:698 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 14:30:27:699 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_38","ProcessID":"project_38-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubTaskTimer-878d0189-0bb6-4c27-8cb5-42eecfa315b6","MsgBody":{}}
25-09-11 14:30:27:699 DESKTOP-3BSREDP INFO [clj-scheduler.script:9] - 执行脚本:
//终止kmax
double abort_kmax = Model.GetVarByName<NumberInputVar>("input_abort_kmax").Value;
//初始kmax
double start_kmax = Model.GetVarByName<NumberInputVar>("input_start_kmax").Value;
//力值比
double force_ratio = Model.GetVarByName<NumberInputVar>("input_force_ratio").Value;
//试验频率
double prefabricated_crack_frequency =  Model.GetVarByName<NumberInputVar>("input_prefabricated_crack_frequency").Value;
//预制循环周次
double prefabrication_cycle = Model.GetVarByName<NumberInputVar>("input_prefabrication_cycle").Value;
//起始循环周次
double starting_cycle = Model.GetVarByName<NumberInputVar>("input_starting_cycle").Value;
//到达均值速度
double average_speed_arrival = Model.GetVarByName<NumberInputVar>("input_average_speed_arrival").Value;
//预制裂纹长度
double precast_crack_length = Model.GetVarByName<NumberInputVar>("input_precast_crack_length").Value;
//线性拟合数据上限
double upper_limit_of_linear_fitting_data = Model.GetVarByName<NumberInputVar>("input_upper_limit_of_linear_fitting_data").Value;
//线性拟合数据下限
double lower_bound_of_linear_fitting_data = Model.GetVarByName<NumberInputVar>("input_lower_bound_of_linear_fitting_data").Value;
//未萌生裂纹循环
double undeveloped_crack_cycle = Model.GetVarByName<NumberInputVar>("input_undeveloped_crack_cycle").Value;
//Kmax增幅比
double increase_ratio_kmax = Model.GetVarByName<NumberInputVar>("input_increase_ratio_kmax").Value;

//获取下位机量程
double? NominalValue = 0.0; //标称值
double? MaxValue = 0.0; //最大值
double? MinValue = 0.0; //最小值
double? UpperSoftLimit = 0.0; //软件上限位值
double? LowerSoftLimit = 0.0; //软件下限位值
int? SoftLimitReaction = 0;  //软件限位响应类型
double? BasicTare = 0.0;  //基础长时清零
double? Tare = 0.0;   //内存清零
double? McFilterTime = 0.0;  // 显示滤波
double? CtrlFilterTime = 0.0;   //控制滤波
//传入轴号、传感器号
int? ret = Model.station.Ccss_ReadSensorPara(0, 1, ref NominalValue, ref MaxValue, ref MinValue, ref UpperSoftLimit,
ref LowerSoftLimit, ref SoftLimitReaction, ref BasicTare, ref Tare, ref McFilterTime, ref CtrlFilterTime);
//打印量程
FuncLibs.Logger.Error("传感器信息：");
FuncLibs.Logger.Error($"ret : {ret}, NominalValue: {NominalValue}, MaxValue: {MaxValue}, MinValue: {MinValue}, UpperSoftLimit : {UpperSoftLimit}, LowerSoftLimit: {LowerSoftLimit}, SoftLimitReaction: {SoftLimitReaction}, BasicTare: {BasicTare},Tare: {Tare}, McFilterTime: {McFilterTime}, CtrlFilterTime: {CtrlFilterTime}");

// 计算周期波所用的参数
//峰谷值
double forceMax = 0;
double forceMin = 0;
TestExpert.PlaneStrainFractureToughnessMethod.Commons.PlaneStrainFractureToughnessMethod
.GetForce( start_kmax,  precast_crack_length, out forceMax);
forceMin = forceMax * force_ratio;


Console.WriteLine($"预制计算出的最大力:{forceMax} ");

if(forceMax<MaxValue && forceMax>MinValue && forceMin<MaxValue && forceMin>MinValue && forceMax>forceMin){
  // 赋值峰谷值变量
  Model.GetVarByName<NumberInputVar>("input_yzlw_valley").Value = forceMin;
  Model.GetVarByName<NumberInputVar>("input_yzlw_peak").Value = forceMax;
}else{
  Model.GetVarByName<NumberInputVar>("input_yzlw_valley").Value = 0;
  Model.GetVarByName<NumberInputVar>("input_yzlw_peak").Value = 0;
  return false;
}


Console.WriteLine($"预制裂纹峰值:{Model.GetVarByName<NumberInputVar>("input_yzlw_peak").Value} ");
Console.WriteLine($"预制裂纹谷值:{Model.GetVarByName<NumberInputVar>("input_yzlw_valley").Value} ");

//赋值循环次数
Model.GetVarByName<NumberInputVar>("input_yzlwxhcs").Value = prefabrication_cycle - starting_cycle;

Model.iVariable["moniliewen"]=0;
Model.dVariableArray["tenLength"] = new double[10];

return true;
25-09-11 14:30:28:013 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:30:28:013 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 1.568002635231078,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 1.568002635231078,\r\n  \u0022Code\u0022: \u0022input_yzlw_valley\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_38\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:30:28:017 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:30:28:018 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 15.68002635231078,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 15.68002635231078,\r\n  \u0022Code\u0022: \u0022input_yzlw_peak\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_38\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:30:28:023 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:30:28:023 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 1000000,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 1000000,\r\n  \u0022Code\u0022: \u0022input_yzlwxhcs\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_38\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:30:28:027 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - RUN_SCRIPT_RSP
25-09-11 14:30:28:027 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"project_38","ScriptId":"0632bf55-1eb1-4e42-a581-6a7cc1c464bc","Result":true}
25-09-11 14:30:28:035 DESKTOP-3BSREDP INFO [clj-scheduler.script:9] - 执行脚本:return true;
25-09-11 14:30:28:439 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - RUN_SCRIPT_RSP
25-09-11 14:30:28:440 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"project_38","ScriptId":"3149ed56-1270-4e3b-acdb-2fcaf0b664e0","Result":true}
25-09-11 14:30:28:513 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:30:28:513 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_one\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_38\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:30:32:494 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:30:32:495 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 15.11165088785825,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 15.11165088785825,\r\n  \u0022Code\u0022: \u0022input_allKmax\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_38\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:30:33:395 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:30:33:396 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 19.287011527464525,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 19.287011527464525,\r\n  \u0022Code\u0022: \u0022input_allKmax\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_38\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:30:33:969 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:30:33:969 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 24.97584148635491,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 24.97584148635491,\r\n  \u0022Code\u0022: \u0022input_allKmax\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_38\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:30:34:604 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_38-fbc2d565-2a68-473a-a2b5-7e932c5ae3a7 初始化
25-09-11 14:30:34:622 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:30:34:622 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 33.891597436808524,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 33.891597436808524,\r\n  \u0022Code\u0022: \u0022input_allKmax\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_38\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:30:34:669 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 14:30:34:670 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_38","ProcessID":"project_38-fbc2d565-2a68-473a-a2b5-7e932c5ae3a7","SubTaskID":"start-node-action","MsgBody":{}}
25-09-11 14:30:34:886 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 14:30:34:887 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"process-action","ClassName":"project_38","ProcessID":"project_38-fbc2d565-2a68-473a-a2b5-7e932c5ae3a7","SubTaskID":"onlyAction-eafe88f9-58ec-4721-aeb9-248ff2ff9a3e","MsgBody":{"Cmd":"abort","InstCode":"sample_14785d372","ActionID":"ab56adb1-93c8-4ca0-a353-39b5c7a3ed16"}}
25-09-11 14:30:34:888 DESKTOP-3BSREDP INFO [clj-scheduler.context:1108] - 终止流程图
25-09-11 14:30:34:889 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_38-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16  Stop: {:parent nil}
25-09-11 14:30:34:943 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-11 14:30:34:944 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 14:30:34:945 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_38","ProcessID":"project_38-fbc2d565-2a68-473a-a2b5-7e932c5ae3a7","SubTaskID":"onlyAction-eafe88f9-58ec-4721-aeb9-248ff2ff9a3e","MsgBody":{}}
25-09-11 14:30:34:945 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 14:30:34:946 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_38","ProcessID":"project_38-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"peakGatherData-96560ef5-387c-45b7-850a-b53d5e44f2f6","MsgBody":{}}
25-09-11 14:30:34:946 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 14:30:34:946 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_38","ProcessID":"project_38-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"arrayDataHandler-3c66b213-0124-415a-ab07-5063a2b97bcc","MsgBody":{}}
25-09-11 14:30:34:946 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 14:30:34:947 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_38","ProcessID":"project_38-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"hightGatherData-2d77f01c-f5da-465e-896c-a8d322f7687c","MsgBody":{}}
25-09-11 14:30:34:947 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 14:30:34:947 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_38","ProcessID":"project_38-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"daqCycle-55422a18-1e18-4163-96d7-92687306d8e7","MsgBody":{}}
25-09-11 14:30:35:043 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:30:35:044 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 65,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 65,\r\n  \u0022Code\u0022: \u0022input_dqzszqs\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_38\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:30:35:047 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:30:35:049 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 65,\r\n  \u0022Dimension\u0022: \u0022dimension_cycle\u0022,\r\n  \u0022Unit\u0022: \u0022unit_cdw178e4a3\u0022,\r\n  \u0022DisplayUnitName\u0022: \u0022cycle\u0022,\r\n  \u0022DisplayValue\u0022: 65,\r\n  \u0022Code\u0022: \u0022input_starting_cycle\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_38\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:30:35:123 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:30:35:124 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 64,\r\n  \u0022Dimension\u0022: \u0022dimension_cycle\u0022,\r\n  \u0022Unit\u0022: \u0022unit_cdw178e4a3\u0022,\r\n  \u0022DisplayUnitName\u0022: \u0022cycle\u0022,\r\n  \u0022DisplayValue\u0022: 64,\r\n  \u0022Code\u0022: \u0022input_starting_cycle\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_38\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:30:35:128 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 14:30:35:128 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_38","ProcessID":"project_38-fbc2d565-2a68-473a-a2b5-7e932c5ae3a7","SubTaskID":"SubTaskTimer-e8fc4f71-e7b1-40a4-9dec-85498fcdec3b","MsgBody":{}}
25-09-11 14:30:35:171 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:30:35:172 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 64,\r\n  \u0022Dimension\u0022: \u0022dimension_cycle\u0022,\r\n  \u0022Unit\u0022: \u0022unit_cdw178e4a3\u0022,\r\n  \u0022DisplayUnitName\u0022: \u0022cycle\u0022,\r\n  \u0022DisplayValue\u0022: 64,\r\n  \u0022Code\u0022: \u0022input_starting_cycle\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_38\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:30:35:177 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:30:35:179 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_yzlwcxksbs\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: true,\r\n  \u0022IsCheck\u0022: true,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_38\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:30:35:184 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:30:35:185 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: \u0022RESTART\u0022,\r\n  \u0022Code\u0022: \u0022input_yzlwhcq\u0022,\r\n  \u0022Type\u0022: \u0022Select\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_38\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:30:35:189 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:30:35:190 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: false,\r\n  \u0022Code\u0022: \u0022input_yzlwbs\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_38\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:30:35:194 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:30:35:194 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 0,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 0,\r\n  \u0022Code\u0022: \u0022input_kf\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_38\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:30:35:198 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:30:35:198 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 33.891597436808524,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 33.891597436808524,\r\n  \u0022Code\u0022: \u0022input_allKmax\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_38\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:30:35:203 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:30:35:203 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 0,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 0,\r\n  \u0022Code\u0022: \u0022input_kf\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_38\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:30:35:211 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:30:35:212 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 33.891597436808524,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 33.891597436808524,\r\n  \u0022Code\u0022: \u0022input_zzzdylqdyz\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_38\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:30:35:219 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:30:35:220 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 15.680025100708008,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 15.680025100708008,\r\n  \u0022Code\u0022: \u0022input_zzzdl\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_38\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:30:35:230 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:30:35:231 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 28.371437794777243,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 28.371437794777243,\r\n  \u0022Code\u0022: \u0022input_yzlwjglwcd\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_38\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:30:35:240 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 14:30:35:241 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_38","ProcessID":"project_38-fbc2d565-2a68-473a-a2b5-7e932c5ae3a7","SubTaskID":"SubTaskEvalScript-4d9f4ca2-4006-412b-a5ff-cfcdfc18f23b","MsgBody":{}}
25-09-11 14:30:35:296 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 14:30:35:297 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_38","ProcessID":"project_38-fbc2d565-2a68-473a-a2b5-7e932c5ae3a7","SubTaskID":"SubTaskEvalScript-57665592-8645-4b41-820a-b3eb30770c0c","MsgBody":{}}
25-09-11 14:30:35:346 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:30:35:347 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_yzlwcxksbs\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: true,\r\n  \u0022IsCheck\u0022: true,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_38\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:30:35:351 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:30:35:351 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: \u0022RESTART\u0022,\r\n  \u0022Code\u0022: \u0022input_yzlwhcq\u0022,\r\n  \u0022Type\u0022: \u0022Select\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_38\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:30:35:355 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:30:35:356 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: false,\r\n  \u0022Code\u0022: \u0022input_yzlwbs\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_38\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:30:35:359 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:30:35:360 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 12.857620525360108,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 12.857620525360108,\r\n  \u0022Code\u0022: \u0022input_yzlw_fzx1\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_38\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:30:35:363 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:30:35:364 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 4.390406799316406,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 4.390406799316406,\r\n  \u0022Code\u0022: \u0022input_yzlw_fzx2\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_38\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:30:35:367 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:30:35:367 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 2.568002223968506,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 2.568002223968506,\r\n  \u0022Code\u0022: \u0022input_yzlw_fzx_qd\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_38\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:30:35:371 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:30:35:372 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 16.680025100708008,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 16.680025100708008,\r\n  \u0022Code\u0022: \u0022input_yzlw_fzx_zd\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_38\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:30:35:376 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 14:30:35:377 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_38","ProcessID":"project_38-fbc2d565-2a68-473a-a2b5-7e932c5ae3a7","SubTaskID":"SubTaskEvalScript-cc8952a1-430c-45c2-bb47-4082987dda7a","MsgBody":{}}
25-09-11 14:30:35:382 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 14:30:35:383 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"process-action","ClassName":"project_38","ProcessID":"project_38-fbc2d565-2a68-473a-a2b5-7e932c5ae3a7","SubTaskID":"onlyAction-04d036d1-723f-47e0-a8b9-5fd50c3aa312","MsgBody":{"Cmd":"start","InstCode":"sample_14785d372","ActionID":"a5112cfc-f9ee-4232-86e0-ff1e81e9df45"}}
25-09-11 14:30:35:389 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_38-a5112cfc-f9ee-4232-86e0-ff1e81e9df45 初始化
25-09-11 14:30:35:466 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 14:30:35:466 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_38","ProcessID":"project_38-a5112cfc-f9ee-4232-86e0-ff1e81e9df45","SubTaskID":"start-node-action","MsgBody":{}}
25-09-11 14:30:35:551 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SAVE_PROJECT
25-09-11 14:30:35:552 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProjectID":38}
25-09-11 14:30:35:552 DESKTOP-3BSREDP INFO [clj-scheduler.mq:47] - Received SAVE_PROJECT ZMQ message, dispatching to project channel: {:ProjectID 38}
25-09-11 14:30:35:553 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 14:30:35:553 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_38","ProcessID":"project_38-a5112cfc-f9ee-4232-86e0-ff1e81e9df45","SubTaskID":"SubTaskEvalScript-c9b5b399-c9dd-439e-b815-683c9bdfc8b4","MsgBody":{}}
25-09-11 14:30:35:563 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 14:30:35:564 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_38","ProcessID":"project_38-a5112cfc-f9ee-4232-86e0-ff1e81e9df45","SubTaskID":"end-node-action","MsgBody":{}}
25-09-11 14:30:35:565 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_38-a5112cfc-f9ee-4232-86e0-ff1e81e9df45  Stop: {:parent {:Type "process-action", :ClassName "project_38", :ProcessID "project_38-fbc2d565-2a68-473a-a2b5-7e932c5ae3a7", :SubTaskID "onlyAction-04d036d1-723f-47e0-a8b9-5fd50c3aa312", :MsgBody {:Cmd "start", :InstCode "sample_14785d372", :ActionID "a5112cfc-f9ee-4232-86e0-ff1e81e9df45"}}}
25-09-11 14:30:35:612 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-11 14:30:35:620 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 14:30:35:621 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_38","ProcessID":"project_38-fbc2d565-2a68-473a-a2b5-7e932c5ae3a7","SubTaskID":"onlyAction-04d036d1-723f-47e0-a8b9-5fd50c3aa312","MsgBody":{}}
25-09-11 14:30:35:627 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 14:30:35:627 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_38","ProcessID":"project_38-fbc2d565-2a68-473a-a2b5-7e932c5ae3a7","SubTaskID":"end-node-action","MsgBody":{}}
25-09-11 14:30:35:629 DESKTOP-3BSREDP INFO [clj-scheduler.context:1077] - 流程 project_38-fbc2d565-2a68-473a-a2b5-7e932c5ae3a7  Stop: {:parent nil}
25-09-11 14:30:35:741 DESKTOP-3BSREDP INFO [clj-scheduler.context:1095] - try-clear(close channel)~~
25-09-11 14:30:47:201 DESKTOP-3BSREDP INFO [clj-scheduler.context:1051] - 任务管理器 project_38-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16 初始化
25-09-11 14:30:47:256 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 14:30:47:256 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_38","ProcessID":"project_38-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"start-node-action","MsgBody":{}}
25-09-11 14:30:47:257 DESKTOP-3BSREDP INFO [clj-scheduler.script:9] - 执行脚本:int flag = 0;

int deviceId = 0;
int ret = Model.station.Ccss_Connected(deviceId);
if (ret == 0) {
  /* 已连接 */ 
  flag++;
} else { 
  /* 未连接 */ 
  Model.GetVarByName<TextInputVar>("input_yzlwcsts").Value = "平面应变断裂韧度试验 预制疲劳裂纹检查操作试验运行"+Model.CurrentInst.Name+"异常信息：控制器连接失败，请重新连接控制器。";
  return false;
}
int ret1 = Model.station.Ccss_DriveReady(deviceId);
if (ret1 == 0) { 
  /* 准备就绪 */ 
  flag++;
} else {
  /* 未就绪 */ 
  Model.GetVarByName<TextInputVar>("input_yzlwcsts").Value = "控制器未在启动状态";
  return false;
}
if(flag == 2){
  Model.GetVarByName<TextInputVar>("input_yzlwcsts").Value = "请确认是否使用"+Model.CurrentInst.Name+"进行平面应变断裂韧度试验 预制疲劳裂纹试验？";
  return true;
}
return false;
25-09-11 14:30:47:283 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:30:47:284 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: \u0022\\u8BF7\\u786E\\u8BA4\\u662F\\u5426\\u4F7F\\u7528\\u8BD5\\u6837147\\u8FDB\\u884C\\u5E73\\u9762\\u5E94\\u53D8\\u65AD\\u88C2\\u97E7\\u5EA6\\u8BD5\\u9A8C \\u9884\\u5236\\u75B2\\u52B3\\u88C2\\u7EB9\\u8BD5\\u9A8C\\uFF1F\u0022,\r\n  \u0022Code\u0022: \u0022input_yzlwcsts\u0022,\r\n  \u0022Type\u0022: \u0022Text\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_38\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:30:47:291 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - RUN_SCRIPT_RSP
25-09-11 14:30:47:291 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"project_38","ScriptId":"025123b2-91be-44b6-9006-be3099c267ed","Result":true}
25-09-11 14:30:48:393 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:30:48:394 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_three\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_38\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:30:48:398 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 14:30:48:399 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_38","ProcessID":"project_38-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubtaskDialogBox-60715637-4d10-4965-aff6-da8166a38c78","MsgBody":{}}
25-09-11 14:30:48:399 DESKTOP-3BSREDP INFO [clj-scheduler.script:9] - 执行脚本:return Model.GetVarByName<BooleanInputVar>("input_three").Value;
25-09-11 14:30:48:420 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - RUN_SCRIPT_RSP
25-09-11 14:30:48:421 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"project_38","ScriptId":"25800e8e-a2b4-48a1-ac08-2e18c84a684f","Result":true}
25-09-11 14:30:48:449 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:30:48:450 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_yzlwbs\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_38\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:30:48:454 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 14:30:48:455 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_38","ProcessID":"project_38-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubTaskEvalScript-d676b27c-cb55-4186-b751-0d80789aef79","MsgBody":{}}
25-09-11 14:30:48:457 DESKTOP-3BSREDP INFO [clj-scheduler.script:9] - 执行脚本:var flag = Model.GetVarByName<BooleanInputVar>("input_yzlwcxksbs").Value;
//是否为重新开始
return flag;
25-09-11 14:30:48:478 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - RUN_SCRIPT_RSP
25-09-11 14:30:48:478 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"project_38","ScriptId":"37e05db2-a9ed-47b4-a8f7-453398cf38b2","Result":true}
25-09-11 14:30:48:503 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 14:30:48:503 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_38","ProcessID":"project_38-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubTaskEvalScript-9567e092-27d1-4397-87eb-144b0531a173","MsgBody":{}}
25-09-11 14:30:48:574 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:30:48:575 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 0,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 0,\r\n  \u0022Code\u0022: \u0022input_dqzszqs\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_38\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:30:48:578 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:30:48:579 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 0,\r\n  \u0022Dimension\u0022: \u0022dimension_cycle\u0022,\r\n  \u0022Unit\u0022: \u0022unit_cdw178e4a3\u0022,\r\n  \u0022DisplayUnitName\u0022: \u0022cycle\u0022,\r\n  \u0022DisplayValue\u0022: 0,\r\n  \u0022Code\u0022: \u0022input_starting_cycle\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_38\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:30:48:583 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 14:30:48:585 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_38","ProcessID":"project_38-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubTaskEvalScript-f3c6e835-6291-45be-bd63-350f12f45f9a","MsgBody":{}}
25-09-11 14:30:48:612 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 14:30:48:614 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_38","ProcessID":"project_38-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubTaskEvalScript-304442c2-62c7-4f92-bca4-5425989dd1cd","MsgBody":{}}
25-09-11 14:30:48:634 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 14:30:48:635 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_38","ProcessID":"project_38-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubTaskEvalScript-7d35a2c8-7abb-4dbb-a88d-93a7760edc8b","MsgBody":{}}
25-09-11 14:30:48:662 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 14:30:48:662 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_38","ProcessID":"project_38-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubTaskEvalScript-0d487418-fdc3-4d88-b707-10257acb369d","MsgBody":{}}
25-09-11 14:30:49:179 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - SUB_TASK_RSP
25-09-11 14:30:49:180 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"Type":"task-finished-msg","ClassName":"project_38","ProcessID":"project_38-ab56adb1-93c8-4ca0-a353-39b5c7a3ed16","SubTaskID":"SubTaskTimer-878d0189-0bb6-4c27-8cb5-42eecfa315b6","MsgBody":{}}
25-09-11 14:30:49:181 DESKTOP-3BSREDP INFO [clj-scheduler.script:9] - 执行脚本:
//终止kmax
double abort_kmax = Model.GetVarByName<NumberInputVar>("input_abort_kmax").Value;
//初始kmax
double start_kmax = Model.GetVarByName<NumberInputVar>("input_start_kmax").Value;
//力值比
double force_ratio = Model.GetVarByName<NumberInputVar>("input_force_ratio").Value;
//试验频率
double prefabricated_crack_frequency =  Model.GetVarByName<NumberInputVar>("input_prefabricated_crack_frequency").Value;
//预制循环周次
double prefabrication_cycle = Model.GetVarByName<NumberInputVar>("input_prefabrication_cycle").Value;
//起始循环周次
double starting_cycle = Model.GetVarByName<NumberInputVar>("input_starting_cycle").Value;
//到达均值速度
double average_speed_arrival = Model.GetVarByName<NumberInputVar>("input_average_speed_arrival").Value;
//预制裂纹长度
double precast_crack_length = Model.GetVarByName<NumberInputVar>("input_precast_crack_length").Value;
//线性拟合数据上限
double upper_limit_of_linear_fitting_data = Model.GetVarByName<NumberInputVar>("input_upper_limit_of_linear_fitting_data").Value;
//线性拟合数据下限
double lower_bound_of_linear_fitting_data = Model.GetVarByName<NumberInputVar>("input_lower_bound_of_linear_fitting_data").Value;
//未萌生裂纹循环
double undeveloped_crack_cycle = Model.GetVarByName<NumberInputVar>("input_undeveloped_crack_cycle").Value;
//Kmax增幅比
double increase_ratio_kmax = Model.GetVarByName<NumberInputVar>("input_increase_ratio_kmax").Value;

//获取下位机量程
double? NominalValue = 0.0; //标称值
double? MaxValue = 0.0; //最大值
double? MinValue = 0.0; //最小值
double? UpperSoftLimit = 0.0; //软件上限位值
double? LowerSoftLimit = 0.0; //软件下限位值
int? SoftLimitReaction = 0;  //软件限位响应类型
double? BasicTare = 0.0;  //基础长时清零
double? Tare = 0.0;   //内存清零
double? McFilterTime = 0.0;  // 显示滤波
double? CtrlFilterTime = 0.0;   //控制滤波
//传入轴号、传感器号
int? ret = Model.station.Ccss_ReadSensorPara(0, 1, ref NominalValue, ref MaxValue, ref MinValue, ref UpperSoftLimit,
ref LowerSoftLimit, ref SoftLimitReaction, ref BasicTare, ref Tare, ref McFilterTime, ref CtrlFilterTime);
//打印量程
FuncLibs.Logger.Error("传感器信息：");
FuncLibs.Logger.Error($"ret : {ret}, NominalValue: {NominalValue}, MaxValue: {MaxValue}, MinValue: {MinValue}, UpperSoftLimit : {UpperSoftLimit}, LowerSoftLimit: {LowerSoftLimit}, SoftLimitReaction: {SoftLimitReaction}, BasicTare: {BasicTare},Tare: {Tare}, McFilterTime: {McFilterTime}, CtrlFilterTime: {CtrlFilterTime}");

// 计算周期波所用的参数
//峰谷值
double forceMax = 0;
double forceMin = 0;
TestExpert.PlaneStrainFractureToughnessMethod.Commons.PlaneStrainFractureToughnessMethod
.GetForce( start_kmax,  precast_crack_length, out forceMax);
forceMin = forceMax * force_ratio;


Console.WriteLine($"预制计算出的最大力:{forceMax} ");

if(forceMax<MaxValue && forceMax>MinValue && forceMin<MaxValue && forceMin>MinValue && forceMax>forceMin){
  // 赋值峰谷值变量
  Model.GetVarByName<NumberInputVar>("input_yzlw_valley").Value = forceMin;
  Model.GetVarByName<NumberInputVar>("input_yzlw_peak").Value = forceMax;
}else{
  Model.GetVarByName<NumberInputVar>("input_yzlw_valley").Value = 0;
  Model.GetVarByName<NumberInputVar>("input_yzlw_peak").Value = 0;
  return false;
}


Console.WriteLine($"预制裂纹峰值:{Model.GetVarByName<NumberInputVar>("input_yzlw_peak").Value} ");
Console.WriteLine($"预制裂纹谷值:{Model.GetVarByName<NumberInputVar>("input_yzlw_valley").Value} ");

//赋值循环次数
Model.GetVarByName<NumberInputVar>("input_yzlwxhcs").Value = prefabrication_cycle - starting_cycle;

Model.iVariable["moniliewen"]=0;
Model.dVariableArray["tenLength"] = new double[10];

return true;
25-09-11 14:30:49:205 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:30:49:205 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 1.568002635231078,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 1.568002635231078,\r\n  \u0022Code\u0022: \u0022input_yzlw_valley\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_38\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:30:49:209 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:30:49:210 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 15.68002635231078,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 15.68002635231078,\r\n  \u0022Code\u0022: \u0022input_yzlw_peak\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_38\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:30:49:213 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:30:49:214 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 1000000,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 1000000,\r\n  \u0022Code\u0022: \u0022input_yzlwxhcs\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_38\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:30:49:219 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - RUN_SCRIPT_RSP
25-09-11 14:30:49:220 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"project_38","ScriptId":"364181e4-56f2-4819-a977-1e61e2a16a5d","Result":true}
25-09-11 14:30:49:227 DESKTOP-3BSREDP INFO [clj-scheduler.script:9] - 执行脚本:return true;
25-09-11 14:30:49:373 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - RUN_SCRIPT_RSP
25-09-11 14:30:49:374 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"ProcessId":"project_38","ScriptId":"5407ca4c-4e61-4a47-9c35-9563097b5463","Result":true}
25-09-11 14:30:49:384 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:30:49:384 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Value\u0022: true,\r\n  \u0022Code\u0022: \u0022input_one\u0022,\r\n  \u0022Type\u0022: \u0022Boolean\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_38\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:30:53:214 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:30:53:215 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 15.11165088785825,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 15.11165088785825,\r\n  \u0022Code\u0022: \u0022input_allKmax\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_38\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:30:54:123 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:30:54:123 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 19.287011527464525,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 19.287011527464525,\r\n  \u0022Code\u0022: \u0022input_allKmax\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_38\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:30:54:825 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:30:54:827 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 24.97584148635491,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 24.97584148635491,\r\n  \u0022Code\u0022: \u0022input_allKmax\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_38\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:30:55:490 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:30:55:491 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 33.891597436808524,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 33.891597436808524,\r\n  \u0022Code\u0022: \u0022input_allKmax\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_38\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:30:58:222 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:30:58:224 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 49.97722700836602,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 49.97722700836602,\r\n  \u0022Code\u0022: \u0022input_allKmax\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_38\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:30:58:339 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:30:58:341 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 84.46280537717269,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 84.46280537717269,\r\n  \u0022Code\u0022: \u0022input_allKmax\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_38\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:30:58:454 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:30:58:455 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 184.24547543994308,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 184.24547543994308,\r\n  \u0022Code\u0022: \u0022input_allKmax\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_38\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
25-09-11 14:30:58:631 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - VAR_MODIFIED
25-09-11 14:30:58:632 DESKTOP-3BSREDP INFO [clj-scheduler.mq:62] - {"modifiedVar":"{\r\n  \u0022Min\u0022: -1,\r\n  \u0022Max\u0022: -1,\r\n  \u0022Value\u0022: 870.1947192045508,\r\n  \u0022Dimension\u0022: null,\r\n  \u0022Unit\u0022: null,\r\n  \u0022DisplayUnitName\u0022: null,\r\n  \u0022DisplayValue\u0022: 870.1947192045508,\r\n  \u0022Code\u0022: \u0022input_allKmax\u0022,\r\n  \u0022Type\u0022: \u0022Number\u0022,\r\n  \u0022IsConstant\u0022: false,\r\n  \u0022IsOverall\u0022: false,\r\n  \u0022_IsCheck\u0022: false,\r\n  \u0022IsCheck\u0022: false,\r\n  \u0022Mode\u0022: null,\r\n  \u0022TemplateName\u0022: \u0022project_38\u0022,\r\n  \u0022InstCode\u0022: \u0022sample_14785d372\u0022,\r\n  \u0022ActionId\u0022: null,\r\n  \u0022SubTaskId\u0022: null,\r\n  \u0022ContactInputCode\u0022: \u0022\u0022\r\n}","options":{"SaveDB":true,"SendToUI":true,"Type":"input"}}
