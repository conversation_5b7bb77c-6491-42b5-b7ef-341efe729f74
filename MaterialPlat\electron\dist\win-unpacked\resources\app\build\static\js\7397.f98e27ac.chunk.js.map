{"version": 3, "file": "static/js/7397.f98e27ac.chunk.js", "mappings": "sJAAO,MAAMA,EAAiB,CAC1BC,MAAO,CACHC,UAAW,GACXC,WAAW,EACXC,aAAa,EACbC,gBAAgB,EAChBC,cAAe,QAEnBC,gBAAiB,CACbC,cAAe,SACfC,eAAgB,QAChBC,WAAY,GACZC,IAAK,OACLC,MAAO,OACPC,OAAQ,OACRC,YAAa,MACbC,YAAa,QAEbC,WAAY,OACZC,cAAe,OACfC,YAAa,OACbC,aAAc,OACdC,YAAa,IAEjBC,SAAU,CACNC,MAAO,KACPC,QAAS,M,+HCxBV,MAAMC,EAAiBC,EAAAA,GAAOC,GAAG;;;;;;;;;;;;;;;;;;;;;;;;EA0B3BC,EAAeF,EAAAA,GAAOC,GAAG;;;;;;;;;;;EAgCzBE,GAnBwBH,EAAAA,GAAOC,GAAG;;;;;;;;;;;;;;;;;EAmBjBD,EAAAA,GAAOC,GAAG;;;;;;;;;;;kBCtDxC,MAkFA,EAlFeG,IAyBR,IAxBHC,QACI7B,OAAO,UACHC,EAAS,UACTC,GAAY,EAAK,YACjBC,GAAc,EAAK,eACnBC,GAAiB,EAAK,cACtBC,EAAgB,QAChB,CAAC,EACLC,iBAAiB,cACbC,EAAa,eACbC,EAAc,WACdC,EAAU,IACVC,EAAG,YACHG,EAAW,YACXC,EAAW,YACXgB,EAAW,WACXf,EAAahB,EAAAA,EAAeO,gBAAgBS,WAAU,cACtDC,EAAgBjB,EAAAA,EAAeO,gBAAgBU,cAAa,YAC5DC,EAAclB,EAAAA,EAAeO,gBAAgBW,YAAW,aACxDC,EAAenB,EAAAA,EAAeO,gBAAgBY,aAAY,YAC1DC,GACA,CAAC,GACL,CAAC,EAAC,SACNY,GACHH,EACG,MAAMI,EAAa,CACfC,WAAY/B,EAAY,OAAS,UACjCgC,UAAW/B,EAAc,SAAW,UACpCgC,SAAU9B,GAAiB,QAG/B,OACI+B,EAAAA,EAAAA,MAACV,EAAY,CACTW,MAAO,CACH9B,gBACAC,iBACAC,aACAC,MACAG,cACAC,cACAgB,cACAf,aACAC,gBACAC,cACAC,eACAC,cACAR,MAAO,OACPC,OAAQ,UACJR,EAAiB,CACjBkC,QAAS,QACTC,SAAU,QACV,CAAC,GACPR,SAAA,CAIE9B,IACIuC,EAAAA,EAAAA,KAAA,OAAKC,UAAU,kBAAkBJ,MAAOL,EAAWD,SAC9C9B,IAOT8B,GAAgC,IAApBA,EAASW,OACfC,EAAAA,SAASC,IAAIb,GAAUc,GAEjBA,KAIJL,EAAAA,EAAAA,KAACb,EAAc,CAAAI,SAAC,mEAKjB,ECxEjBe,GAAUC,EAAAA,EAAAA,OAAK,IAAM,8EA8G3B,EA5GcnB,IAEP,IAADoB,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAA,IAFS,KACXC,EAAI,GAAEC,EAAE,aAAEC,EAAY,SAAE1B,GAC3BH,EAEG,MAAO8B,EAAMC,IAAWC,EAAAA,EAAAA,WAAS,IAE1B/B,EAAQgC,IAAaD,EAAAA,EAAAA,YAGtBE,GAAYC,EAAAA,EAAAA,GACR,OAANlC,QAAM,IAANA,GAAgB,QAAVmB,EAANnB,EAAQT,gBAAQ,IAAA4B,GAAS,QAATC,EAAhBD,EAAkB1B,eAAO,IAAA2B,OAAnB,EAANA,EAA2Be,MAC3B,GAIEC,GAAUF,EAAAA,EAAAA,GACN,OAANlC,QAAM,IAANA,GAAgB,QAAVqB,EAANrB,EAAQT,gBAAQ,IAAA8B,GAAU,QAAVC,EAAhBD,EAAkBgB,gBAAQ,IAAAf,OAApB,EAANA,EAA4Ba,MAC5B,GA2BJ,OAvBAG,EAAAA,EAAAA,YAAU,KACN,IACI,GAAQ,OAAJZ,QAAI,IAAJA,GAAAA,EAAMa,YAAa,CACnB,MAAM,YAAEC,GAAgBC,KAAKC,MAAU,OAAJhB,QAAI,IAAJA,OAAI,EAAJA,EAAMa,aACpCI,IAAQH,EAAaxC,KAEhB,eAAgBwC,EAAY/D,kBAC9B+D,EAAY/D,gBAAgBS,WAAasD,EAAY/D,gBAAgBmE,QACrEJ,EAAY/D,gBAAgBU,cAAgBqD,EAAY/D,gBAAgBmE,QACxEJ,EAAY/D,gBAAgBW,YAAcoD,EAAY/D,gBAAgBmE,QACtEJ,EAAY/D,gBAAgBY,aAAemD,EAAY/D,gBAAgBmE,SAE3EZ,EAAUQ,GAElB,MACIR,EAAU9D,EAAAA,EAElB,CAAE,MAAO2E,GACLb,EAAU9D,EAAAA,GACV4E,QAAQC,IAAI,MAAOF,EACvB,IACD,CAAK,OAAJnB,QAAI,IAAJA,OAAI,EAAJA,EAAMa,eAGN5B,EAAAA,EAAAA,KAAAqC,EAAAA,SAAA,CAAA9C,UACIK,EAAAA,EAAAA,MAACb,EAAc,CACXiC,GAAIA,EACJnB,MAAO,CACH1B,MAAa,OAANkB,QAAM,IAANA,GAAuB,QAAjBuB,EAANvB,EAAQvB,uBAAe,IAAA8C,OAAjB,EAANA,EAAyBzC,MAChCC,OAAc,OAANiB,QAAM,IAANA,GAAuB,QAAjBwB,EAANxB,EAAQvB,uBAAe,IAAA+C,OAAjB,EAANA,EAAyBzC,QACnCmB,SAAA,EAGGkC,IACGzB,EAAAA,EAAAA,KAAA,OACIC,UAAU,aACVJ,MAAO,CACHyC,WAAY,cAAoB,OAANjD,QAAM,IAANA,GAAuB,QAAjByB,EAANzB,EAAQvB,uBAAe,IAAAgD,OAAjB,EAANA,EAAyBnC,kBAM9D2C,GAAajC,IACVW,EAAAA,EAAAA,KAACuC,EAAM,CACHlD,OAAQA,EAAOE,SAEdA,KAKbS,EAAAA,EAAAA,KAACwC,EAAAA,SAAQ,CAACC,UAAUzC,EAAAA,EAAAA,KAAAqC,EAAAA,SAAA,IAAM9C,SAElB2B,IACIlB,EAAAA,EAAAA,KAACM,EAAO,CACJY,KAAMA,EACNC,QAASA,EACTF,aAAcA,EACdI,UAAWA,EACXN,KAAMA,MAOlBU,IACIzB,EAAAA,EAAAA,KAAC0C,EAAAA,EAAW,CACRC,MAAO3B,EACPC,aAAcA,EAAa1B,UAE3BS,EAAAA,EAAAA,KAAA,OACIC,UAAU,iBACV2C,QAASA,IAAMzB,GAAQ,GAAM5B,SAChC,+CAQlB,C", "sources": ["module/layout/controlComp/lib/Block/constants.js", "module/layout/controlComp/lib/Block/style.js", "module/layout/controlComp/lib/Block/render/index.js", "module/layout/controlComp/lib/Block/index.js"], "names": ["DEFAULT_CONFIG", "props", "titleText", "titleBold", "titleItalic", "isUseScrollbar", "titleFontSize", "style2Container", "flexDirection", "justifyContent", "alignItems", "gap", "width", "height", "borderWidth", "borderStyle", "paddingTop", "paddingBottom", "paddingLeft", "paddingRight", "maskOpacity", "variable", "value", "visible", "BlockContainer", "styled", "div", "BlockContent", "EmptyContainer", "_ref", "config", "borderColor", "children", "titleStyle", "fontWeight", "fontStyle", "fontSize", "_jsxs", "style", "display", "overflow", "_jsx", "className", "length", "Children", "map", "child", "Setting", "lazy", "_config$variable", "_config$variable$visi", "_config$variable2", "_config$variable2$dis", "_config$style2Contain", "_config$style2Contain2", "_config$style2Contain3", "item", "id", "layoutConfig", "open", "<PERSON><PERSON><PERSON>", "useState", "setConfig", "isVisible", "useInputVariableValueByCode", "code", "isAbled", "disabled", "useEffect", "data_source", "comp_config", "JSON", "parse", "isEqual", "padding", "error", "console", "log", "_Fragment", "background", "Render", "Suspense", "fallback", "ContextMenu", "domId", "onClick"], "sourceRoot": ""}