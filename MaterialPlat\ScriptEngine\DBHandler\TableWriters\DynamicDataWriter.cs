using System.Data;
using Dapper;
using Microsoft.Data.Sqlite;
using static Logging.CCSSLogger;
namespace ScriptEngine.DBHandler.TableWriters;

public record DynamicDataTable(string TableName, string[] Fields);

/// <summary>
/// 动态数据
///   当前值包含基础字段, 如果后续需要查询具体数据再增加
/// </summary>
public record DynamicData(int Id, int Index, int CreateTime);

/// <summary>
/// 动态数据表Writer
///     Writer中的字段在实例化后不可修改, 如果要修改Writer中的字段需要重新实例化对象
/// </summary>
public class DynamicDataWriter : TableWriter<Dictionary<string, double>>
{
    protected sealed override string Sql { get; set; }
    protected sealed override int MaximumCount { get; set; }
    //computer_time计算机时间
    private List<string> Fields { get; set; } = new() { "index", "create_time", "computer_time" };
    
    // 预编译的 SQL 命令缓存
    private SqliteCommand? _preparedCommand;
    private readonly object _commandLock = new object();
    private string? _cachedSql; // 缓存的 SQL 字符串，用于检测变化

    public DynamicDataWriter(string tableName, string[] fields) : base(tableName)
    {
        MaximumCount = 10000;
        Fields.AddRange(fields);
        Sql = $@"INSERT INTO {tableName} ({string.Join(", ", Fields.Select(s => $"`{s}`"))})
                    VALUES  ({string.Join(", ", Fields.Select(s => "@" + s))})";
    }

    public DynamicDataWriter(DynamicDataTable table) : this(table.TableName, table.Fields)
    {

    }
    /// <summary>
    /// 删除后N周期的重复数据
    /// </summary>
    /// <param name="connection"></param>
    /// <param name="cycleCode"></param>
    /// <param name="latestCycle"></param>
    public void RemoveLast(IDbConnection connection, string cycleCode, double latestCycle)
    {
        lock (connection)
        {
            using var transaction = connection.BeginTransaction();
            var sql = $"DELETE FROM {TableName} WHERE {cycleCode} >= {latestCycle}";
            connection.Execute(sql);
            transaction.Commit();
        }
    }
    public override void Write(IDbConnection connection)
    {
        var count = Queue.Count;
        if (IsDisposed || count == 0) return;

        // 确保连接是 SqliteConnection 类型
        if (connection is not SqliteConnection sqliteConnection)
        {
            // 如果不是 SqliteConnection，回退到原始实现
            WriteWithDapper(connection);
            return;
        }

        try
        {
            WriteWithPreparedStatement(sqliteConnection);
        }
        catch (Exception ex) when (ex.Message.Contains("no column named computer_time"))
        {
            // 处理缺少 computer_time 列的情况
            var alterTableSql = $"ALTER TABLE {TableName} ADD COLUMN `computer_time` TEXT NULL;";
            connection.Execute(alterTableSql);
            WriteWithPreparedStatement(sqliteConnection);
        }
        catch (Exception ex)
        {
            Logger.Error($"预编译语句插入数据库错误：{ex}");
            Logger.Error($"SQL语句：{Sql}");
            Logger.Error($"异常详情：{ex.Message}");
            Logger.Error($"堆栈跟踪：{ex.StackTrace}");
            // 回退到 Dapper 实现
            WriteWithDapper(connection);
        }
    }

    /// <summary>
    /// 使用预编译语句的写入方法
    /// </summary>
    private void WriteWithPreparedStatement(SqliteConnection connection)
    {
        var count = Queue.Count;
        var batchSize = Math.Min(count, MaximumCount);
        
        lock (_commandLock)
        {
            // 初始化或重用预编译命令
            if (_preparedCommand == null || 
                _preparedCommand.Connection != connection || 
                _cachedSql != Sql)
            {
                _preparedCommand?.Dispose();
                _preparedCommand = new SqliteCommand(Sql, connection);
                _cachedSql = Sql; // 缓存当前的 SQL
                
                // 添加参数占位符
                foreach (var field in Fields)
                {
                    SqliteParameter parameter;
                    switch (field)
                    {
                        case "computer_time":
                            parameter = new SqliteParameter("@" + field, SqliteType.Text);
                            break;
                        case "index":
                        case "create_time":
                            parameter = new SqliteParameter("@" + field, SqliteType.Integer);
                            break;
                       
                        default:
                            // 其他字段默认为数值类型
                            parameter = new SqliteParameter("@" + field, SqliteType.Real);
                            break;
                    }
                    _preparedCommand.Parameters.Add(parameter);
                }
                
                // 验证参数数量是否匹配
                var expectedParamCount = Sql.Count(c => c == '@');
                var actualParamCount = _preparedCommand.Parameters.Count;
                if (expectedParamCount != actualParamCount)
                {
                    Logger.Error($"参数数量不匹配：SQL中有 {expectedParamCount} 个参数，但添加了 {actualParamCount} 个参数");
                    Logger.Error($"SQL: {Sql}");
                    Logger.Error($"Fields: {string.Join(", ", Fields)}");
                }
                
                // 预编译语句
                _preparedCommand.Prepare();
            }

            using var transaction = connection.BeginTransaction();
            _preparedCommand.Transaction = transaction;

            try
            {
                for (var i = 0; i < batchSize; i++)
                {
                    if (Queue.TryDequeue(out var data))
                    {
                        // 更新参数值
                        foreach (var field in Fields)
                        {
                            var parameter = _preparedCommand.Parameters["@" + field];
                            if (field == "computer_time")
                            {
                                parameter.Value = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                            }
                            else if (data.TryGetValue(field, out double value))
                            {
                                if (double.IsNaN(value))
                                {
                                    parameter.Value = DBNull.Value;
                                }
                                else
                                {
                                    // 根据字段类型设置相应的值
                                    switch (field)
                                    {
                                        case "index":
                                        case "create_time":
                                        default:
                                            parameter.Value = value;
                                            break;
                                    }
                                }
                            }
                            else
                            {
                                parameter.Value = DBNull.Value;
                            }
                        }
                        
                        // 执行预编译语句
                        _preparedCommand.ExecuteNonQuery();
                    }
                }
                
                transaction.Commit();
            }
            catch (Exception ex)
            {
                Logger.Error($"预编译语句执行过程中出错：{ex.Message}");
                Logger.Error($"SQL: {Sql}");
                
                // 记录当前参数值以便调试
                if (_preparedCommand?.Parameters != null)
                {
                    var paramValues = string.Join(", ", 
                        _preparedCommand.Parameters.Cast<SqliteParameter>()
                            .Select(p => $"{p.ParameterName}={p.Value ?? "NULL"}"));
                    Logger.Error($"Parameters: {paramValues}");
                }
                
                transaction.Rollback();
                throw;
            }
        }
    }

    /// <summary>
    /// 使用 Dapper 的原始写入方法作为回退
    /// </summary>
    private void WriteWithDapper(IDbConnection connection)
    {
        var count = Queue.Count;
        if (count == 0) return;
        
        var dbData = new List<DynamicParameters>();
        for (var i = 0; i < Math.Min(count, MaximumCount); i++)
        {
            if (Queue.TryDequeue(out var data))
            {
                var parameters = new DynamicParameters();
                foreach (var field in Fields)
                {
                    if (data.TryGetValue(field, out double value))
                    {
                        parameters.Add(field, double.IsNaN(value) ? (double?)null : value);
                    }
                    else
                    {
                        parameters.Add(field, null);
                    }
                }
                parameters.Add("computer_time", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
                dbData.Add(parameters);
            }
        }

        using var transaction = connection.BeginTransaction();
        lock (connection)
        {
            try
            {
                connection.Execute(Sql, dbData, transaction);
                transaction.Commit();
            }
            catch (Exception ex) when (ex.Message.Contains("no column named computer_time"))
            {
                var alterTableSql = $"ALTER TABLE {TableName} ADD COLUMN `computer_time` TEXT NULL;";
                connection.Execute(alterTableSql, null, transaction);
                connection.Execute(Sql, dbData, transaction);
                transaction.Commit();
            }
            catch (Exception ex)
            {
                Logger.Error($"插入数据库错误：{ex}");
                Logger.Error($"SQL语句：{Sql}");
                foreach (var paramSet in dbData)
                {
                    var parameters = new Dictionary<string, object>();
                    foreach (string name in paramSet.ParameterNames)
                    {
                        object value = paramSet.Get<object>(name);
                        parameters[name] = value ?? "null";
                    }
                    Logger.Error($"Parameters: {string.Join(", ", parameters.Select(kvp => $"{kvp.Key}={kvp.Value}"))}");
                }
                transaction.Rollback();
                throw;
            }
        }
    }

    /// <summary>
    /// 释放资源，包括预编译的命令
    /// </summary>
    public new void Dispose()
    {
        lock (_commandLock)
        {
            _preparedCommand?.Dispose();
            _preparedCommand = null;
            _cachedSql = null;
        }
        base.Dispose();
    }
}
