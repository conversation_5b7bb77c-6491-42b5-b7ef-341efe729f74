"use strict";(self.webpackChunkzhongji_experiment_react=self.webpackChunkzhongji_experiment_react||[]).push([[910],{90910:(e,n,r)=>{r.r(n),r.d(n,{default:()=>o});var s=r(65043),a=r(72535),t=r(93950),d=r.n(t),i=r(70579);const o=e=>{let{sizes:n,onDragEnd:r,id:t,render:o,onDrag:l,disabled:u=!1,...c}=e;const[f,g]=(0,s.useState)(n);(0,s.useEffect)((()=>{g(n)}),[n]);(0,s.useCallback)(d()(((e,n,r)=>l(e,n,r)),500),[]);return(0,i.jsx)(a.A,{id:`hor-${t}`,minSize:25,snapOffset:0,render:o,onDragEnd:u?void 0:(e,n)=>{r(e,n)},onDrag:u?void 0:(e,n,r)=>{g(r),l(e,n,r)},gridTemplateColumns:f,disabled:u,...c},`hor-${t}`)}}}]);
//# sourceMappingURL=910.d13aea6f.chunk.js.map