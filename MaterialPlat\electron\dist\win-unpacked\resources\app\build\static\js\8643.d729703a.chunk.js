(self.webpackChunkzhongji_experiment_react=self.webpackChunkzhongji_experiment_react||[]).push([[8643],{55518:(e,a,l)=>{"use strict";l.d(a,{A:()=>D});var n=l(65043),t=l(74117),i=l(56543),d=l(81143),r=l(68374),o=l(18650);const s=0,c="left";var u=l(70579);const h=d.Ay.div`
    width: ${(0,r.D0)("20px")};
    height: ${(0,r.D0)("20px")};
    background-size: ${(0,r.D0)("20px")} ${(0,r.D0)("20px")};
    background-image: url(${e=>{let{isConstant:a}=e;return a?o.fd:o.Mo}});
`,v=e=>{let{variable:a,onChange:l,disabled:n}=e;const{default_val:t,is_fx:i}=a;return!i||n?(0,u.jsx)(u.Fragment,{}):(0,u.jsx)(h,{isConstant:t.isConstant===s,onClick:()=>{l({...a,default_val:{...t,isConstant:0===(null===t||void 0===t?void 0:t.isConstant)?1:0}})}})};var _=l(95206),b=l(34154),m=l(67208),p=l(16090),f=l(36497),g=l(29977);const x=e=>{var a;let{disabled:l,variable:t,handleChange:i}=e;const d=(0,g.A)(),r=(0,n.useMemo)((()=>(null===d||void 0===d?void 0:d.filter((e=>e.variable_type===t.variable_type&&e.id!==t.id))).map((e=>({...e,labelName:`${e.name}(${e.code})`})))),[d,t]);return(0,u.jsx)(f.A,{showSearch:!0,optionFilterProp:"labelName",disabled:l,fieldNames:{label:"labelName",value:"id"},className:"input-width",value:null===t||void 0===t||null===(a=t.default_val)||void 0===a?void 0:a.variable_id,options:r,onChange:(e,a)=>i(a)})},Y=e=>{let{disabled:a,content:l,buttonType:t,actionId:d,script:r}=e;const[o,s]=(0,n.useState)(!1),{startAction:c}=(0,p.A)(),h=()=>{t!==b.NR.\u52a8\u4f5c?t!==b.NR.\u811a\u672c?console.log("\u672a\u8bbe\u7f6e\u70b9\u51fb\u89e6\u53d1\u4e8b\u4ef6"):(async()=>{try{s(!0),await(0,m.O5k)({script:r,result_type:i.Jt.BOOL})}catch(e){console.log("err when handlesSubmitScript",e)}finally{s(!1)}})():(async()=>{try{d&&(s(!0),await c({action_id:d}))}catch(e){console.log("err when handleSubmitAction",e)}finally{s(!1)}})()};return(0,u.jsx)(_.Ay,{loading:o,disabled:a,className:"button-width",onClick:()=>h(),children:l})},w=d.Ay.div`
    display: flex;
    flex-direction: ${e=>{let{isLeft:a}=e;return a?"row":"row-reverse"}};
    gap: 8px;
    overflow: hidden;

    .button-width {
        width: ${(0,r.D0)("80px")};
        pointer-events: auto;
    }
`,y=e=>{let{disabled:a,variable:l,render:n,onChange:t,buttonShow:i}=e;const{button_variable_tab:d,default_val:r}=l;return(0,u.jsx)(w,{isLeft:(null===d||void 0===d?void 0:d.position)===c,children:1===r.isConstant?(0,u.jsx)(x,{disabled:a,variable:l,handleChange:e=>{t({...l,default_val:{...r,variable_id:null===e||void 0===e?void 0:e.id,variable_code:null===e||void 0===e?void 0:e.code}})}}):(0,u.jsxs)(u.Fragment,{children:[i&&(null===d||void 0===d?void 0:d.isEnable)&&(0,u.jsx)(Y,{...d,disabled:a}),n()]})})};var C=l(12624),j=l(32513);const k=e=>{let{variable:a,disabled:l=!1,onChange:n,usableShowType:t="checkbox"}=e;return null!==a&&void 0!==a&&a.is_enable?"switch"===t?(0,u.jsx)(C.A,{disabled:l,checked:null===a||void 0===a?void 0:a.is_feature,onChange:e=>{n({...a,is_feature:e})}}):(0,u.jsx)(j.A,{disabled:l,checked:null===a||void 0===a?void 0:a.is_feature,onChange:e=>{n({...a,is_feature:e.target.checked})}}):(0,u.jsx)(u.Fragment,{})},S=d.Ay.div`
    .input-render-left{
        display: inline-block;
        overflow: hidden;
        &>div{
            display: flex;
            gap: 8px;
            align-items: center;
        }
    }

    .input-render-right{
        float: right;
        display: flex;
        align-items: center;
        gap: 8px;
        max-width: 100%;
        overflow: hidden;
    }
`,D=e=>{let{variable:a,disabled:l=!1,onChange:n,render:d,usableShow:r=!0,buttonShow:o=!0,fxShow:s=!0,nameShow:c=!0,usableShowType:h}=e;const{t:_}=(0,t.Bd)(),b=l||(a.variable_type===i.ps.\u5e03\u5c14\u578b?(null===a||void 0===a?void 0:a.is_enable)&&(null===a||void 0===a?void 0:a.is_feature):(null===a||void 0===a?void 0:a.is_enable)&&!(null!==a&&void 0!==a&&a.is_feature));return(0,u.jsxs)(S,{children:[(r||c)&&(0,u.jsx)("div",{className:"input-render-left",children:(0,u.jsxs)("div",{children:[r&&(0,u.jsx)(k,{variable:a,disabled:l,onChange:n,usableShowType:h}),c&&(0,u.jsx)("div",{className:"variable_name",children:_(a.name)})]})}),(0,u.jsxs)("div",{className:"input-render-right",children:[s&&(0,u.jsx)(v,{variable:a,onChange:n,disabled:b}),(0,u.jsx)(y,{disabled:b,variable:a,onChange:n,buttonShow:o,render:()=>d({innerDisabled:b})})]})]})}},76484:function(e,a,l){e.exports=function(e){"use strict";function a(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var l=a(e),n={name:"zh-cn",weekdays:"\u661f\u671f\u65e5_\u661f\u671f\u4e00_\u661f\u671f\u4e8c_\u661f\u671f\u4e09_\u661f\u671f\u56db_\u661f\u671f\u4e94_\u661f\u671f\u516d".split("_"),weekdaysShort:"\u5468\u65e5_\u5468\u4e00_\u5468\u4e8c_\u5468\u4e09_\u5468\u56db_\u5468\u4e94_\u5468\u516d".split("_"),weekdaysMin:"\u65e5_\u4e00_\u4e8c_\u4e09_\u56db_\u4e94_\u516d".split("_"),months:"\u4e00\u6708_\u4e8c\u6708_\u4e09\u6708_\u56db\u6708_\u4e94\u6708_\u516d\u6708_\u4e03\u6708_\u516b\u6708_\u4e5d\u6708_\u5341\u6708_\u5341\u4e00\u6708_\u5341\u4e8c\u6708".split("_"),monthsShort:"1\u6708_2\u6708_3\u6708_4\u6708_5\u6708_6\u6708_7\u6708_8\u6708_9\u6708_10\u6708_11\u6708_12\u6708".split("_"),ordinal:function(e,a){return"W"===a?e+"\u5468":e+"\u65e5"},weekStart:1,yearStart:4,formats:{LT:"HH:mm",LTS:"HH:mm:ss",L:"YYYY/MM/DD",LL:"YYYY\u5e74M\u6708D\u65e5",LLL:"YYYY\u5e74M\u6708D\u65e5Ah\u70b9mm\u5206",LLLL:"YYYY\u5e74M\u6708D\u65e5ddddAh\u70b9mm\u5206",l:"YYYY/M/D",ll:"YYYY\u5e74M\u6708D\u65e5",lll:"YYYY\u5e74M\u6708D\u65e5 HH:mm",llll:"YYYY\u5e74M\u6708D\u65e5dddd HH:mm"},relativeTime:{future:"%s\u5185",past:"%s\u524d",s:"\u51e0\u79d2",m:"1 \u5206\u949f",mm:"%d \u5206\u949f",h:"1 \u5c0f\u65f6",hh:"%d \u5c0f\u65f6",d:"1 \u5929",dd:"%d \u5929",M:"1 \u4e2a\u6708",MM:"%d \u4e2a\u6708",y:"1 \u5e74",yy:"%d \u5e74"},meridiem:function(e,a){var l=100*e+a;return l<600?"\u51cc\u6668":l<900?"\u65e9\u4e0a":l<1100?"\u4e0a\u5348":l<1300?"\u4e2d\u5348":l<1800?"\u4e0b\u5348":"\u665a\u4e0a"}};return l.default.locale(n,null,!0),n}(l(60446))},88643:(e,a,l)=>{"use strict";l.r(a),l.d(a,{default:()=>m});l(65043);var n=l(19372),t=l(60446),i=l.n(t),d=(l(76484),l(89379)),r=l(35539);const o=(0,d.A)((0,d.A)({},r.I),{},{locale:"zh_CN",today:"\u4eca\u5929",now:"\u6b64\u523b",backToToday:"\u8fd4\u56de\u4eca\u5929",ok:"\u786e\u5b9a",timeSelect:"\u9009\u62e9\u65f6\u95f4",dateSelect:"\u9009\u62e9\u65e5\u671f",weekSelect:"\u9009\u62e9\u5468",clear:"\u6e05\u9664",week:"\u5468",month:"\u6708",year:"\u5e74",previousMonth:"\u4e0a\u4e2a\u6708 (\u7ffb\u9875\u4e0a\u952e)",nextMonth:"\u4e0b\u4e2a\u6708 (\u7ffb\u9875\u4e0b\u952e)",monthSelect:"\u9009\u62e9\u6708\u4efd",yearSelect:"\u9009\u62e9\u5e74\u4efd",decadeSelect:"\u9009\u62e9\u5e74\u4ee3",previousYear:"\u4e0a\u4e00\u5e74 (Control\u952e\u52a0\u5de6\u65b9\u5411\u952e)",nextYear:"\u4e0b\u4e00\u5e74 (Control\u952e\u52a0\u53f3\u65b9\u5411\u952e)",previousDecade:"\u4e0a\u4e00\u5e74\u4ee3",nextDecade:"\u4e0b\u4e00\u5e74\u4ee3",previousCentury:"\u4e0a\u4e00\u4e16\u7eaa",nextCentury:"\u4e0b\u4e00\u4e16\u7eaa",yearFormat:"YYYY\u5e74",cellDateFormat:"D",monthBeforeYear:!1}),s={placeholder:"\u8bf7\u9009\u62e9\u65f6\u95f4",rangePlaceholder:["\u5f00\u59cb\u65f6\u95f4","\u7ed3\u675f\u65f6\u95f4"]},c={lang:Object.assign({placeholder:"\u8bf7\u9009\u62e9\u65e5\u671f",yearPlaceholder:"\u8bf7\u9009\u62e9\u5e74\u4efd",quarterPlaceholder:"\u8bf7\u9009\u62e9\u5b63\u5ea6",monthPlaceholder:"\u8bf7\u9009\u62e9\u6708\u4efd",weekPlaceholder:"\u8bf7\u9009\u62e9\u5468",rangePlaceholder:["\u5f00\u59cb\u65e5\u671f","\u7ed3\u675f\u65e5\u671f"],rangeYearPlaceholder:["\u5f00\u59cb\u5e74\u4efd","\u7ed3\u675f\u5e74\u4efd"],rangeMonthPlaceholder:["\u5f00\u59cb\u6708\u4efd","\u7ed3\u675f\u6708\u4efd"],rangeQuarterPlaceholder:["\u5f00\u59cb\u5b63\u5ea6","\u7ed3\u675f\u5b63\u5ea6"],rangeWeekPlaceholder:["\u5f00\u59cb\u5468","\u7ed3\u675f\u5468"]},o),timePickerLocale:Object.assign({},s)};c.lang.ok="\u786e\u5b9a";const u=c;var h=l(55518),v=l(70579);const _="YYYY-MM-DD HH:mm:ss",b=e=>{let{onChange:a,disabled:l,variable:t}=e;const{value:d}=t.default_val;return(0,v.jsx)(n.A,{value:d?i()(d,_):null,onChange:(e,l)=>{a({...null===t||void 0===t?void 0:t.default_val,value:l})},showTime:!0,locale:u,disabled:l,className:"date-width",format:_})},m=e=>{let{variable:a,disabled:l,onChange:n}=e;return(0,v.jsx)(h.A,{variable:a,disabled:l,onChange:n,render:e=>{let{innerDisabled:l}=e;return(0,v.jsx)(b,{variable:a,disabled:l,onChange:e=>{n({...a,default_val:e})}})}})}}}]);
//# sourceMappingURL=8643.d729703a.chunk.js.map