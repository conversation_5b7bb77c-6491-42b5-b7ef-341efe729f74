{"version": 3, "file": "static/js/3056.5b0d5d45.chunk.js", "mappings": "0VAWA,MAoEA,EApEeA,IAqBR,IApBHC,QACIC,MAAM,UACFC,EAAS,WACTC,EAAU,MACVC,EAAK,YACLC,EAAW,UACXC,EAAS,cACTC,EAAa,YACbC,GACA,CAAC,EACLC,UAAU,MACNC,EAAK,QACLC,EAAO,SACPC,GACA,CAAC,EACLC,OAAO,OACHC,GACA,CAAC,GACL,CAAC,EAAC,kBACNC,GACHhB,EACG,MAAM,QAAEiB,IAAYC,EAAAA,EAAAA,KAEdC,GAAWC,EAAAA,EAAAA,GAA4B,OAALT,QAAK,IAALA,OAAK,EAALA,EAAOU,KCtBb,eAAC,cACnCC,GACHC,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,CAAC,EAAC,MAAM,CAGRG,KAAM,GAENL,KAAM,GAENM,eAAgB,GAEhBC,YAAa,GAEbC,GAAI,GAEJP,cAA4B,OAAbA,QAAa,IAAbA,EAAAA,EAAiB,SAEhCQ,YAAa,CAETnB,MAAO,EAEPoB,WAAY,EAEZC,WAAY,SAEZC,SAAU,KAEVC,KAAM,MAGVC,UAAW,GAEXC,SAAU,GAEVC,IAAK,GAELC,UAAW,EAEXC,WAAY,EAEZC,WAAY,EAEZC,MAAO,EAGPC,WAAY,CAERC,OAAQ,CAEJC,cAAe,MAEfC,WAAY,OAEZC,WAAY,KAEZC,YAAa,KAEbC,kBAAmB,EAEnBC,kBAAmB,EAEnBC,cAAe,EAEfC,UAAW,EAEXC,WAAY,EAEZC,WAAY,EAEZC,WAAY,EAEZC,WAAY,EAEZC,WAAY,GAGhBC,QAAS,CAELC,YAAa,SAEbD,QAAS,SAETE,kBAAkB,EAElBC,aAAc,IAGlBC,qBAAsB,CAElBC,kBAAmB,EAEnBC,gBAAiB,OAGrB7B,KAAM,CAEFD,SAAU,SAEVC,KAAM,SAENyB,kBAAkB,EAElBC,aAAc,KAKtBI,mBAAoB,CAEhBC,eAAgB,QAChBC,OAAQ,CAEJ,EAEA,GAGJC,WAAY,GAEZC,SAAU,GAEVC,SAAU,GAEVC,gBAAgB,GAIpBC,oBAAqB,CAEjBC,UAAU,EAEVC,QAAS,GAETC,SAAU,OAEVC,SAAU,GAEVC,SAAU,OAEVvC,IAAK,GAELwC,WAAY,SAEZC,OAAQ,IAIZC,WAAY,CAERP,UAAU,EAEVC,QAAS,GAETC,SAAU,OAEVC,SAAU,GAEVC,SAAU,GAEVI,OAAQ,aAER3C,IAAK,GAEL4C,KAAM,SAENC,OAAQ,QAIZC,YAAa,CAETC,cAAe,GAEflD,KAAM,GAENmD,UAAW,GAEXC,WAAY,GAEZC,KAAM,GAENC,QAAS,IAIbC,SAAU,CAENhB,QAAS,GAET9B,OAAQ,SAER+C,YAAaC,EAAAA,GAAYC,KAEzBC,YAAY,GAIhBC,WAAY,CAERC,UAAW,cAEXC,SAAU,GAEVC,YAAaC,EAAAA,GAA8BC,OAE3CC,MAAO,GAEPzD,OAAQ,GAER0D,QAAS,IAIbC,oBAAqB,CAEjBC,UAAW,EAEXC,YAAa,EAEbC,gBAAgB,EAEhBC,mBAAmB,EAEnBC,WAAW,EAEXC,cAAe,CACX,CAEIC,MAAO,UAEP5B,KAAM,OAEN6B,QAAS,KAIjBC,iBAAkB,CACd,CAEIF,MAAO,UAEP5B,KAAM,OAEN6B,QAAS,KAIjBE,WAAY,CACR,CAAC,MAKTC,iBAAkB,CAEdC,QAAS,cAIbC,YAAa,CAETlC,KAAM,aAENmC,YAAa,WAEbC,aAAc,GAEdhG,KAAM,GAENiG,aAAc,GAEdC,kBAAmB,GAEnBV,MAAO,GAEPW,UAAW,GAEXC,QAAS,GAETC,QAAQ,GAIZC,WAAY,CAERC,YAAa,aAEbC,KAAM,EAENC,YAAa,SAEbL,QAAS,IAIbM,UAAW,CAEPpF,OAAQ,GAER8B,QAAS,GAETuD,SAAU,GAEVC,KAAM,IAIVC,YAAa,CAETC,IAAK,GAELC,UAAU,EAEVC,KAAM,GAEN3G,KAAM,IAIV4G,gBAAiB,CAEbC,KAAM,IAIVC,iBAAkB,CAEdC,UAAW,EAEXC,QAAS,IAIbC,sBAAuB,CAEnBC,eAAgB,GAEhBC,OAAQ,GAEf,CD7TwDC,CAAuB,CAAExH,cAAeN,KAEvF+H,GAAyBC,EAAAA,EAAAA,UAAQ,SAAY7H,EAAUO,KAAW,OAALrB,QAAK,IAALA,EAAAA,EAASc,EAASO,QAAS,CAACP,EAAUd,IAEnGgF,GAAY4D,EAAAA,EAAAA,GAAmC,OAAPrI,QAAO,IAAPA,OAAO,EAAPA,EAASS,MAAM,GACvD6H,GAAUD,EAAAA,EAAAA,GAAoC,OAARpI,QAAQ,IAARA,OAAQ,EAARA,EAAUQ,MAAM,GAoB5D,OACI8H,EAAAA,EAAAA,KAAAC,EAAAA,SAAA,CAAAC,SAEQhE,IACI8D,EAAAA,EAAAA,KAACG,EAAAA,EAAW,CACR5I,SAAUqI,EACVlI,UAAWqI,EACXK,SAzBHC,UACb,GAAU,OAAL7I,QAAK,IAALA,IAAAA,EAAOU,KAER,YADAoI,EAAAA,GAAQC,MAAM,kCAKlB,MAAMC,EAAU,IACTC,EAAGlI,KAAMP,EAASO,YAEPmI,EAAAA,EAAAA,KAAeF,MAG7BG,EAAAA,EAAAA,GAAqB,CAAEzI,KAAMsI,EAAQtI,MAAQsI,GAC7C1I,EAAQF,GACZ,EAWgBT,YAAaA,EACbC,UAAWA,EACXC,cAAeA,EACfC,YAAaA,KAI1B,EE3EEsJ,EAAmB/J,IAAA,IAAC,MAAEK,EAAQ,IAAIL,EAAA,MAAM,CACjDE,KAAM,CACFC,UAAW,OACX6J,WAAY,OACZ3J,QACAD,WAAY,MACZyH,KAAM,SACNoC,aAAa,EACbC,UAAU,GAEdxJ,SAAU,CACNC,MAAO,KACPC,QAAS,KACTC,SAAU,MAEdC,MAAO,CACHC,OAAQ,MAEf,ECJKoJ,GAAUC,EAAAA,EAAAA,OAAK,IAAM,0DAEdC,EAAYC,EAAAA,GAAOC,GAAG;aACtBvK,IAAA,IAAC,UAAEG,GAAWH,EAAA,OAAc,OAATG,QAAS,IAATA,EAAAA,EAAa,MAAM;cACrCqK,IAAA,IAAC,WAAER,GAAYQ,EAAA,OAAe,OAAVR,QAAU,IAAVA,EAAAA,EAAc,MAAM;;;;EAiGtD,EA1F0BS,IAGnB,IAADC,EAAAC,EAAAC,EAAAC,EAAA,IAHqB,KACvBC,EAAI,GAAEjJ,EAAE,aAAEkJ,EAAY,kBACtB/J,EAAoBgK,EAAAA,GAAoBC,aAAE,aAAEC,EAAe,kCAC9DT,EACG,MAAM,iBAAEU,IAAqBC,EAAAA,EAAAA,MAEtBC,EAAMC,IAAWC,EAAAA,EAAAA,WAAS,IAC1BtL,EAAQuL,IAAaD,EAAAA,EAAAA,aAG5BE,EAAAA,EAAAA,YAAU,KACND,EAAUzB,EAAiB,CAAE1J,MAAO6K,IAAgB,GACrD,CAACA,KAGJO,EAAAA,EAAAA,YAAU,KACN,IACI,GAAQ,OAAJX,QAAI,IAAJA,GAAAA,EAAMY,YAAa,CACnB,MAAM,YAAEC,GAAgBC,KAAKC,MAAU,OAAJf,QAAI,IAAJA,OAAI,EAAJA,EAAMY,aACpCI,IAAQH,EAAa1L,IACtBuL,EAAUG,EAElB,MACIH,EAAUzB,EAAiB,CAAE1J,MAAO6K,IAE5C,CAAE,MAAOxB,GACLqC,QAAQC,IAAI,MAAOtC,EACvB,IACD,CAAK,OAAJoB,QAAI,IAAJA,OAAI,EAAJA,EAAMY,cAEV,MAaMrG,GAAY4D,EAAAA,EAAAA,GAAkC,OAANhJ,QAAM,IAANA,GAAgB,QAAVyK,EAANzK,EAAQS,gBAAQ,IAAAgK,GAAS,QAATC,EAAhBD,EAAkB9J,eAAO,IAAA+J,OAAnB,EAANA,EAA2BtJ,MAAM,GAE/E,OACI4K,EAAAA,EAAAA,MAAC5B,EAAS,CACNxI,GAAIA,EACJ1B,UAAiB,OAANF,QAAM,IAANA,GAAY,QAAN2K,EAAN3K,EAAQC,YAAI,IAAA0K,OAAN,EAANA,EAAczK,UACzB6J,WAAkB,OAAN/J,QAAM,IAANA,GAAY,QAAN4K,EAAN5K,EAAQC,YAAI,IAAA2K,OAAN,EAANA,EAAcb,WAC1BkC,MAAO,CACHC,QAAS9G,EAAY,QAAU,QACjCgE,SAAA,CAGEpJ,IAAUkJ,EAAAA,EAAAA,KAACiD,EAAM,CAACnM,OAAQA,EAAQe,kBAAmBA,KAGzDmI,EAAAA,EAAAA,KAACkD,EAAAA,SAAQ,CAACC,UAAUnD,EAAAA,EAAAA,KAAAC,EAAAA,SAAA,IAAMC,SAElBgC,IACIlC,EAAAA,EAAAA,KAACgB,EAAO,CACJkB,KAAMA,EACNkB,QAjCRA,KACZjB,GAAQ,GAGRH,EAAiB,CACbqB,OAAQzB,EACR0B,QAAS,IACF3B,EACHY,YAAaE,KAAKc,UAAU,CAAEf,YAAa1L,MAEjD,EAwBkBA,OAAQA,EACRuL,UAAWA,EACXxK,kBAAmBA,OAMnCmI,EAAAA,EAAAA,KAACwD,EAAAA,EAAW,CACRC,MAAO/K,EACPkJ,aAAcA,EAAa1B,UAE3BF,EAAAA,EAAAA,KAAA,OACI0D,UAAU,iBACVC,QAASA,IAAMxB,GAAQ,GAAMjC,SAGzB,eAAK6B,UAKT,C,2HCxGpB,MAAM6B,GAAoB3C,EAAAA,EAAAA,OAAK,IAAM,kCAExBlJ,EAAaA,KACtB,MAAM,YAAE8L,IAAgBC,EAAAA,EAAAA,KAoBlBC,EAAkB1D,UAChB2D,SACMH,EAAY,CAAEG,UAAWC,OAAOD,IAC1C,EAIEE,EAAkB7D,UAChB1E,SACMwI,EAAAA,EAAAA,KAAa,CACfxI,SACAyI,YAAa,QAErB,EAGJ,MAAO,CACHtM,QAlCaH,IACb,IACI,GAAIA,EAAO,CACP,MAAM,UAAEqM,EAAS,aAAEK,EAAY,OAAE1I,GAAWhE,EACvB,WAAjB0M,GACAN,EAAgBC,GAEC,WAAjBK,GACAH,EAAgBvI,EAExB,CACJ,CAAE,MAAO4E,GACLqC,QAAQC,IAAI,QAAStC,EACzB,GAsBH,EAqDL,EAzC0B1J,IAEnB,IAFoB,GACvB6B,EAAE,MAAElB,EAAK,SAAE4I,GACdvJ,EACG,MAAM,EAAEyN,IAAMC,EAAAA,EAAAA,OACPrC,EAAMC,IAAWC,EAAAA,EAAAA,WAAS,GAE3BoC,EAAmBA,KACrBrC,GAAQ,EAAK,EAGjB,OACIW,EAAAA,EAAAA,MAAA,OAAA5C,SAAA,CAEQ1I,GACIwI,EAAAA,EAAAA,KAAAC,EAAAA,SAAA,CAAAC,UACI4C,EAAAA,EAAAA,MAAC2B,EAAAA,EAAK,CAAAvE,SAAA,EACFF,EAAAA,EAAAA,KAAC0E,EAAAA,GAAM,CAACf,QAASA,IAAMa,IAAmBtE,SAAEoE,EAAE,mBAC9CtE,EAAAA,EAAAA,KAAC0E,EAAAA,GAAM,CAACf,QAASA,IAAMvD,IAAWF,SAAEoE,EAAE,wBAI9CtE,EAAAA,EAAAA,KAAC0E,EAAAA,GAAM,CAACf,QAASA,IAAMa,IAAmBtE,SAAEoE,EAAE,2CAGtDtE,EAAAA,EAAAA,KAACkD,EAAAA,SAAQ,CAACC,UAAUnD,EAAAA,EAAAA,KAAAC,EAAAA,SAAA,IAAMC,SAElBgC,IACIlC,EAAAA,EAAAA,KAAC4D,EAAiB,CACd1B,KAAMA,EACNC,QAASA,EACTxK,MAAOH,EACPmN,SAAUvE,QAMxB,C,uGCtFd,MAyEA,EAzEuB6B,KACnB,MAAM2C,GAAWC,EAAAA,EAAAA,OACX,WAAEC,IAAeC,EAAAA,EAAAA,KAuBjBC,EAAgB3E,UAAgC,IAAzB,OAAEgD,EAAM,QAAEC,GAASjC,EAE5C,MAAM4D,EAAY,IACX5B,EACHnD,SAAUgF,EAAU7B,EAAOnD,SAAUoD,KAGlC6B,SAAoBC,EAAAA,EAAAA,KAAe,CAAEC,WAAY,CAAO,OAANhC,QAAM,IAANA,OAAM,EAANA,EAAQiC,mBAE3DC,EAAAA,EAAAA,KAAU,CACZC,QAAS,CACL,IAAKL,EAAY9B,QAAQoC,EAAAA,EAAAA,IAAoBR,EAAiB,OAAN5B,QAAM,IAANA,OAAM,EAANA,EAAQiC,eAIxEV,EAAS,CAAE9I,KAAM4J,EAAAA,GAAgCC,MAAOR,EAAWG,WAAY,EAG7EJ,EAAYA,CAACU,EAAKtC,IACbsC,EAAIC,KAAIlE,GACPA,EAAKjJ,KAAO4K,EAAQ5K,GACb4K,EAGP3B,EAAKzB,UAAYyB,EAAKzB,SAAS7H,OAAS,EACjC,IACAsJ,EACHzB,SAAUgF,EAAUvD,EAAKzB,SAAUoD,IAIpC3B,IAITmE,EAAazF,UAAgC,IAAzB,OAAEgD,EAAM,QAAEC,GAAShC,EACzC,MAAM2D,EAAY,IACX5B,EACHnD,SAAUgF,EAAU7B,EAAOnD,SAAUoD,UAEnCwB,EAAWG,EAAU,EAG/B,MAAO,CACHjD,iBA5DqB3B,UAGlB,IAHyB,OAC5BgD,EAAM,QACNC,GACHzM,EAEc,OAANwM,QAAM,IAANA,GAAAA,EAAQiC,WAMT1C,QAAQC,IAAI,sCACNmC,EAAc,CAAE3B,SAAQC,cAL9BV,QAAQC,IAAI,qDACNiD,EAAW,CAAEzC,SAAQC,YAK/B,EAgDH,C", "sources": ["module/layout/controlComp/lib/AtomInputVariable/render/index.js", "module/variableInput/constants/variableInfo.js", "module/layout/controlComp/lib/AtomInputVariable/constants.js", "module/layout/controlComp/lib/AtomInputVariable/index.js", "components/formItems/SetActionOrScript/index.js", "hooks/useSplitLayout.js"], "names": ["_ref", "config", "attr", "compWidth", "labelWidth", "label", "labelItalic", "labelBold", "contentItalic", "contentBold", "variable", "value", "visible", "disabled", "event", "change", "inputVariableType", "onEvent", "useTrigger", "valueVar", "useInputVariableByCode", "code", "variable_type", "arguments", "length", "undefined", "name", "group_category", "description", "id", "default_val", "isConstant", "value_type", "unitType", "unit", "created<PERSON>y", "f1_index", "pic", "is_enable", "is_feature", "is_overall", "is_fx", "number_tab", "format", "numberRequire", "formatType", "afterPoint", "beforePoint", "significantDigits", "amendmentInterval", "pointPosition", "roundMode", "threshold1", "threshold2", "roundType1", "roundType2", "roundType3", "channel", "channelType", "isUserConversion", "lockChannels", "multipleMeasurements", "measurementCounts", "measurementType", "reasonable_val_tab", "reasonableType", "values", "defaultVal", "minParam", "max<PERSON>ara<PERSON>", "isToResultList", "button_variable_tab", "isEnable", "content", "position", "actionId", "function", "buttonType", "script", "button_tab", "source", "type", "method", "program_tab", "numericFormat", "isVisible", "isDisabled", "mode", "is<PERSON><PERSON><PERSON>", "text_tab", "return_type", "SCRIPT_TYPE", "BOOL", "canUseText", "select_tab", "selection", "group_id", "<PERSON><PERSON><PERSON><PERSON>", "MAPPING_SELECT_INPUTVAR_LAYER", "轴", "items", "comment", "two_digit_array_tab", "rowCounts", "columnCount", "rowHeaderPlace", "columnHeaderPlace", "isRowType", "rowDefinition", "title", "options", "columnDefinition", "columnData", "custom_array_tab", "useType", "control_tab", "dialog_type", "control_name", "default_name", "related_variables", "variables", "signals", "is_daq", "buffer_tab", "buffer_type", "size", "size_expand", "label_tab", "fontSize", "fore", "picture_tab", "src", "showName", "path", "related_var_tab", "vars", "double_array_tab", "rowNumber", "columns", "double_array_list_tab", "dataSourceCode", "number", "getVariableDefaultInfo", "applyLabelNameValueVar", "useMemo", "useInputVariableValueByCode", "isAbled", "_jsx", "_Fragment", "children", "InputRender", "onChange", "async", "message", "error", "newVari", "v", "updateInputVar", "dispatchSyncInputVar", "getDefaultConfig", "compHeight", "isShowColon", "readonly", "Setting", "lazy", "Container", "styled", "div", "_ref2", "_ref3", "_config$variable", "_config$variable$visi", "_config$attr", "_config$attr2", "item", "layoutConfig", "INPUT_VARIABLE_TYPE", "文本", "atomTypeName", "updateLayoutItem", "useSplitLayout", "open", "<PERSON><PERSON><PERSON>", "useState", "setConfig", "useEffect", "data_source", "comp_config", "JSON", "parse", "isEqual", "console", "log", "_jsxs", "style", "display", "Render", "Suspense", "fallback", "onClose", "layout", "newItem", "stringify", "ContextMenu", "domId", "className", "onClick", "EventEditorDialog", "startAction", "useAction", "handleRunAction", "action_id", "String", "handleRunScript", "submitScript", "result_type", "execute_type", "t", "useTranslation", "handleOpenDialog", "Space", "<PERSON><PERSON>", "callback", "dispatch", "useDispatch", "saveLayout", "useTemplateLayout", "handleTabEdit", "newLayout", "recursion", "binderData", "getBatchBinder", "binder_ids", "binder_id", "actionTab", "binders", "handleTabLayoutData", "SPLIT_CHANGE_CHANGED_BINDER_ID", "param", "arr", "map", "handleEdit"], "sourceRoot": ""}