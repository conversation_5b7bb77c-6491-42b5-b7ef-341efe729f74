"use strict";(self.webpackChunkzhongji_experiment_react=self.webpackChunkzhongji_experiment_react||[]).push([[7582],{55518:(e,a,l)=>{l.d(a,{A:()=>N});var n=l(65043),i=l(74117),t=l(56543),d=l(81143),s=l(68374),r=l(18650);const o=0,u="left";var v=l(70579);const c=d.Ay.div`
    width: ${(0,s.D0)("20px")};
    height: ${(0,s.D0)("20px")};
    background-size: ${(0,s.D0)("20px")} ${(0,s.D0)("20px")};
    background-image: url(${e=>{let{isConstant:a}=e;return a?r.fd:r.Mo}});
`,h=e=>{let{variable:a,onChange:l,disabled:n}=e;const{default_val:i,is_fx:t}=a;return!t||n?(0,v.jsx)(v.Fragment,{}):(0,v.jsx)(c,{isConstant:i.isConstant===o,onClick:()=>{l({...a,default_val:{...i,isConstant:0===(null===i||void 0===i?void 0:i.isConstant)?1:0}})}})};var b=l(95206),p=l(34154),g=l(67208),x=l(16090),f=l(36497),w=l(29977);const _=e=>{var a;let{disabled:l,variable:i,handleChange:t}=e;const d=(0,w.A)(),s=(0,n.useMemo)((()=>(null===d||void 0===d?void 0:d.filter((e=>e.variable_type===i.variable_type&&e.id!==i.id))).map((e=>({...e,labelName:`${e.name}(${e.code})`})))),[d,i]);return(0,v.jsx)(f.A,{showSearch:!0,optionFilterProp:"labelName",disabled:l,fieldNames:{label:"labelName",value:"id"},className:"input-width",value:null===i||void 0===i||null===(a=i.default_val)||void 0===a?void 0:a.variable_id,options:s,onChange:(e,a)=>t(a)})},m=e=>{let{disabled:a,content:l,buttonType:i,actionId:d,script:s}=e;const[r,o]=(0,n.useState)(!1),{startAction:u}=(0,x.A)(),c=()=>{i!==p.NR.\u52a8\u4f5c?i!==p.NR.\u811a\u672c?console.log("\u672a\u8bbe\u7f6e\u70b9\u51fb\u89e6\u53d1\u4e8b\u4ef6"):(async()=>{try{o(!0),await(0,g.O5k)({script:s,result_type:t.Jt.BOOL})}catch(e){console.log("err when handlesSubmitScript",e)}finally{o(!1)}})():(async()=>{try{d&&(o(!0),await u({action_id:d}))}catch(e){console.log("err when handleSubmitAction",e)}finally{o(!1)}})()};return(0,v.jsx)(b.Ay,{loading:r,disabled:a,className:"button-width",onClick:()=>c(),children:l})},C=d.Ay.div`
    display: flex;
    flex-direction: ${e=>{let{isLeft:a}=e;return a?"row":"row-reverse"}};
    gap: 8px;
    overflow: hidden;

    .button-width {
        width: ${(0,s.D0)("80px")};
        pointer-events: auto;
    }
`,j=e=>{let{disabled:a,variable:l,render:n,onChange:i,buttonShow:t}=e;const{button_variable_tab:d,default_val:s}=l;return(0,v.jsx)(C,{isLeft:(null===d||void 0===d?void 0:d.position)===u,children:1===s.isConstant?(0,v.jsx)(_,{disabled:a,variable:l,handleChange:e=>{i({...l,default_val:{...s,variable_id:null===e||void 0===e?void 0:e.id,variable_code:null===e||void 0===e?void 0:e.code}})}}):(0,v.jsxs)(v.Fragment,{children:[t&&(null===d||void 0===d?void 0:d.isEnable)&&(0,v.jsx)(m,{...d,disabled:a}),n()]})})};var y=l(12624),A=l(32513);const k=e=>{let{variable:a,disabled:l=!1,onChange:n,usableShowType:i="checkbox"}=e;return null!==a&&void 0!==a&&a.is_enable?"switch"===i?(0,v.jsx)(y.A,{disabled:l,checked:null===a||void 0===a?void 0:a.is_feature,onChange:e=>{n({...a,is_feature:e})}}):(0,v.jsx)(A.A,{disabled:l,checked:null===a||void 0===a?void 0:a.is_feature,onChange:e=>{n({...a,is_feature:e.target.checked})}}):(0,v.jsx)(v.Fragment,{})},S=d.Ay.div`
    .input-render-left{
        display: inline-block;
        overflow: hidden;
        &>div{
            display: flex;
            gap: 8px;
            align-items: center;
        }
    }

    .input-render-right{
        float: right;
        display: flex;
        align-items: center;
        gap: 8px;
        max-width: 100%;
        overflow: hidden;
    }
`,N=e=>{let{variable:a,disabled:l=!1,onChange:n,render:d,usableShow:s=!0,buttonShow:r=!0,fxShow:o=!0,nameShow:u=!0,usableShowType:c}=e;const{t:b}=(0,i.Bd)(),p=l||(a.variable_type===t.ps.\u5e03\u5c14\u578b?(null===a||void 0===a?void 0:a.is_enable)&&(null===a||void 0===a?void 0:a.is_feature):(null===a||void 0===a?void 0:a.is_enable)&&!(null!==a&&void 0!==a&&a.is_feature));return(0,v.jsxs)(S,{children:[(s||u)&&(0,v.jsx)("div",{className:"input-render-left",children:(0,v.jsxs)("div",{children:[s&&(0,v.jsx)(k,{variable:a,disabled:l,onChange:n,usableShowType:c}),u&&(0,v.jsx)("div",{className:"variable_name",children:b(a.name)})]})}),(0,v.jsxs)("div",{className:"input-render-right",children:[o&&(0,v.jsx)(h,{variable:a,onChange:n,disabled:p}),(0,v.jsx)(j,{disabled:p,variable:a,onChange:n,buttonShow:r,render:()=>d({innerDisabled:p})})]})]})}},97582:(e,a,l)=>{l.r(a),l.d(a,{default:()=>c});var n=l(65043),i=l(83720),t=l(79889),d=l.n(t),s=l(46085),r=l(55518),o=l(70579);const{TextArea:u}=i.A,v=e=>{var a;let{variable:l,disabled:t,onChange:r}=e;const{text_tab:v,default_val:c}=l,[h,b]=(0,n.useState)(l.default_val),[p,g]=(0,n.useState)(!1),x=(0,n.useRef)(!1);(0,n.useEffect)((()=>{const e=d()(f,200);return window.addEventListener("click",e),()=>{window.removeEventListener("click",e),e.cancel()}}),[]),(0,n.useEffect)((()=>{b(c)}),[c]);const f=()=>{x.current?x.current=!1:g(!1)},w=e=>{let a=null;a=null!=e.target?e.target.value:e,(e=>{const a={...c,...e};b(a)})({value:a})},_=e=>{let a=null;a=null!=e.target?e.target.value:e,(e=>{const a={...c,...e};r&&r(a)})({value:a})};null===(a=v.content)||void 0===a||a.split("\n");switch(v.format){case"multiple":return(0,o.jsx)(u,{readOnly:t,value:null===h||void 0===h?void 0:h.value,className:"input-width "+(t?"input-readonly":""),onChange:w,onBlur:_});case"func":return(0,o.jsx)(s.im,{disabled:t,type:null===v||void 0===v?void 0:v.return_type,value:null===h||void 0===h?void 0:h.value,width:"25vw",height:"20vw",module:s.et.\u8f93\u5165\u53d8\u91cf,onChange:_});case"single_with_text":return(0,o.jsx)(i.A,{className:"input-width",disabled:t,onChange:w,onBlur:_,value:null===h||void 0===h?void 0:h.value,placeholder:v.content});default:return(0,o.jsx)(i.A,{disabled:t,value:null===h||void 0===h?void 0:h.value,className:"input-width",onChange:e=>w(e.target.value),onBlur:_})}},c=e=>{let{variable:a,disabled:l,onChange:n}=e;return(0,o.jsx)(r.A,{variable:a,disabled:l,onChange:n,render:e=>{let{innerDisabled:l}=e;return(0,o.jsx)(v,{variable:a,disabled:l,onChange:e=>{n({...a,default_val:e})}})}})}}}]);
//# sourceMappingURL=7582.9ffa534e.chunk.js.map