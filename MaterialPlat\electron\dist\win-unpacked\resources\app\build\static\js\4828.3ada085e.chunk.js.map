{"version": 3, "file": "static/js/4828.3ada085e.chunk.js", "mappings": "mTAAO,MAAMA,EACL,K,eCsBR,MAAQC,QAAO,OAAEC,GAASC,EAAAA,EAEpBC,EAAUA,KACZ,MAAM,EAAEC,IAAMC,EAAAA,EAAAA,MACRC,GAAWC,EAAAA,EAAAA,OAEX,KAAEC,EAAI,cAAEC,IAAkBC,EAAAA,EAAAA,MAC1B,UAAEC,EAAS,YAAEC,IAAgBC,EAAAA,EAAAA,KAC7BC,GAAoBC,EAAAA,EAAAA,MAEnBC,EAAkBC,IAAuBC,EAAAA,EAAAA,WAAS,IAClDC,EAAgBC,IAAqBF,EAAAA,EAAAA,aACrCG,EAAcC,IAAmBJ,EAAAA,EAAAA,aACjCK,EAAaC,IAAkBN,EAAAA,EAAAA,aAC/BO,EAAOC,IAAYR,EAAAA,EAAAA,UAAS,iBAC5BS,EAAWC,IAAcV,EAAAA,EAAAA,WAAS,IAClCW,EAAQC,IAAaZ,EAAAA,EAAAA,UAAS,iBAC9Ba,EAAQC,IAAad,EAAAA,EAAAA,WAAS,GAC/Be,GAAYC,EAAAA,EAAAA,UACZC,GAAiBD,EAAAA,EAAAA,UACjBE,GAAiBF,EAAAA,EAAAA,UACjBG,GAAgBH,EAAAA,EAAAA,QAAO,IACvBI,GAAiBJ,EAAAA,EAAAA,QAAO,IACxBK,GAAeL,EAAAA,EAAAA,WAErBM,EAAAA,EAAAA,YAAU,KACNC,IACAC,IACO,KAAO,IAADC,EAAAC,EAAAC,EAAAC,EACQ,QAAjBH,EAAAV,EAAUc,eAAO,IAAAJ,GAAO,QAAPC,EAAjBD,EAAmBK,aAAK,IAAAJ,GAAxBA,EAAAK,KAAAN,GACsB,QAAtBE,EAAAV,EAAeY,eAAO,IAAAF,GAAO,QAAPC,EAAtBD,EAAwBG,aAAK,IAAAF,GAA7BA,EAAAG,KAAAJ,EAAiC,IAEtC,IAEH,MAAMJ,EAAoBS,UACtBjB,EAAUc,cAAgBtC,EAAc0C,EAAAA,GAAgBC,eACxD,UAAW,MAAOC,EAAQC,KAAQrB,EAAUc,QAAS,CACjD,MAAMQ,EAAOC,KAAKC,MAAMH,GACnBlB,EAAeW,QAIhBV,EAAcU,QAAU,IAAIV,EAAcU,QAASQ,IAHnD/B,EAAe+B,GACfnB,EAAeW,QAAUQ,EAIjC,GAGEb,EAAyBQ,UAC3Bf,EAAeY,cAAgBtC,EAAc0C,EAAAA,GAAgBO,oBAC7D,UAAW,MAAOL,EAAQC,KAAQnB,EAAeY,QAAS,CACtD,MAAMQ,EAAOC,KAAKC,MAAMH,GACxBhB,EAAeS,QAAU,IAAIT,EAAeS,QAASQ,EACzD,IAGJf,EAAAA,EAAAA,YAAU,KACN,GAAIjB,GAEIe,EAAeS,SAAWT,EAAeS,QAAQY,OAAS,EAAG,CAC7D,MAAMC,EAAatB,EAAeS,QAAQc,MAAKC,GAAKA,EAAEC,YAAcxC,EAAYwC,YAC/D,IAADC,EAAhB,GAAIJ,EACArB,EAAaQ,QAAUa,EACvBtB,EAAeS,QAAUT,EAAeS,QAAQkB,QAAOH,GAAKA,EAAEC,YAAcxC,EAAYwC,YACxFpD,EAAU,CACNuD,SAA6B,QAArBF,EAAEJ,EAAWO,gBAAQ,IAAAH,OAAA,EAAnBA,EAAqBI,QAC/BC,YAAY,GAGxB,CACJ,GACD,CAAC9C,EACAe,EAAeS,WAEnBP,EAAAA,EAAAA,YAAU,KACN,IAAKjB,GAAec,EAAcU,SAAWV,EAAcU,QAAQY,OAAS,EAAG,CAC3E,MAAOW,GAAajC,EAAcU,QAClCvB,EAAe8C,GACflC,EAAeW,QAAUuB,EACzBjC,EAAcU,QAAQwB,OAC1B,CACA,GAAIhD,EAAa,CAEb,GAAIA,EAAYiD,QAAUC,EAAAA,GAAYC,WAAY,CAC9C,MAAMC,EAAmB,OAAXpD,QAAW,IAAXA,OAAW,EAAXA,EAAa4C,SAC3B7C,EAAqB,OAALqD,QAAK,IAALA,OAAK,EAALA,EAAOC,aAEd,OAALD,QAAK,IAALA,OAAK,EAALA,EAAOE,cAAeC,EAAAA,GAAYC,WAClCjD,EAAU,sBACVE,GAAU,IAEdf,GAAoB,EACxB,CAGA,GAAIM,EAAYiD,QAAUC,EAAAA,GAAYO,YAAa,CAC/C,MAAML,EAAmB,OAAXpD,QAAW,IAAXA,OAAW,EAAXA,EAAa4C,SAC3BzC,EAAc,OAALiD,QAAK,IAALA,OAAK,EAALA,EAAOM,OAChBhE,GAAoB,GACpBW,GAAgB,OAAL+C,QAAK,IAALA,OAAK,EAALA,EAAOO,cAAenF,GACjCiC,GAAe,OAAL2C,QAAK,IAALA,OAAK,EAALA,EAAOO,cAAenF,EACpC,CACJ,IACD,CAACwB,KAEJiB,EAAAA,EAAAA,YAAU,KACN,IAAKjB,GAAec,EAAcU,SAAWV,EAAcU,QAAQY,OAAS,EAAG,CAC3E,MAAOW,GAAajC,EAAcU,QAClCvB,EAAe8C,GACflC,EAAeW,QAAUuB,EACzBjC,EAAcU,QAAQwB,OAC1B,CACA,GAAIhD,EAAa,CAEb,GAAIA,EAAYiD,QAAUC,EAAAA,GAAYU,iBAAkB,CACpD/D,GAAkB,GAClB,MAAMuD,EAAmB,OAAXpD,QAAW,IAAXA,OAAW,EAAXA,EAAa4C,SAE3B,GAAIrD,IAAsC,OAAjBA,QAAiB,IAAjBA,OAAiB,EAAjBA,EAAmB6C,QAAS,EAAG,CAAC,IAADyB,EACpD,MAAMC,EAAc,OAALV,QAAK,IAALA,GAAc,QAATS,EAALT,EAAOW,eAAO,IAAAF,OAAT,EAALA,EAAgBG,KAAIC,IAC/B,MAAMC,EAAQ3E,EAAkB+C,MAAKC,GAAKA,EAAE4B,OAASF,EAAEE,OACvD,MAAO,IAAKD,EAAOE,YAAa,IAAKF,EAAME,YAAaC,MAAOJ,EAAEI,OAAS,IAE9EtE,EAAgB+D,EACpB,CACJ,CAGA,GAAI9D,EAAYiD,QAAUC,EAAAA,GAAYoB,kBAAmB,CAAC,IAADC,EACrD,MAAMnB,EAAmB,OAAXpD,QAAW,IAAXA,OAAW,EAAXA,EAAa4C,SAE3B,GADA/C,GAAkB,GACT,OAALuD,QAAK,IAALA,GAAAA,EAAOoB,YAAkB,OAALpB,QAAK,IAALA,GAAgB,QAAXmB,EAALnB,EAAOoB,iBAAS,IAAAD,OAAX,EAALA,EAAkBnC,QAAS,EAAG,CAAC,IAADqC,EAClD,MAAMX,EAAc,OAALV,QAAK,IAALA,GAAgB,QAAXqB,EAALrB,EAAOoB,iBAAS,IAAAC,OAAX,EAALA,EAAkBT,KAAIC,GACnB1E,EAAkB+C,MAAKC,GAAKA,EAAE4B,OAASF,MAGzDlE,EAAgB+D,EACpB,CACS,OAALV,QAAK,IAALA,GAAAA,EAAOM,OACPvD,EAAc,OAALiD,QAAK,IAALA,OAAK,EAALA,EAAOM,MAExB,CACJ,IACD,CAAC1D,IAEJ,MAAM0E,EAAkBA,KAAO,IAADC,EAAAC,EASmBC,EAAAC,GAR7CpF,GAAoB,GACpBG,GAAkB,GAClBY,GAAU,GACVJ,GAAW,GACXN,EAAgB,IAChBI,EAAS,gBACTI,EAAU,gBACVN,IACwB,QAAxB0E,EAAI3D,EAAaQ,eAAO,IAAAmD,GAAU,QAAVC,EAApBD,EAAsB/B,gBAAQ,IAAAgC,GAA9BA,EAAgC/B,UAChCxD,EAAgC,QAArBwF,EAAC7D,EAAaQ,eAAO,IAAAqD,GAAU,QAAVC,EAApBD,EAAsBjC,gBAAQ,IAAAkC,OAAV,EAApBA,EAAgCjC,SAEhD7B,EAAaQ,aAAUuD,EACnBjE,EAAcU,SAA4C,IAAjCV,EAAcU,QAAQY,SAC/CvB,EAAeW,aAAUuD,EACzBhE,EAAeS,QAAU,GAC7B,EAEEwD,EAAU,WAAqD,IAApDC,EAAMC,UAAA9C,OAAA,QAAA2C,IAAAG,UAAA,GAAAA,UAAA,GAAGC,EAAAA,GAAmBC,SAAUC,EAAQH,UAAA9C,OAAA,EAAA8C,UAAA,QAAAH,EAC3D,MAAM,UAAEvC,EAAS,UAAE8C,GAActF,EACjCf,EAAKgD,KAAKsD,UAAU,CAChB/C,YACA8C,YACAL,SACAO,OAAQC,EAAAA,GAAwBC,QAChCL,SAAUA,EAAWpD,KAAKsD,UAAUF,GAAY,OAExD,EAEMM,EAAwBN,IAC1BL,EAAQG,EAAAA,GAAmBS,SAAUP,GACrCX,GAAiB,EAEfmB,EAAuBlE,iBAA+D,IAAxDsD,EAAMC,UAAA9C,OAAA,QAAA2C,IAAAG,UAAA,GAAAA,UAAA,GAAGC,EAAAA,GAAmBW,aAC5Dd,EAAQC,EAD0EC,UAAA9C,OAAA,EAAA8C,UAAA,QAAAH,GAElFL,GACJ,EAEMqB,EAAqBpE,UACvB,IACQ7B,UACMkG,EAAAA,EAAAA,KAAoB,CACtBC,WAAwB,OAAZnG,QAAY,IAAZA,OAAY,EAAZA,EAAckE,KAAIkC,IAAA,IAAC,GAC3BC,EAAE,YAAE/B,EAAW,WAAEgC,EAAU,MAAEC,GAChCH,EAAA,MAAM,CACHC,KACA/B,cACAgC,aACAC,QACH,MAELR,EAAqBV,EAAAA,GAAmBC,UACxCrG,GAASuH,EAAAA,EAAAA,MAEjB,CAAE,MAAOC,GACLC,QAAQC,IAAI,mDAAYF,EAC5B,GAGEG,EAAoBC,IACtB5G,EAA4B,OAAZD,QAAY,IAAZA,OAAY,EAAZA,EAAckE,KAAIC,GAAMA,EAAEkC,KAAOQ,EAAKR,GAAKQ,EAAO1C,IAAI,EAGpE2C,EAAY,CACd,CAAC1D,EAAAA,GAAYC,YAAawC,EAC1B,CAACzC,EAAAA,GAAYU,kBAAmB+B,EAChC,CAACzC,EAAAA,GAAYO,aAAc,IAAMoC,EAAqBV,EAAAA,GAAmBC,SAAU,CAAEyB,SAAS,IAC9F,CAAC3D,EAAAA,GAAYoB,mBAAoBqB,GAG/BmB,GAAQ,CACV,CAAC5D,EAAAA,GAAYC,YAAa0C,EAC1B,CAAC3C,EAAAA,GAAYU,kBAAmBmC,EAChC,CAAC7C,EAAAA,GAAYO,aAAc,IAAMoC,EAAqBV,EAAAA,GAAmBC,SAAU,CAAEyB,SAAS,IAC9F,CAAC3D,EAAAA,GAAYoB,mBAAoByB,GAI/BgB,GAAY,CACd,CAAC7D,EAAAA,GAAYC,YAAawC,EAC1B,CAACzC,EAAAA,GAAYU,kBAAmB+B,EAChC,CAACzC,EAAAA,GAAYO,aAAc,IAAMkC,EAAqB,CAAEkB,SAAS,IACjE,CAAC3D,EAAAA,GAAYoB,mBAAoBqB,GAG/BqB,IAAUC,EAAAA,EAAAA,cACZ,KAEI,GADAvH,GAAoB,GAChBc,EACAC,GAAU,GACV/B,EAAK,CACDwB,MAAOrB,EAAEqB,GACTI,OAAQzB,EAAEyB,GACV4G,QAASpH,EACTqH,IAAAA,GACiB,OAATP,QAAS,IAATA,GAAAA,EAAY5G,EAAYiD,SACf,OAAT2D,QAAS,IAATA,GAAAA,EAAY5G,EAAYiD,SAEhC,QAED,CACH,MAAMG,EAAQ,CACVlD,MAAOrB,EAAEqB,GACTkH,MAAMC,EAAAA,EAAAA,KAACC,EAAAA,EAAuB,IAC9BJ,QAASpH,EACTQ,OAAQzB,EAAEyB,GACViH,cAAc,EACdJ,IAAAA,GACa,OAALL,SAAK,IAALA,IAAAA,GAAQ9G,EAAYiD,SACf,OAAL6D,SAAK,IAALA,IAAAA,GAAQ9G,EAAYiD,SAE5B,EACAuE,QAAAA,GACiB,OAATT,SAAS,IAATA,IAAAA,GAAY/G,EAAYiD,SACf,OAAT8D,SAAS,IAATA,IAAAA,GAAY/G,EAAYiD,SAEhC,GAGA7C,EACA3B,EAAQ2E,GAER1E,EAAK0E,EAEb,IACD,CACC5C,EACAJ,EACAJ,EACAE,EACAI,EACAR,EACAL,IAIR,OACIgI,EAAAA,EAAAA,MAAAC,EAAAA,SAAA,CAAAC,SAAA,CACKlI,GAAoBuH,KACpBpH,IAEGyH,EAAAA,EAAAA,KAACO,EAAAA,EAAM,CACHC,MAAM,OACN3H,MAAOrB,EAAEqB,GACT4H,KAAMlI,EACNuH,KAAMpB,EACNyB,SAAU7B,EAAqBgC,SAElB,OAAZ7H,QAAY,IAAZA,OAAY,EAAZA,EAAckE,KAAIC,IACfoD,EAAAA,EAAAA,KAACU,EAAAA,SAAQ,CAAAJ,UACLN,EAAAA,EAAAA,KAACW,EAAAA,EAAW,CACRC,SAAUhE,EACViE,SAAUxB,EACVyB,kBAAgB,KAJTlE,EAAEkC,UAU9B,EAIX,GAAeiC,EAAAA,EAAAA,MAAKxJ,E,iFCnUpB,MAyEA,EAzEiBU,KACb,MAAMP,GAAWC,EAAAA,EAAAA,MAiEjB,MAAO,CACHqJ,cAjEkB1G,UAClB,IACI,MAAM2G,QAAYC,EAAAA,EAAAA,OAClB,GAAID,EAAK,CACL,MAAME,EAAYF,EAAIG,UACtB1J,EAAS,CACL2J,KAAMC,EAAAA,GACNvF,MAAOoF,GAEf,CACJ,CAAE,MAAOjC,GACLC,QAAQC,IAAIF,EAChB,GAsDAnH,UAnDcuC,UAIX,IAJkB,SACrBgB,EAAQ,WACRiG,EAAU,WACV9F,GAAa,GAChBoD,EACG,IACI,MAAMoC,QAAYO,EAAAA,EAAAA,KAAa,CAAElG,aACjC,GAAI2F,EAAK,CACL,MAAMQ,EAAO,IAAIC,KAAK,CAACT,GAAM,WAAY,CAAEI,KAAM,eAC3CM,EAAcC,SAASC,cAAc,SACrCC,EAAYC,IAAIC,gBAAgBP,GACtCE,EAAYM,IAAMH,EAClBH,EAAY7C,GAAKxD,EACjBqG,EAAYO,OACZP,EAAYQ,OAEZP,SAASQ,KAAKC,YAAYV,GAC1BA,EAAYW,iBAAiB,SAAS,KAC9B7G,EACAkG,EAAYQ,OAEZR,EAAYY,SAEZhB,GAEAA,GAAW,EAAMjG,GAErByG,IAAIS,gBAAgBV,EAAU,IAC/B,EACP,CACJ,CAAE,MAAO5C,GACLC,QAAQD,MAAM,uBAAwBA,EAC1C,GAoBAlH,YAlBgBsC,MAAOgB,EAAUmH,KACjC,IACI,MAAMC,EAAed,SAASe,eAAerH,GACzCoH,IACAA,EAAaH,SACTE,GACAA,GAAW,EAAMnH,GAErByG,IAAIS,gBAAgBE,EAAaT,KAEzC,CAAE,MAAO/C,GACLC,QAAQD,MAAM,wBAAyBA,EAC3C,GAOH,C", "sources": ["components/SubTask/constants.js", "components/SubTask/index.js", "hooks/useAudio.js"], "names": ["OPEN_DIALOG_TYPE", "confirm", "info", "Modal", "SubTask", "t", "useTranslation", "dispatch", "useDispatch", "send", "useSubscriber", "useSubTask", "playAudio", "removeAudio", "useAudio", "inputVariableList", "useInputVariableList", "confirmModalOpen", "setConfirmModalOpen", "useState", "inputModalOpen", "setInputModalOpen", "modalContext", "setModalContext", "currentData", "setCurrentData", "title", "setTitle", "isConfirm", "setConfirm", "okText", "setOkText", "isInfo", "setIsInfo", "clientSub", "useRef", "clientAudioSub", "currentDataRef", "taskModalData", "taskModalAudio", "currentAudio", "useEffect", "initUseSubscriber", "initUseAudioSubscriber", "_clientSub$current", "_clientSub$current$cl", "_clientAudioSub$curre", "_clientAudioSub$curre2", "current", "close", "call", "async", "TASK_TOPIC_TYPE", "UI_OPEN_MODAL", "_topic", "msg", "data", "JSON", "parse", "UI_TASK_PLAY_AUDIO", "length", "modalAudio", "find", "f", "SubTaskID", "_modalAudio$UIParams", "filter", "audio_id", "UIParams", "audioId", "isLoopPlay", "modalData", "shift", "UICmd", "UI_CMD_TYPE", "OPEN_MODAL", "param", "dialogInfo", "dialogType", "DIALOG_TYPE", "REMINDER", "OPEN_DIALOG", "Title", "ButtonType", "INPUT_VAR_DIALOG", "_param$listMap", "inputs", "listMap", "map", "m", "input", "code", "default_val", "value", "OPEN_INPUT_DIALOG", "_param$InputVars", "InputVars", "_param$InputVars2", "onGeneralCancel", "_currentAudio$current", "_currentAudio$current2", "_currentAudio$current3", "_currentAudio$current4", "undefined", "subSend", "Action", "arguments", "DIALOG_ACTION_TYPE", "CLICK_OK", "JsonArgs", "ProcessID", "stringify", "Target", "DIALOG_TYPE_TARGET_SEND", "TO_TASK", "onConfirmModalCancel", "CLOSE_OK", "handleConfirmModalOk", "ModalClickOK", "handleInputModalOk", "batchUpdateInputVar", "input_vars", "_ref", "id", "is_feature", "is_fx", "initInputVariables", "error", "console", "log", "onVariableChange", "newV", "okInfoMap", "ClickOk", "okMap", "cancelMap", "Confirm", "useCallback", "content", "onOk", "icon", "_jsx", "ExclamationCircleFilled", "maskClosable", "onCancel", "_jsxs", "_Fragment", "children", "VModal", "width", "open", "Fragment", "InputRender", "variable", "onChange", "openMarginBottom", "memo", "initAudioData", "res", "getAudioList", "sortedRes", "reverse", "type", "AUDIO", "onAudioEnd", "getAudioInfo", "file", "File", "audioPlayer", "document", "createElement", "objectURL", "URL", "createObjectURL", "src", "load", "play", "body", "append<PERSON><PERSON><PERSON>", "addEventListener", "remove", "revokeObjectURL", "onCallBack", "audioElement", "getElementById"], "sourceRoot": ""}