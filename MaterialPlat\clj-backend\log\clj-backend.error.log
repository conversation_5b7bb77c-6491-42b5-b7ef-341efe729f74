2025-09-11 11:36:15,581 [async-dispatch-7] ERROR jdbc.audit - 59. PreparedStatement.execute() PRAGMA wal_checkpoint(RESTART) 
 
org.sqlite.SQLiteException: [SQLITE_LOCKED]  A table in the database is locked (database table is locked)
	at org.sqlite.core.DB.newSQLException(DB.java:1030)
	at org.sqlite.core.DB.newSQLException(DB.java:1042)
	at org.sqlite.core.DB.execute(DB.java:881)
	at org.sqlite.jdbc3.JDBC3PreparedStatement.execute(JDBC3PreparedStatement.java:54)
	at net.sf.log4jdbc.PreparedStatementSpy.execute(PreparedStatementSpy.java:417)
	at next.jdbc.result_set$stmt__GT_result_set.invokeStatic(result_set.clj:663)
	at next.jdbc.result_set$stmt__GT_result_set.invoke(result_set.clj:658)
	at next.jdbc.result_set$eval4526$fn__4534.invoke(result_set.clj:891)
	at next.jdbc.protocols$eval3616$fn__3617$G__3607__3626.invoke(protocols.clj:33)
	at next.jdbc$execute_BANG_.invokeStatic(jdbc.clj:250)
	at next.jdbc$execute_BANG_.invoke(jdbc.clj:237)
	at clj_backend.modules.sqlite.cache_utils$safe_wal_checkpoint$fn__47855.invoke(cache_utils.clj:19)
	at clj_backend.modules.sqlite.cache_utils$safe_wal_checkpoint.invokeStatic(cache_utils.clj:13)
	at clj_backend.modules.sqlite.cache_utils$safe_wal_checkpoint.invoke(cache_utils.clj:9)
	at clj_backend.modules.sqlite.cache_utils$flush_project_cache.invokeStatic(cache_utils.clj:49)
	at clj_backend.modules.sqlite.cache_utils$flush_project_cache.invoke(cache_utils.clj:40)
	at clj_backend.modules.project.project_service$save_project.invokeStatic(project_service.clj:414)
	at clj_backend.modules.project.project_service$save_project.invoke(project_service.clj:400)
	at clj_backend.modules.project.project_service$handle_project_event.invokeStatic(project_service.clj:633)
	at clj_backend.modules.project.project_service$handle_project_event.invoke(project_service.clj:628)
	at clj_backend.modules.project.project_service$eval48077$fn__48078$fn__48100$state_machine__29475__auto____48101$fn__48103.invoke(project_service.clj:638)
	at clj_backend.modules.project.project_service$eval48077$fn__48078$fn__48100$state_machine__29475__auto____48101.invoke(project_service.clj:638)
	at clojure.core.async.impl.runtime$run_state_machine.invokeStatic(runtime.clj:62)
	at clojure.core.async.impl.runtime$run_state_machine.invoke(runtime.clj:61)
	at clojure.core.async.impl.runtime$run_state_machine_wrapped.invokeStatic(runtime.clj:66)
	at clojure.core.async.impl.runtime$run_state_machine_wrapped.invoke(runtime.clj:64)
	at clojure.core.async.impl.runtime$take_BANG_$fn__24211.invoke(runtime.clj:75)
	at clojure.core.async.impl.channels.ManyToManyChannel$fn__24005$fn__24006.invoke(channels.clj:100)
	at clojure.lang.AFn.run(AFn.java:22)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at clojure.core.async.impl.concurrent$counted_thread_factory$reify__23874$fn__23875.invoke(concurrent.clj:29)
	at clojure.lang.AFn.run(AFn.java:22)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-09-11 11:36:15,585 [async-dispatch-7] ERROR jdbc.sqltiming - 59. PreparedStatement.execute() FAILED! PRAGMA wal_checkpoint(RESTART) 
 {FAILED after 12 msec} 
org.sqlite.SQLiteException: [SQLITE_LOCKED]  A table in the database is locked (database table is locked)
	at org.sqlite.core.DB.newSQLException(DB.java:1030)
	at org.sqlite.core.DB.newSQLException(DB.java:1042)
	at org.sqlite.core.DB.execute(DB.java:881)
	at org.sqlite.jdbc3.JDBC3PreparedStatement.execute(JDBC3PreparedStatement.java:54)
	at net.sf.log4jdbc.PreparedStatementSpy.execute(PreparedStatementSpy.java:417)
	at next.jdbc.result_set$stmt__GT_result_set.invokeStatic(result_set.clj:663)
	at next.jdbc.result_set$stmt__GT_result_set.invoke(result_set.clj:658)
	at next.jdbc.result_set$eval4526$fn__4534.invoke(result_set.clj:891)
	at next.jdbc.protocols$eval3616$fn__3617$G__3607__3626.invoke(protocols.clj:33)
	at next.jdbc$execute_BANG_.invokeStatic(jdbc.clj:250)
	at next.jdbc$execute_BANG_.invoke(jdbc.clj:237)
	at clj_backend.modules.sqlite.cache_utils$safe_wal_checkpoint$fn__47855.invoke(cache_utils.clj:19)
	at clj_backend.modules.sqlite.cache_utils$safe_wal_checkpoint.invokeStatic(cache_utils.clj:13)
	at clj_backend.modules.sqlite.cache_utils$safe_wal_checkpoint.invoke(cache_utils.clj:9)
	at clj_backend.modules.sqlite.cache_utils$flush_project_cache.invokeStatic(cache_utils.clj:49)
	at clj_backend.modules.sqlite.cache_utils$flush_project_cache.invoke(cache_utils.clj:40)
	at clj_backend.modules.project.project_service$save_project.invokeStatic(project_service.clj:414)
	at clj_backend.modules.project.project_service$save_project.invoke(project_service.clj:400)
	at clj_backend.modules.project.project_service$handle_project_event.invokeStatic(project_service.clj:633)
	at clj_backend.modules.project.project_service$handle_project_event.invoke(project_service.clj:628)
	at clj_backend.modules.project.project_service$eval48077$fn__48078$fn__48100$state_machine__29475__auto____48101$fn__48103.invoke(project_service.clj:638)
	at clj_backend.modules.project.project_service$eval48077$fn__48078$fn__48100$state_machine__29475__auto____48101.invoke(project_service.clj:638)
	at clojure.core.async.impl.runtime$run_state_machine.invokeStatic(runtime.clj:62)
	at clojure.core.async.impl.runtime$run_state_machine.invoke(runtime.clj:61)
	at clojure.core.async.impl.runtime$run_state_machine_wrapped.invokeStatic(runtime.clj:66)
	at clojure.core.async.impl.runtime$run_state_machine_wrapped.invoke(runtime.clj:64)
	at clojure.core.async.impl.runtime$take_BANG_$fn__24211.invoke(runtime.clj:75)
	at clojure.core.async.impl.channels.ManyToManyChannel$fn__24005$fn__24006.invoke(channels.clj:100)
	at clojure.lang.AFn.run(AFn.java:22)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at clojure.core.async.impl.concurrent$counted_thread_factory$reify__23874$fn__23875.invoke(concurrent.clj:29)
	at clojure.lang.AFn.run(AFn.java:22)
	at java.base/java.lang.Thread.run(Thread.java:840)
