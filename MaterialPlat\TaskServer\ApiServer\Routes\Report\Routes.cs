using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using TaskServer.ApiServer.Routes.Report.Models;

namespace TaskServer.ApiServer.Routes.Report;

/// <summary>
/// 报表接口
/// </summary>
[ApiController]
[Route("/report")]
public class Routes
{

    /// <summary>
    /// 导出报表
    /// </summary>
    /// <param name="param"></param>
    /// <returns></returns>
    [HttpPost]
    [Route("/export")]
    public IResult Export([FromBody] ExportReportParams param)
    {
        if (param.HistoricalDataParams != null)
        {
            param.HistoricalDataParams.SignalVars.Add(new ReportSignalVarParams
            (
                "系统时间",
                "computer_time",
                null
            ));
        }
        Service.ReportExport(param);
        return Results.Ok("Success");
    }

    /// <summary>
    /// 导出csv
    /// </summary>
    /// <param name="param"></param>
    /// <returns></returns>
    [HttpPost]
    [Route("/export/csv")]
    public IResult ExportCsv([FromBody] ExportCsvParams param)
    {

        Service.CsvExport(param);
        return Results.Ok("Exporting");
    }

    /// <summary>
    /// 分页导出Excel - 初始化文件
    /// </summary>
    /// <param name="param"></param>
    /// <returns></returns>
    [HttpPost]
    [Route("/export/excel/init")]
    public IResult InitExcelExport([FromBody] InitExcelExportParams param)
    {
        if (param.HistoricalDataParams != null)
        {
            param.HistoricalDataParams.SignalVars.Add(new ReportSignalVarParams
            (
                "系统时间",
                "computer_time",
                null
            ));
        }
        var result = Service.InitExcelExport(param);
        return Results.Ok(result);
    }

    /// <summary>
    /// 分页导出Excel - 追加数据
    /// </summary>
    /// <param name="param"></param>
    /// <returns></returns>
    [HttpPost]
    [Route("/export/excel/append")]
    public IResult AppendExcelData([FromBody] AppendExcelDataParams param)
    {
        if (param.HistoricalDataParams != null)
        {
            param.HistoricalDataParams.SignalVars.Add(new ReportSignalVarParams
            (
                "系统时间",
                "computer_time",
                null
            ));
        }
        Service.AppendExcelData(param);
        return Results.Ok("Success");
    }

    /// <summary>
    /// 分页导出Excel - 完成导出
    /// </summary>
    /// <param name="param"></param>
    /// <returns></returns>
    [HttpPost]
    [Route("/export/excel/finalize")]
    public IResult FinalizeExcelExport([FromBody] FinalizeExcelExportParams param)
    {
        Service.FinalizeExcelExport(param);
        return Results.Ok("Success");
    }

    /// <summary>
    /// 导出报表
    /// </summary>
    /// <param name="param"></param>
    /// <returns></returns>
    [HttpPost]
    [Route("/export/excel/doubleArray")]
    public IResult ExportExcelDoubleArry([FromBody] ExportDoubleArrayParams param)
    {
        Service.ExportDoubleArray(param);
        return Results.Ok("Success");
    }
    /// <summary>
    /// 导出报表
    /// </summary>
    /// <param name="param"></param>
    /// <returns></returns>
    [HttpPost]
    [Route("/export/csv/doubleArray")]
    public IResult ExportCsvDoubleArray([FromBody] ExportDoubleArrayParams param)
    {
        Service.ExportCsvDoubleArray(param);
        return Results.Ok("Success");
    }

    /// <summary>
    /// 异步导出CSV
    /// </summary>
    /// <param name="param">导出参数</param>
    /// <returns>任务信息</returns>
    [HttpPost]
    [Route("/export/csv/async")]
    public IResult ExportCsvAsync([FromBody] ExportCsvParams param)
    {
        try
        {
            // 生成任务ID
            var taskId = Guid.NewGuid().ToString();

            // 创建任务状态
            TaskStatusManager.CreateTask(taskId, "CSV导出任务已启动");

            // 异步执行导出
            _ = Task.Run(async () => await Service.CsvExportAsync(param, taskId));

            // 立即返回任务信息
            var response = new AsyncExportResponse
            {
                TaskId = taskId,
                Status = "started",
                Message = "CSV导出任务已启动，请使用任务ID查询进度"
            };
            return Results.Ok(response);
        }
        catch (Exception ex)
        {
            return Results.BadRequest(new { Message = "任务启动失败", Error = ex.Message });
        }

       
    }

    /// <summary>
    /// 查询CSV导出任务状态
    /// </summary>
    /// <param name="taskId">任务ID</param>
    /// <returns>任务状态</returns>
    [HttpGet]
    [Route("/export/csv/status/{taskId}")]
    public IResult GetCsvExportStatus(string taskId)
    {
        var status = TaskStatusManager.GetStatus(taskId);
        if (status is null)
        {
            return Results.NotFound(new { Message = "任务不存在或已过期" });
        }

        return Results.Ok(new { Status = status });
    }

    /// <summary>
    /// 获取所有CSV导出任务列表
    /// </summary>
    /// <returns>任务列表</returns>
    [HttpGet]
    [Route("/export/csv/tasks")]
    public IResult GetCsvExportTasks()
    {
        var tasks = TaskStatusManager.GetAllTasks();
        return Results.Ok(tasks);
    }

    /// <summary>
    /// 获取任务统计信息
    /// </summary>
    /// <returns>统计信息</returns>
    [HttpGet]
    [Route("/export/csv/statistics")]
    public IResult GetTaskStatistics()
    {
        var statistics = TaskStatusManager.GetStatistics();
        return Results.Ok(statistics);
    }
}