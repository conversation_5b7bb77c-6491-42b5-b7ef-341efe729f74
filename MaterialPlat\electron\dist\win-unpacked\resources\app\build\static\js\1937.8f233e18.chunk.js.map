{"version": 3, "file": "static/js/1937.8f233e18.chunk.js", "mappings": "4XAIO,MAAMA,EAAuBC,EAAAA,GAAOC,GAAG;;;;;;;;iBCY9C,MAoDA,EApDoBC,IAEb,IAFc,KACjBC,EAAI,KAAEC,EAAI,KAAEC,EAAI,QAAEC,GACrBJ,EACG,MAAM,EAAEK,IAAMC,EAAAA,EAAAA,MACRC,GAAsBC,EAAAA,EAAAA,MACrBC,EAAOC,IAAYC,EAAAA,EAAAA,aAE1BC,EAAAA,EAAAA,YAAU,KACFX,GACAS,EAAST,EACb,GACD,CAACA,IAMJ,OACIY,EAAAA,EAAAA,KAAAC,EAAAA,SAAA,CAAAC,UACIF,EAAAA,EAAAA,KAACG,EAAAA,EAAM,CACHd,KAAMA,EACNe,MAAOZ,EAAE,gBACTa,MAAM,OACNC,SAAUA,IAAMf,GAAQ,GACxBgB,OAAQ,KAAKL,UAEbM,EAAAA,EAAAA,MAACxB,EAAoB,CAAAkB,SAAA,EACjBM,EAAAA,EAAAA,MAAA,OAAAN,SAAA,EACIF,EAAAA,EAAAA,KAAA,QAAAE,SAAOV,EAAE,8BAAe,UAExBQ,EAAAA,EAAAA,KAACS,EAAAA,EAAM,CACHC,QAAShB,EACTE,MAAOA,EACPe,MAAO,CAAEN,MAAO,QAChBO,WAAY,CAAEC,MAAO,OAAQjB,MAAO,QACpCkB,SAAWC,GAAMlB,EAASkB,SAGlCf,EAAAA,EAAAA,KAAA,OAAKgB,UAAU,gBAAed,UAC1BM,EAAAA,EAAAA,MAACS,EAAAA,EAAK,CAACC,UAAU,WAAUhB,SAAA,EACvBF,EAAAA,EAAAA,KAACmB,EAAAA,EAAO,CAACC,OAAK,EAACC,QAASA,KA1B5C/B,EAAKM,EA0BwD,EAAAM,SAAEV,EAAE,mBAC7CQ,EAAAA,EAAAA,KAACmB,EAAAA,EAAO,CAACC,OAAK,EAACC,QAASA,IAAM9B,GAAQ,GAAOW,SAAEV,EAAE,6BAOlE,EC7DE8B,EAAiBrC,EAAAA,GAAOC,GAAG;;;;;;;kBAOtBqC,EAAAA,GAAMC;EAEXC,EAAuBxC,EAAAA,GAAOC,GAAG;;;;;;;;;ECSxCwC,EAAwBvC,IAEvB,IAFwB,MAC3BwC,EAAK,aAAEC,EAAY,aAAEC,EAAY,eAAEC,EAAc,mBAAEC,EAAkB,gBAAEC,GAC1E7C,EACG,MAAM,EAAEK,EAAC,KAAEyC,IAASxC,EAAAA,EAAAA,MACpB,OACIO,EAAAA,EAAAA,KAACyB,EAAoB,CAAAvB,UACjBM,EAAAA,EAAAA,MAAC0B,EAAAA,EAAW,CACRP,MAAOA,EACPE,aAAcA,EAAa3B,SAAA,EAE3BF,EAAAA,EAAAA,KAAA,OACIgB,UAAU,iBACVK,QAASA,KACLO,GAAa,EAAK,EACpB1B,SAEDV,EAAE,mBAEPQ,EAAAA,EAAAA,KAAA,OACIgB,UAAW,mBAAkBgB,EAAkB,WAAa,IAC5DX,QAASA,KACLU,GAAoB,EACtB7B,SAEgCV,EAAhCsC,EAAkC,6CAAf,oDAIV,EA8R/B,EA1RcK,IAEP,IAADC,EAAAC,EAAA,IAFS,GACXC,EAAE,KAAElD,EAAI,UAAEmD,GAAY,EAAK,KAAEC,EAAI,aAAEX,GACtCM,EACG,MAAM,EAAE3C,EAAC,KAAEyC,IAASxC,EAAAA,EAAAA,MAEdgD,GAAYC,EAAAA,EAAAA,KAAYC,GAASA,EAAMC,SAASH,YAChDI,GAAaH,EAAAA,EAAAA,KAAYC,GAASA,EAAMC,SAASC,aACjDC,GAAYJ,EAAAA,EAAAA,KAAYC,GAASA,EAAMI,QAAQD,YAC/CE,GAAgBN,EAAAA,EAAAA,KAAYC,GAASA,EAAMM,QAAQD,gBACnDE,GAAeR,EAAAA,EAAAA,KAAYC,GAASA,EAAMM,QAAQC,eAClDxD,GAAsBC,EAAAA,EAAAA,MACtB,YACFwD,EAAW,mBACXC,EAAkB,WAClBC,EACAC,aAAcC,IACdC,EAAAA,EAAAA,MACE,WAAEC,IAAeC,EAAAA,EAAAA,MAEhBC,EAAWC,IAAgB9D,EAAAA,EAAAA,aAC3B+D,EAAUC,IAAehE,EAAAA,EAAAA,UAAS,KAClCiE,EAASC,IAAclE,EAAAA,EAAAA,WAAS,IAChCmE,EAAUC,IAAepE,EAAAA,EAAAA,WAAS,IAClCgC,EAAgBqC,IAAqBrE,EAAAA,EAAAA,WAASyC,KAAsBgB,IACpEa,EAAaC,IAAkBvE,EAAAA,EAAAA,WAAS,IACxCwE,EAAYC,IAAiBzE,EAAAA,EAAAA,YAC9B0E,GAAYC,EAAAA,EAAAA,WAElB1E,EAAAA,EAAAA,YAAU,KACN,GAAQ,OAAJyC,QAAI,IAAJA,GAAAA,EAAMkC,UAAW,CACjB,MAAMC,GAAaC,EAAAA,EAAAA,IAAS/B,EAAY,YAAiB,OAAJL,QAAI,IAAJA,OAAI,EAAJA,EAAMkC,WAC3DH,EAAcI,EAClB,CACA,MAAO,KACHJ,GAAe,CAClB,GACF,CAAC1B,KAEJ9C,EAAAA,EAAAA,YAAU,KACDwC,GACD4B,IAAoBZ,EACxB,GACD,CAACA,KAEJxD,EAAAA,EAAAA,YAAU,MACDiD,GAAiBE,GAAgBpB,GAClC+C,GACJ,GACD,CAAC7B,EACAE,EACApB,IAEJ,MAAM+C,GAAYC,EAAAA,EAAAA,aACdC,KAAS,KACLC,IAAkB,GACnB,KACH,KAGJjF,EAAAA,EAAAA,YAAU,KACNkF,IACO,KACHnB,EAAY,IACZF,GAAc,IAEnB,CAACxE,EACA0D,EACAhB,EACAkB,EACAP,KAGJ1C,EAAAA,EAAAA,YAAU,KAAO,IAADmF,EACZ,IAAKlC,GAA2B,OAAVsB,QAAU,IAAVA,GAAuB,QAAbY,EAAVZ,EAAYa,mBAAW,IAAAD,GAAvBA,EAAyBE,WAAY,CAAC,IAADC,EAAAC,EACvD,MAAMC,EAA2B,OAAnB7F,QAAmB,IAAnBA,GAA+E,QAA5D2F,EAAnB3F,EAAqB8F,MAAKC,IAAC,IAAAC,EAAA,OAAK,OAADD,QAAC,IAADA,OAAC,EAADA,EAAGE,SAAmB,OAAVrB,QAAU,IAAVA,GAAuB,QAAboB,EAAVpB,EAAYa,mBAAW,IAAAO,OAAb,EAAVA,EAAyBN,WAAW,eAAAC,GAAa,QAAbC,EAA/ED,EAAiFO,mBAAW,IAAAN,OAAzE,EAAnBA,EAA8F1F,MAC5GiG,EAAyBN,EAC7B,IACD,CAAW,OAAVjB,QAAU,IAAVA,GAAuB,QAAblC,EAAVkC,EAAYa,mBAAW,IAAA/C,OAAb,EAAVA,EAAyBgD,WACzB1F,EACAsD,IAGJ,MAAM6C,EAA2BC,UAC7B,IACShE,GACDiE,YAAW,KAAO,IAADC,EACI,QAAjBA,EAAAxB,EAAUyB,eAAO,IAAAD,GAAjBA,EAAmBE,OAAOX,EAAM,GACjC,IAEX,CAAE,MAAOY,GACLC,QAAQC,IAAIF,EAChB,GAWElB,EAAOa,UACT,GAAIhE,EACAwE,UAGJ,GAAIlH,EAAJ,CACI,MAAMmH,QAAYpD,EAAY/D,GAC1BmH,EACA3C,EAAa2C,GAEbzC,EAAYtE,EAAE,wCAGtB,MAEA,GAAIsD,EAAW,CACX,MAAM0D,EAAQ/D,EAAU+C,MAAKC,GAAKA,EAAEgB,cAAgB3D,EAAU6C,OAC1Da,IAAUxD,GACVkB,GAAY,GACZF,GAAW,GACX0C,GAASF,IACFjD,EACP+C,KAEAxC,EAAYtE,EAAE,kCAEtB,GAGE8G,GAAcR,UAChB,IACI,MAAMxC,QAAqBF,IAC3Be,GAAkB,GAClBL,EAAY,IACZF,EAAaN,GACbU,GAAW,GACXE,GAAY,EAChB,CAAE,MAAOiC,GACc,oBAAfA,EAAMQ,OACN7C,EAAYtE,EAAE2G,EAAMS,UACpBzC,GAAkB,GAE1B,GAGEuC,GAAWZ,UACb,GAAIU,EAAO,CACP,MAAMD,QAAYpD,EAAYqD,GAC1BD,EACA3C,EAAa2C,GAEbzC,EAAYtE,EAAE,wCAEtB,GAkCEqH,GAAmB9B,KAASe,MAAOgB,EAAOnE,MAC5CoE,EAAAA,EAAAA,GAAqB,CACjBpB,KAAMmB,EAAMnB,KACZC,YAAa,CACThG,MAAOoH,OAAOrE,EAAMsE,iBAGtBC,EAAAA,EAAAA,KAAoB,CACtBC,WAAY,CAAC,CACT7E,GAAIwE,EAAMxE,GACVsD,YAAa,IACNkB,EAAMlB,YACThG,MAAOoH,OAAOrE,EAAMsE,YAG9B,GACH,MAEGG,IAAwBtC,EAAAA,EAAAA,aAAY+B,GAAkB,IAUtD7B,GAAmBA,KACrB,GAAIR,EAAUyB,SAAWzB,EAAUyB,QAAQoB,QAAS,CACjC7C,EAAUyB,QAAQoB,QAAQC,iBAAiB,SAGnDC,SAAQf,IACPA,EAAMgB,YACNhB,EAAMgB,UAAY,KACtB,GAER,MACIpB,QAAQD,MAAM,gDAElB9C,IACAc,GAAkB,EAAM,EAW5B,OACI3D,EAAAA,EAAAA,MAACc,EAAc,CAAApB,SAAA,CACV2D,GACK7D,EAAAA,EAAAA,KAAA,OAAAE,SAAM2D,KAEJ7D,EAAAA,EAAAA,KAACyH,IAAW,CACRC,IAAKlD,EACLmD,IAAKhE,EACLtD,MAAM,MACNuH,OAAO,MACPC,OA3CDC,KACf9D,GAAW,EAAK,EA2CA+D,QAxCAC,KAChBhE,GAAW,EAAM,EAwCDC,SAAUA,EACVF,QAASA,EACTkE,WA5EGnC,UACnB,IAAgB,IAAZ/B,IAAsBf,EAAe,CAAC,IAADkF,EACrC,MAAMpB,EAAQpH,EAAoB8F,MAAKC,IAAC,IAAA0C,EAAA,OAAI1C,EAAEE,QAAmB,OAAVrB,QAAU,IAAVA,GAAuB,QAAb6D,EAAV7D,EAAYa,mBAAW,IAAAgD,OAAb,EAAVA,EAAyB/C,WAAW,IACvF0B,IAAiC,QAAxBoB,EAACpB,EAAMlB,YAAYhG,aAAK,IAAAsI,EAAAA,EAAI,GAAGE,QAAQ,KAAOpB,OAAOrE,EAAMsE,QAAQmB,QAAQ,IACpFhB,GAAsBN,EAAOnE,EAErC,MA0EI3C,EAAAA,EAAAA,KAAC0B,EAAqB,CAClBC,MAAOW,EACPT,aAAcA,EACdD,aAzGSA,KACjByC,GAAe,EAAK,EAyGZvC,eAAgBA,EAChBC,mBA/BeA,KACnBD,EACAkD,KAEAsB,IACJ,EA2BQtE,kBAAmBgB,IAEtBoB,IAEIpE,EAAAA,EAAAA,KAACqI,EAAW,CACRhJ,KAAM+E,EACN7E,QAAS8E,EACTjF,KAAgB,OAAVkF,QAAU,IAAVA,GAAuB,QAAbjC,EAAViC,EAAYa,mBAAW,IAAA9C,OAAb,EAAVA,EAAyB+C,WAC/B9F,KAhHAwG,UAEb,GAAIxB,EAAY,OACYgE,EAAAA,EAAAA,KAAW,CAC/B5D,UAAqB,OAAVJ,QAAU,IAAVA,OAAU,EAAVA,EAAYI,UACvB6D,UAAqB,OAAVjE,QAAU,IAAVA,OAAU,EAAVA,EAAYiE,UACvBC,YAAuB,OAAVlE,QAAU,IAAVA,OAAU,EAAVA,EAAYkE,YACzBC,YAAuB,OAAVnE,QAAU,IAAVA,OAAU,EAAVA,EAAYlE,MACzB+E,YAAa,CAAEC,WAAYxF,OAG3B6D,IACAY,GAAe,GAEvB,OAsGiB,C,gDC3UzB,IAAIqE,EAAY1B,OAAO2B,OACnB,SAAkB/I,GACd,MAAwB,kBAAVA,GAAsBA,IAAUA,CAClD,EAUJ,SAASgJ,EAAeC,EAAWC,GAC/B,GAAID,EAAUE,SAAWD,EAAWC,OAChC,OAAO,EAEX,IAAK,IAAIC,EAAI,EAAGA,EAAIH,EAAUE,OAAQC,IAClC,GAdSC,EAcIJ,EAAUG,GAdPE,EAcWJ,EAAWE,KAbtCC,IAAUC,GAGVR,EAAUO,IAAUP,EAAUQ,IAW1B,OAAO,EAfnB,IAAiBD,EAAOC,EAkBpB,OAAO,CACX,CAyBA,QAvBA,SAAoBC,EAAUC,GAE1B,IAAIC,OADY,IAAZD,IAAsBA,EAAUR,GAEpC,IACIU,EADAC,EAAW,GAEXC,GAAa,EAejB,OAdA,WAEI,IADA,IAAIC,EAAU,GACLC,EAAK,EAAGA,EAAKC,UAAUZ,OAAQW,IACpCD,EAAQC,GAAMC,UAAUD,GAE5B,OAAIF,GAAcH,IAAaO,MAAQR,EAAQK,EAASF,KAGxDD,EAAaH,EAASU,MAAMD,KAAMH,GAClCD,GAAa,EACbH,EAAWO,KACXL,EAAWE,GALAH,CAOf,CAEJ,C", "sources": ["pages/layout/video/components/configModal/style.js", "pages/layout/video/components/configModal/index.js", "pages/layout/video/style.js", "pages/layout/video/index.js", "../node_modules/memoize-one/dist/memoize-one.esm.js"], "names": ["ConfigModalContainer", "styled", "div", "_ref", "data", "open", "onOk", "<PERSON><PERSON><PERSON>", "t", "useTranslation", "inputVariableNumber", "useNumberInputVariable", "value", "setValue", "useState", "useEffect", "_jsx", "_Fragment", "children", "VModal", "title", "width", "onCancel", "footer", "_jsxs", "Select", "options", "style", "fieldNames", "label", "onChange", "e", "className", "Space", "direction", "VButton", "block", "onClick", "VideoContainer", "COLOR", "splitBack", "ContextMenuContainer", "ContextMenuRightClick", "domId", "handelConfig", "layoutConfig", "isCameraStream", "handelCameraStream", "isSubTaskSample", "i18n", "ContextMenu", "_ref2", "_configData$data_sour3", "_configData$data_sour5", "id", "isHistory", "item", "videoList", "useSelector", "state", "template", "widgetData", "optSample", "project", "subTaskSample", "subTask", "isFinishMain", "getVideoUrl", "getUserMediaStream", "stopCamera", "cameraStream", "cameraVideoStream", "useVideo", "initWidget", "useWidget", "playerUrl", "setPlayerUrl", "mediaErr", "setMediaErr", "playing", "setPlaying", "controls", "setControls", "setIsCameraStream", "configModal", "setConfigModal", "configData", "setConfigData", "playerRef", "useRef", "widget_id", "findWidget", "findItem", "handelEnd", "useCallback", "debounce", "handelStopCamera", "init", "_configData$data_sour", "data_source", "input_code", "_inputVariableNumber$", "_inputVariableNumber$2", "index", "find", "f", "_configData$data_sour2", "code", "default_val", "getCreateTimeOriginIndex", "async", "setTimeout", "_playerRef$current", "current", "seekTo", "error", "console", "log", "startCamera", "res", "video", "sample_code", "getVideo", "name", "message", "subBatchInputVar", "input", "dispatchSyncInputVar", "Number", "played", "batchUpdateInputVar", "input_vars", "subCacheBatchInputVar", "wrapper", "querySelectorAll", "for<PERSON>ach", "srcObject", "ReactPlayer", "ref", "url", "height", "onPlay", "handlePlay", "onPause", "handlePause", "onProgress", "_input$default_val$va", "_configData$data_sour4", "toFixed", "ConfigModal", "saveWidget", "parent_id", "widget_type", "widget_name", "safeIsNaN", "isNaN", "areInputsEqual", "newInputs", "lastInputs", "length", "i", "first", "second", "resultFn", "isEqual", "lastThis", "lastResult", "lastArgs", "calledOnce", "newArgs", "_i", "arguments", "this", "apply"], "sourceRoot": ""}