"use strict";(self.webpackChunkzhongji_experiment_react=self.webpackChunkzhongji_experiment_react||[]).push([[7813],{17813:(e,t,n)=>{n.r(t),n.d(t,{default:()=>ue});var l=n(65043),i=n(80077),a=n(74117),o=n(21256),r=n(36950),d=n(80231),s=n(63804),u=n(83463),c=n(81929),v=n.n(c),p=n(16271);const m={"\u5e73\u94fa":"\u5e73\u94fa","\u81ea\u9002\u5e94":"\u81ea\u9002\u5e94","\u5c45\u4e2d\u663e\u793a":"\u5c45\u4e2d\u663e\u793a"},g={[m["\u5e73\u94fa"]]:{backgroundRepeat:"repeat"},[m["\u81ea\u9002\u5e94"]]:{backgroundSize:"contain",backgroundRepeat:"no-repeat",backgroundPosition:"center"},[m["\u5c45\u4e2d\u663e\u793a"]]:{backgroundPosition:"center",backgroundRepeat:"no-repeat"}},h={"\u9ed8\u8ba4":"\u9ed8\u8ba4","\u6570\u503c\u5728\u5de6,\u5355\u4f4d\u5728\u53f3":"\u6570\u503c\u5728\u5de6,\u5355\u4f4d\u5728\u53f3","\u5de6\u5bf9\u9f50\uff0c\u6570\u503c\u4e0e\u5355\u4f4d\u4e24\u884c\u663e\u793a":"\u5de6\u5bf9\u9f50\uff0c\u6570\u503c\u4e0e\u5355\u4f4d\u4e24\u884c\u663e\u793a","\u5de6\u5bf9\u9f50\uff0c\u6570\u503c\u4e0e\u5355\u4f4d\u5355\u884c\u663e\u793a":"\u5de6\u5bf9\u9f50\uff0c\u6570\u503c\u4e0e\u5355\u4f4d\u5355\u884c\u663e\u793a","\u5c45\u4e2d\u5bf9\u9f50":"\u5c45\u4e2d\u5bf9\u9f50"},b={[h["\u9ed8\u8ba4"]]:"list-item-1",[h["\u6570\u503c\u5728\u5de6,\u5355\u4f4d\u5728\u53f3"]]:"list-item-2",[h["\u5de6\u5bf9\u9f50\uff0c\u6570\u503c\u4e0e\u5355\u4f4d\u4e24\u884c\u663e\u793a"]]:"list-item-3",[h["\u5de6\u5bf9\u9f50\uff0c\u6570\u503c\u4e0e\u5355\u4f4d\u5355\u884c\u663e\u793a"]]:"list-item-4",[h["\u5c45\u4e2d\u5bf9\u9f50"]]:"list-item-5"},x=Object.assign({},...Array.from({length:23},((e,t)=>{const n=8+t;return{[`${n}px`]:`${n}px`}}))),f={backgroundImageType:m["\u5e73\u94fa"],backgroundColor:"#ffffff",colNumber:1,rowNumber:1,updateFreq:180},y={headWidth:"100",headHeight:"32",fontSize:"12",showType:h["\u9ed8\u8ba4"],fontColor:"#6c74ff",headBgColor:"#bdccff",colNumber:2,rowNumber:3},j={defName:"\u7ec4",index:1,groupName:"\u7ec4",UUID:crypto.randomUUID()};var w=n(81143);const A=w.Ay.div`
    width: 100%;
    height: 100%;

    .special-head-container {
        width: 100%;
        height: 100%;
        position: relative;
        overflow: hidden;

        .drag-box {
            border: 1px solid #e1e1e1;
            padding: 2px;
            cursor: move;
            display: inline-block;
            position: absolute;

            .drag-box-name {
                position: absolute;
                top: 0;
                transform: translate(5px, -95%);
                font-size: 12px;
                z-index: 2;
                background: #fff;
                padding: 0 2px;
                line-height: 1;
            }

            .drag-box-item {
                position: relative;
                z-index: 1;
                display: grid;
                grid-column-gap: 2px;
                grid-row-gap: 2px;
                
                .list-item {
                    border: 1px solid #666666;
                    padding: 2px;
                    //margin-top: 2px;
                    font-size: 12px;
                    overflow: hidden;

                    
                    .item-div {
                        display: inline-block;
                        margin-right: 8px;
                        &:last-child {
                            margin-right: 0;
                        }
                    }

                    &.list-item-1{
                    }
                    &.list-item-2{
                        display: flex;
                        .item-unit{
                            flex: 1;
                            text-align: right;
                        }
                    }
                    &.list-item-3{
                        .item-unit{
                            display: block;
                        }
                    }
                    &.list-item-4{
                    }
                    &.list-item-5{
                        display: flex;
                        align-content: center;
                        justify-content: center;
                        .item-div{
                            display: flex;
                            align-items: center;
                            justify-content: center;
                        }
                    }
                }
            }

        }
    }
`;var C=n(70579);const N=e=>{var t;let{data:n,configData:l,dragOpen:o,updateDragPosition:d}=e;const s=(0,p.A)(),u=(0,i.d4)((e=>e.global.unitList)),c=(null===l||void 0===l?void 0:l.attributeData)||{},m=(null===l||void 0===l?void 0:l.headingData)||[],h=(null===l||void 0===l?void 0:l.dragPosition)||{},{t:x}=(0,a.Bd)(),f=e=>{const{rowNumber:t}=e||1,{colNumber:n}=e||1;return{gridTemplate:`repeat(${t}, 1fr) / repeat(${n}, 1fr)`}},y={cursor:o?"move":"initial"},j=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const t=(arguments.length>1&&void 0!==arguments[1]?arguments[1]:0)+1;if(null!==h&&void 0!==h&&h[e.UUID])return null===h||void 0===h?void 0:h[e.UUID];return{x:5*(t%10===0?10:t%10)+2*(Math.ceil(t/10)-1),y:5*Math.ceil(t/10)}};return(0,C.jsx)(A,{children:(0,C.jsx)("div",{className:"special-head-container",style:{backgroundImage:`url(${null===c||void 0===c?void 0:c.backgroundImage})`,backgroundColor:(null===c||void 0===c?void 0:c.backgroundColor)||"#ffffff",...g[null===c||void 0===c?void 0:c.backgroundImageType]},children:null===m||void 0===m||null===(t=m.groupList)||void 0===t?void 0:t.map(((e,t)=>{var l;return(null===e||void 0===e||null===(l=e.transfer)||void 0===l?void 0:l.length)>0?(0,C.jsx)(v(),{bounds:"parent",disabled:!o,defaultPosition:j(e,t),position:j(e,t),onStop:(t,n)=>((e,t,n)=>{const l={...h,[e]:{x:t,y:n}};d(l)})(e.UUID,n.x,n.y),children:(0,C.jsxs)("div",{className:"drag-box drag-container-box",style:{...y,marginTop:!0===e.groupNameIsShow?"10px":0},children:[!0===e.groupNameIsShow?(0,C.jsx)("div",{className:"drag-box-name",children:x(e.groupName||e.defName)}):null,(0,C.jsx)("div",{className:"drag-box-item",style:f(e),children:e.transfer.map(((t,l)=>{const i=(e.rowNumber||0)*(e.colNumber||0),a=(e=>{const t=e||{};return`list-item ${b[null===t||void 0===t?void 0:t.showType]}`})(e),o=(e=>{const t=e||{};return{width:null!==t&&void 0!==t&&t.headWidth?`${null===t||void 0===t?void 0:t.headWidth}px`:"auto",height:null!==t&&void 0!==t&&t.headHeight?`${null===t||void 0===t?void 0:t.headHeight}px`:"auto",backgroundColor:(null===t||void 0===t?void 0:t.headBgColor)||"#ffffff",color:(null===t||void 0===t?void 0:t.fontColor)||"#000000",fontSize:null!==t&&void 0!==t&&t.fontSize?`${null===t||void 0===t?void 0:t.fontSize}px`:"14px"}})(e);if(l>=i)return null;const d=(e=>{var t,l,i,a,o,d;const v=(null===s||void 0===s||null===(t=s.find((e=>e.code===c.doubleArray)))||void 0===t?void 0:t.double_array_tab)||{},p=(null===v||void 0===v||null===(l=v.columns)||void 0===l?void 0:l.find((t=>t.code===e.code)))||{},m=n[p.code]||{},g=null===(i=(0,r.tJ)(null===m||void 0===m?void 0:m.value,null===p||void 0===p||null===(a=p.typeParam)||void 0===a?void 0:a.dimensionId,null===e||void 0===e?void 0:e.unit))||void 0===i?void 0:i.toFixed(null===e||void 0===e?void 0:e.decimal),h=null!==p&&void 0!==p&&null!==(o=p.typeParam)&&void 0!==o&&o.dimensionId?null===u||void 0===u||null===(d=u.find((e=>{var t;return e.id===(null===p||void 0===p||null===(t=p.typeParam)||void 0===t?void 0:t.dimensionId)})))||void 0===d?void 0:d.units:[],b=(null===h||void 0===h?void 0:h.find((t=>t.id===(null===e||void 0===e?void 0:e.unit))))||{};return{name:(null===p||void 0===p?void 0:p.showName)||"",unitStr:g,unitName:(null===b||void 0===b?void 0:b.name)||""}})(t);return(0,C.jsxs)("div",{className:a,style:o,children:[(0,C.jsx)("div",{className:"item-div item-name",children:x(null===d||void 0===d?void 0:d.name)}),(0,C.jsxs)("div",{className:"item-div item-unit",children:[null===d||void 0===d?void 0:d.unitStr,x(null===d||void 0===d?void 0:d.unitName)]})]},l)}))})]})},e.UUID):null}))})})},I=w.Ay.div`
    width: 100%;
    height: 100%;
    .container-box{
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        .grid-container {
            flex: 1;
            padding: 5px;
            display: grid;
            height: 100%;
            grid-template-rows: ${e=>`repeat(${e.rowNumber}, 1fr)`};
            grid-template-columns: ${e=>`repeat(${e.colNumber}, 1fr)`};
            gap: 5px;
            .grid-item {
                border: 1px solid #ccc;
            }
        }
        .pagination-box{
            text-align: center;
            padding: 8px;
        }
    }
    
`,k=e=>{let{config:t,dragOpen:n,updateDragPosition:i,dataSource:a=[]}=e;const o=(null===t||void 0===t?void 0:t.attributeData)||{},r=(0,l.useRef)(null),[d,s]=(0,l.useState)(1),c=()=>((null===o||void 0===o?void 0:o.rowNumber)||0)*((null===o||void 0===o?void 0:o.colNumber)||0)||0,v=Array.from({length:c()},((e,t)=>({[`${t+1}`]:{value:""}})));(0,l.useEffect)((()=>((()=>{if(null!==r&&void 0!==r&&r.current&&clearTimeout(null===r||void 0===r?void 0:r.current),o.isAutoSwitch&&o.switchTime){const e=((null===o||void 0===o?void 0:o.rowNumber)||0)*((null===o||void 0===o?void 0:o.colNumber)||0)||0,t=a.length||0;s(1),r.current=setInterval((()=>{s((n=>n+1>Math.ceil(t/e)?1:n+1))}),1e3*o.switchTime)}})(),()=>clearTimeout(r.current))),[t]);const p=e=>{i(e)};return(0,C.jsx)(I,{rowNumber:(null===o||void 0===o?void 0:o.rowNumber)||0,colNumber:(null===o||void 0===o?void 0:o.colNumber)||0,children:(0,C.jsxs)("div",{className:"container-box",children:[(0,C.jsx)("div",{className:"grid-container",children:a.length<=0?v.slice((d-1)*c(),d*c()).map(((e,l)=>(0,C.jsx)("div",{className:"grid-item",children:(0,C.jsx)(N,{data:e||{},configData:t,dragOpen:n,updateDragPosition:p},`${d}-${l}-${c()}`)},`${d}-${l}-${c()}`))):a.slice((d-1)*c(),d*c()).map(((e,l)=>(0,C.jsx)("div",{className:"grid-item",children:(0,C.jsx)(N,{data:e||{},configData:t,dragOpen:n,updateDragPosition:p},`${d}-${l}-${c()}`)},`${d}-${l}-${c()}`)))}),c()>0?(0,C.jsx)("div",{className:"pagination-box",children:(0,C.jsx)(u.A,{simple:!0,current:d,pageSize:c(),total:a.length||0,onChange:(e,t)=>{s(e)}})}):null]})})};var S=n(8918),D=n(6051),_=n(95206),T=n(75440),$=(n(87049),n(83720)),R=n(25055),E=n(47419),P=n(11645),L=n(97914),U=n(12624),O=n(36497),z=n(42999),F=n(97588),V=n(67998),M=n(56543);const W=Object.keys(m).map((e=>({label:e,value:m[e]}))),{TextArea:q}=$.A,{Item:B,useForm:H}=R.A,G=(0,l.forwardRef)(((e,t)=>{const{t:n}=(0,a.Bd)(),[i]=H(),{config:o,changeConfig:r}=e,d=(null===o||void 0===o?void 0:o.attributeData)||{},[s,u]=(0,l.useState)(!1),c=e=>{u(!1)};return(0,l.useEffect)((()=>{null!==d&&void 0!==d&&d.backgroundImageType&&i.setFieldsValue({...d})}),[d]),(0,l.useImperativeHandle)(t,(()=>({getData:async()=>{const e=await i.validateFields();return e||null}}))),(0,C.jsx)("div",{className:"attribute-content",children:(0,C.jsxs)(R.A,{form:i,labelCol:{style:{width:"80px"}},initialValues:f,onValuesChange:e=>{const t=i.getFieldsValue();r(t)},children:[(0,C.jsx)("div",{className:"attribute-title",children:n("\u663e\u793a\u8bbe\u7f6e")}),(0,C.jsxs)(E.A,{children:[(0,C.jsx)(P.A,{span:12,children:(0,C.jsx)(B,{label:n("\u884c"),name:"rowNumber",children:(0,C.jsx)(L.A,{style:{width:"100%"},min:1})})}),(0,C.jsx)(P.A,{span:12,children:(0,C.jsx)(B,{label:n("\u5217"),name:"colNumber",children:(0,C.jsx)(L.A,{style:{width:"100%"},min:1})})}),(0,C.jsx)(P.A,{span:12,children:(0,C.jsx)(B,{label:n("\u662f\u5426\u81ea\u52a8\u5207\u6362"),name:"isAutoSwitch",valuePropName:"checked",children:(0,C.jsx)(U.A,{})})}),(0,C.jsx)(P.A,{span:12,children:(0,C.jsx)(B,{label:n("\u5207\u6362\u65f6\u95f4(s)"),name:"switchTime",children:(0,C.jsx)(L.A,{style:{width:"100%"},min:1})})})]}),(0,C.jsx)("div",{className:"attribute-title",children:n("\u63a7\u4ef6\u8bbe\u7f6e")}),(0,C.jsxs)(E.A,{children:[(0,C.jsx)(P.A,{span:12,children:(0,C.jsx)(B,{label:n("\u4e8c\u7ef4\u6570\u7ec4"),name:"doubleArray",children:(0,C.jsx)(V.A,{inputVariableType:M.ps.\u4e8c\u7ef4\u6570\u7ec4})})}),(0,C.jsx)(P.A,{span:12,children:(0,C.jsx)(B,{label:n("\u663e\u793a\u66f4\u65b0\u9891\u7387"),name:"updateFreq",children:(0,C.jsx)(O.A,{options:[{label:"0.1s",value:90},{label:"0.2s",value:180},{label:"0.5s",value:470},{label:"1s",value:900},{label:"2s",value:1900}]})})})]}),(0,C.jsxs)(E.A,{children:[(0,C.jsx)(P.A,{span:12,children:(0,C.jsx)(B,{label:n("\u80cc\u666f\u56fe\u663e\u793a"),name:"backgroundImageType",children:(0,C.jsx)(O.A,{options:null===W||void 0===W?void 0:W.map((e=>({...e,label:n(e.label)})))})})}),(0,C.jsx)(P.A,{span:12,children:(0,C.jsx)(B,{rules:[{required:!1,message:n("\u8bf7\u9009\u62e9")}],label:n("\u80cc\u666f\u56fe"),name:"backgroundImage",children:(0,C.jsx)(F.A,{src:i.getFieldValue("backgroundImage"),btnCLick:e=>{u(!0)},btnTitle:n("\u9009\u62e9\u56fe\u7247"),open:s,onCancel:c,onChange:e=>{i.setFieldValue("icon",e),c()},modalTitle:n("\u9009\u62e9\u56fe\u7247")})})})]}),(0,C.jsxs)(E.A,{children:[(0,C.jsx)(P.A,{span:12,children:(0,C.jsx)(B,{label:n("\u80cc\u666f\u8272"),name:"backgroundColor",children:(0,C.jsx)(z.A,{})})}),(0,C.jsx)(P.A,{span:12,children:(0,C.jsx)(B,{label:n("\u63cf\u8ff0"),name:"describe",children:(0,C.jsx)(q,{})})})]})]})})}));var J=n(32513),K=n(12159),Q=n(94293);const X=e=>{let{value:t=[],onChange:n,setSelectedGroupIndex:l,setSelectedGroupIndexChange:i}=e;const{t:o}=(0,a.Bd)();return(0,C.jsxs)("div",{children:[(0,C.jsxs)("div",{className:"heading-content-group-top",children:[(0,C.jsx)("div",{className:"heading-content-group-top-name",children:o("\u5217\u8868\u7ec4")}),(0,C.jsxs)("div",{className:"heading-content-group-top-btnbox",children:[(0,C.jsx)(_.Ay,{size:"small",onClick:()=>{var e;const l=t.length,a=((null===t||void 0===t||null===(e=t.at(-1))||void 0===e?void 0:e.index)||l)+1,o={...j,...y,groupName:j.groupName+a,defName:j.groupName+a,index:a,UUID:crypto.randomUUID()},r=[...t,o];null===n||void 0===n||n(r),0===l&&(null===i||void 0===i||i(0))},children:o("\u6dfb\u52a0")}),(0,C.jsx)(_.Ay,{size:"small",onClick:()=>{if(t.length<=1)return;const e=null===t||void 0===t?void 0:t.filter(((e,t)=>t!==l));setTimeout((()=>{n(e),null===i||void 0===i||i(0)}),0)},children:o("\u5220\u9664")})]})]}),(0,C.jsx)("div",{className:"heading-content-group-list",children:t.map(((e,t)=>(0,C.jsx)("div",{className:"heading-content-group-list-item "+(t===l?"active":""),onClick:()=>{i(t)},children:o(e.groupName||e.defName)},e.UUID)))})]})};var Z=n(10202);const Y=e=>{let{value:t=[],onChange:n,doubleArrayTabData:i,rowKey:o="code",transferSelectIndex:r,transferSelectIndexChange:d}=e;const{t:s}=(0,a.Bd)(),[u,c]=(0,l.useState)();return(0,C.jsx)(C.Fragment,{children:(0,C.jsx)(Z.A,{listStyle:{width:"100%",height:"30vh"},rowKey:o,isMove:!0,oneWay:!0,dataSource:(null===i||void 0===i?void 0:i.columns)||[],targetKeys:Array.isArray(t)&&t.length>0?null===t||void 0===t?void 0:t.map((e=>e[o])):[],onChange:e=>{const t=e.map((e=>({[o]:e})));null===n||void 0===n||n(t)},onChangeDelWay:e=>{null===n||void 0===n||n(t.filter((t=>t[o]!==e[o])))},onChangeMove:e=>{const l=e.map((e=>t.find((t=>t[o]===e))));null===n||void 0===n||n(l),d(e.findIndex((e=>e===u[o])))},onChangeWay:e=>{e?(c(e),d(t.findIndex((t=>t[o]===e[o])))):(c(null),d(null))},render:e=>null!==e&&void 0!==e&&e.code?`${s(null===e||void 0===e?void 0:e.showName)}(${null===e||void 0===e?void 0:e.code})`:s(null===e||void 0===e?void 0:e.showName),wayRender:e=>null!==e&&void 0!==e&&e.code?`${s(null===e||void 0===e?void 0:e.showName)}(${null===e||void 0===e?void 0:e.code})`:s(null===e||void 0===e?void 0:e.showName)})})},ee=Object.keys(x).map((e=>({label:e,value:x[e]}))),te=Object.keys(h).map((e=>({label:e,value:h[e]}))),{Item:ne,useForm:le}=R.A,ie=(0,l.forwardRef)(((e,t)=>{var n,i,o;let{config:d,attributeFormData:s}=e;const{t:u}=(0,a.Bd)(),c=(0,p.A)(),v=(null===d||void 0===d?void 0:d.headingData)||{},[m]=le(),g={groupList:[{...j,...y,groupName:j.groupName+1,defName:j.groupName+1,transfer:[]}]},[h,b]=(0,l.useState)(0),[x,f]=(0,l.useState)(null),w=()=>{const e=null===c||void 0===c?void 0:c.find((e=>e.code===s.doubleArray));return(null===e||void 0===e?void 0:e.double_array_tab)||{}},A=function(){var e;let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:x;return((null===(e=w())||void 0===e?void 0:e.columns)||[])[t]||{}};return(0,l.useEffect)((()=>{null!==v&&void 0!==v&&v.groupList&&(null===v||void 0===v?void 0:v.groupList.length)>0&&m.setFieldsValue({...v})}),[v]),(0,l.useImperativeHandle)(t,(()=>({getData:async()=>{const e=await m.validateFields();return e||null}}))),(0,C.jsx)(R.A,{form:m,initialValues:g,labelCol:{style:{width:"70px"}},children:(0,C.jsxs)("div",{className:"heading-content",children:[(0,C.jsx)("div",{className:"heading-content-group",children:(0,C.jsx)(R.A.Item,{noStyle:!0,name:"groupList",children:(0,C.jsx)(X,{setSelectedGroupIndex:h,setSelectedGroupIndexChange:e=>{b(e),f(null)}})})}),(0,C.jsxs)("div",{className:"heading-content-set",children:[(0,C.jsx)("div",{className:"heading-content-set-title",children:u("\u7ec4\u8bbe\u7f6e")}),(0,C.jsxs)(E.A,{children:[(0,C.jsx)(P.A,{span:8,children:(0,C.jsxs)("div",{className:"group-name-box",children:[(0,C.jsx)("div",{className:"group-name-box-left",children:(0,C.jsx)(ne,{labelCol:{style:{width:"auto"}},label:"",valuePropName:"checked",name:["groupList",h,"groupNameIsShow"],children:(0,C.jsxs)(J.A,{children:[u("\u7ec4\u540d\u79f0"),":"]})})}),(0,C.jsx)("div",{className:"group-name-box-right",children:(0,C.jsx)(ne,{label:"",name:["groupList",h,"groupName"],children:(0,C.jsx)($.A,{})})})]})}),(0,C.jsx)(P.A,{span:8,children:(0,C.jsx)(ne,{label:u("\u5217\u6570"),name:["groupList",h,"colNumber"],children:(0,C.jsx)(L.A,{style:{width:"100%"}})})}),(0,C.jsx)(P.A,{span:8,children:(0,C.jsx)(ne,{label:u("\u884c\u6570"),name:["groupList",h,"rowNumber"],children:(0,C.jsx)(L.A,{style:{width:"100%"}})})})]}),(0,C.jsxs)(E.A,{children:[(0,C.jsx)(P.A,{span:8,children:(0,C.jsx)(ne,{label:u("\u8868\u5934\u5bbd\u5ea6"),name:["groupList",h,"headWidth"],children:(0,C.jsx)($.A,{suffix:"px"})})}),(0,C.jsx)(P.A,{span:8,children:(0,C.jsx)(ne,{label:u("\u8868\u5934\u9ad8\u5ea6"),name:["groupList",h,"headHeight"],children:(0,C.jsx)($.A,{suffix:"px"})})}),(0,C.jsx)(P.A,{span:8,children:(0,C.jsx)(ne,{label:u("\u663e\u793a\u5b57\u53f7"),name:["groupList",h,"fontSize"],children:(0,C.jsx)(O.A,{options:ee})})})]}),(0,C.jsxs)(E.A,{children:[(0,C.jsx)(P.A,{span:8,children:(0,C.jsx)(ne,{label:u("\u663e\u793a\u65b9\u5f0f"),name:["groupList",h,"showType"],children:(0,C.jsx)(O.A,{options:null===te||void 0===te?void 0:te.map((e=>({...e,label:u(e.label)})))})})}),(0,C.jsx)(P.A,{span:8,children:(0,C.jsx)(ne,{label:u("\u6587\u5b57\u8272"),name:["groupList",h,"fontColor"],children:(0,C.jsx)(z.A,{})})}),(0,C.jsx)(P.A,{span:8,children:(0,C.jsx)(ne,{label:u("\u8868\u5934\u80cc\u666f\u8272"),name:["groupList",h,"headBgColor"],children:(0,C.jsx)(z.A,{})})})]}),(0,C.jsxs)("div",{className:"group-set-box",children:[(0,C.jsx)("div",{className:"group-set-box-transfer",children:(0,C.jsx)(ne,{style:{overflow:"hidden"},label:"",name:["groupList",h,"transfer"],children:(0,C.jsx)(Y,{doubleArrayTabData:w(),transferSelectIndex:x,transferSelectIndexChange:e=>{if(f(e),e>=0){var t;const l=A(e);var n;if(l&&null!==l&&void 0!==l&&l.typeParam&&null!==l&&void 0!==l&&null!==(t=l.typeParam)&&void 0!==t&&t.unitId&&!m.getFieldValue(["groupList",h,"transfer",e,"unit"]))m.setFieldValue(["groupList",h,"transfer",e,"unit"],(null===l||void 0===l||null===(n=l.typeParam)||void 0===n?void 0:n.unitId)||void 0)}}})})}),(0,C.jsxs)("div",{className:"group-set-formitem-box",children:["Number"===(null===(n=A())||void 0===n?void 0:n.type)?(0,C.jsx)(ne,{labelCol:{span:24},wrapperCol:{span:24},layout:"vertical",label:u("\u5355\u4f4d"),name:["groupList",h,"transfer",x,"unit"],children:(0,C.jsx)(K.A,{disabled:(0,r.Im)(x),dimensionId:null===(i=A())||void 0===i||null===(o=i.typeParam)||void 0===o?void 0:o.dimensionId,size:"small"})}):null,(0,C.jsx)(ne,{labelCol:{span:24},wrapperCol:{span:24},layout:"vertical",label:u("\u5c0f\u6570\u4f4d\u6570"),name:["groupList",h,"transfer",x,"decimal"],children:(0,C.jsx)(O.A,{disabled:(0,r.Im)(x),showSearch:!0,optionFilterProp:"label",size:"small",options:(0,Q.hg)({t:u})})})]})]})]})]})})})),ae=w.Ay.div`
    background: #fff;
    padding: 5px 10px 10px;
    .attribute-content {
        .attribute-title{
            font-size: 14px;
            margin-bottom: 8px;
        }
    }
    .heading-content{
        display: flex;
        .heading-content-group{
            width: 140px;
            margin-right: 24px;
            .heading-content-group-top{
                display: flex;
                .heading-content-group-top-name{
                    flex: 1;
                }
                .heading-content-group-top-btnbox{
                    .ant-btn:last-child{
                        margin-left: 8px;
                    }
                }
            }
            .heading-content-group-list{
                margin-top: 12px;
                .heading-content-group-list-item{
                    margin-bottom: 8px;
                    padding: 2px 4px;
                    border-radius: 4px;
                    cursor: pointer;
                    &:hover,&.active{
                        background: rgba(0,0,0,.2);
                    }
                }
            }
        }
        .heading-content-set{
            flex: 1;
            overflow: hidden;
            .heading-content-set-title{
                font-size: 14px;
                margin-bottom: 8px;
            }
            
            .group-name-box{
                display: flex;
                .group-name-box-left{
                    width: 70px;
                    .ant-form-item-control-input-content{
                        text-align: right;
                    }
                }
                .group-name-box-right{
                    flex: 1;
                }
            }
            
            .group-set-box{
                margin-top: 12px;
                flex: 1;
                display: flex;
                .group-set-box-transfer{
                    flex: 1;
                    overflow: hidden;
                    .transfer-content{
                        .ant-transfer {
                            flex: 1;
                            overflow: hidden;
                        }
                    }
                    .layout-right{
                        width: 50px;
                    }
                    .ant-transfer-list{
                        overflow: hidden;
                    }
                }
                .group-set-formitem-box{
                    margin-left: 12px;
                    overflow: hidden;
                    width: 120px;
                    
                }
            }
        }
    }
    .footer-div{
        margin-top: 20px;
        text-align: right;
    }
`,oe=e=>{let{open:t,setOpen:n,config:i,updateConfig:o}=e;const{t:r}=(0,a.Bd)(),d=(0,l.useRef)(null),s=(0,l.useRef)(null),[u,c]=(0,l.useState)((null===i||void 0===i?void 0:i.attributeData)||{}),v=()=>{n(!1)},p=[{key:"1",label:r("\u5c5e\u6027"),children:(0,C.jsx)(G,{ref:d,config:i,changeConfig:e=>{c(e)}})},{key:"2",label:r("\u8868\u5934"),children:(0,C.jsx)(ie,{ref:s,config:i,attributeFormData:u})}];return(0,C.jsx)(T.A,{open:t,title:r("\u8bbe\u7f6e\u8868\u5934"),maskClosable:!1,width:"800px",footer:null,onCancel:v,children:(0,C.jsxs)(ae,{children:[(0,C.jsx)(S.A,{items:p}),(0,C.jsx)("div",{className:"footer-div",children:(0,C.jsxs)(D.A,{children:[(0,C.jsx)(_.Ay,{onClick:v,children:r("\u53d6\u6d88")}),(0,C.jsx)(_.Ay,{type:"primary",onClick:async()=>{var e,t;const n=await(null===d||void 0===d||null===(e=d.current)||void 0===e?void 0:e.getData())||null,l=await(null===s||void 0===s||null===(t=s.current)||void 0===t?void 0:t.getData())||null,a={attributeData:n||(null!==i&&void 0!==i&&i.attributeData?null===i||void 0===i?void 0:i.attributeData:null),headingData:l||(null!==i&&void 0!==i&&i.headingData?null===i||void 0===i?void 0:i.headingData:null),dragPosition:(null===i||void 0===i?void 0:i.dragPosition)||null};o(a),v()},children:r("\u786e\u8ba4\u751f\u6210")})]})})]})})};var re=n(84617);const de=e=>{let{controlCompId:t}=e;const[n,i]=(0,l.useState)([]);return(0,re.A)({controlCompId:t,onMessage:(0,l.useCallback)((e=>{const{mode:t,data:n}=e,l=Object.keys(n),a=Math.max(...l.map((e=>{var t;return(null===(t=n[e])||void 0===t?void 0:t.length)||0}))),o=[];for(let i=0;i<a;i+=1){const e={};l.forEach((t=>{var l;e[t]={value:(null===(l=n[t])||void 0===l?void 0:l[i])||0}})),o.push(e)}switch(t){case 0:i(o);break;case 1:i((e=>[...e,...o]))}}),[])}),n},se=w.Ay.div`
    width: 100%;
    height: 100%;
    background: #fff;
    .attribute-content {
        .attribute-title{
            font-size: 18px;
            margin-bottom: 12px;
        }
    }
`,ue=e=>{var t,n,u;let{item:c,id:v,layoutConfig:p}=e;const{t:m}=(0,a.Bd)(),g=(0,i.d4)((e=>e.template.widgetData)),{editWidget:h}=(0,o.A)(),[b,x]=(0,l.useState)(!1),f=(0,l.useMemo)((()=>(0,r.Rm)(g,"widget_id",null===c||void 0===c?void 0:c.widget_id)),[c,g]),y=null!==(t=null===f||void 0===f?void 0:f.data_source)&&void 0!==t?t:{},j=e=>{h({...f,data_source:e})},{targetRef:w}=(0,s.A)({controlCompId:v,dataSourceType:s.d.\u4e8c\u7ef4\u6570\u7ec4,dataSourceCode:null===y||void 0===y||null===(n=y.attributeData)||void 0===n?void 0:n.doubleArray,dataCodes:(0,l.useMemo)((()=>{var e,t;const n=new Set;return null===y||void 0===y||null===(e=y.headingData)||void 0===e||null===(t=e.groupList)||void 0===t||t.forEach((e=>{var t;null===e||void 0===e||null===(t=e.transfer)||void 0===t||t.forEach((e=>{n.add(e.code)}))})),Array.from(n)}),[y]),timer:null!==(u=null===y||void 0===y?void 0:y.updateFreq)&&void 0!==u?u:180,number:-1,testStatus:1}),[A,N]=(0,l.useState)(!1),I=de({controlCompId:v});return(0,C.jsxs)(se,{ref:w,children:[(0,C.jsx)(k,{config:y,dragOpen:A,updateDragPosition:e=>{const t={...y.dragPosition,...e},n={...y,dragPosition:t};j(n)},dataSource:I}),b&&(0,C.jsx)(oe,{open:b,setOpen:x,config:y,updateConfig:j}),(0,C.jsxs)(d.A,{domId:v,layoutConfig:p,handelEditClick:!0,children:[(0,C.jsx)("div",{className:"unique-content",onClick:function(){N(!A)},children:m(A?"\u7ed3\u675f\u5e03\u5c40\u8c03\u6574":"\u5e03\u5c40\u8c03\u6574")}),(0,C.jsx)("div",{className:"unique-content",onClick:()=>x(!0),children:m("\u8bbe\u7f6e")})]})]})}},42999:(e,t,n)=>{n.d(t,{A:()=>v});var l=n(65043),i=n(37097),a=n(6051),o=n(36282),r=n(18650),d=n(81143),s=n(68374);const u=d.Ay.div`
    .color-layout {
        display: flex;
        align-items: center;
    }
 
    .background-layout {
        min-width: ${(0,s.D0)("25px")} !important;
        min-height: ${(0,s.D0)("25px")} !important;
        background-color: #000;
        border-radius: 2px;
    }
    .background-img {
        width: ${(0,s.D0)("23px")};
        height: ${(0,s.D0)("23px")};
        display: flex;
        align-items:center ;
        justify-content: center;
        cursor: pointer;
    }
    .allowed {
        cursor: not-allowed;
    }

`;var c=n(70579);const v=e=>{let{onChange:t,value:n,disabled:d=!1}=e;const[s,v]=(0,l.useState)(n);(0,l.useEffect)((()=>{v(n||"#000")}),[n]);const p=(0,c.jsx)(c.Fragment,{children:(0,c.jsx)(i.Xq,{color:s,showMoreColor:!1,onChangeComplete:e=>{const{rgb:n}=e,l=`rgba(${n.r},${n.g},${n.b},${n.a})`;v(l),t&&t(l)}})});return(0,c.jsx)(u,{children:(0,c.jsx)("div",{className:"color-layout",children:(0,c.jsxs)(a.A,{children:[(0,c.jsx)("div",{className:"background-layout",style:{backgroundColor:s}}),!d&&(0,c.jsx)(o.A,{overlayClassName:"popover-sketch-picker",content:p,title:"",trigger:"click",placement:"bottom",destroyOnHidden:!0,arrow:!1,children:(0,c.jsx)("img",{className:"background-img "+(d?"allowed":""),src:r.Dp,alt:""})})]})})})}},63804:(e,t,n)=>{n.d(t,{d:()=>s,A:()=>u});var l=n(65043),i=n(67208),a=n(36950),o=n(19853),r=n.n(o);function d(){let{threshold:e=0,rootMargin:t="0px",enableIntersectionObserver:n=!0,enablePageVisibility:i=!0,enableMutationObserver:a=!0,onVisibilityChange:o=null}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const r=(0,l.useRef)(null),d=(0,l.useRef)({isIntersecting:!1,isPageVisible:!0,displayStyle:"block",position:{top:0,left:0}}),s=(0,l.useRef)(!1),u=(0,l.useCallback)((()=>{const e=d.current,t=e.isIntersecting&&e.isPageVisible&&"none"!==e.displayStyle;t!==s.current&&(s.current=t,o&&o(t))}),[o]);return(0,l.useEffect)((()=>{if(!r.current)return()=>{};const l=[];if(n){const n=new IntersectionObserver((e=>{e.forEach((e=>{const t=d.current.isIntersecting,n=e.isIntersecting;t!==n&&(d.current={...d.current,isIntersecting:n},u(),n?console.log("\ud83d\udd0d \u5143\u7d20\u8fdb\u5165\u89c6\u53e3"):console.log("\ud83d\udc7b \u5143\u7d20\u79bb\u5f00\u89c6\u53e3"))}))}),{threshold:e,rootMargin:t});n.observe(r.current),l.push((()=>n.disconnect()))}if(i){const e=()=>{const e=!document.hidden;d.current={...d.current,isPageVisible:e},u(),e?console.log("\ud83d\udc41\ufe0f \u9875\u9762\u53d8\u4e3a\u53ef\u89c1"):console.log("\ud83d\ude48 \u9875\u9762\u53d8\u4e3a\u4e0d\u53ef\u89c1")};document.addEventListener("visibilitychange",e),l.push((()=>document.removeEventListener("visibilitychange",e)))}if(a){const e=new MutationObserver((e=>{e.forEach((e=>{if("attributes"===e.type&&"style"===e.attributeName){const e=window.getComputedStyle(r.current).display;d.current={...d.current,displayStyle:e},u(),"none"===e?console.log("\ud83d\udeab \u5143\u7d20\u88ab\u9690\u85cf (display: none)"):console.log("\u2705 \u5143\u7d20\u663e\u793a\u6837\u5f0f\u6062\u590d")}}))}));e.observe(r.current,{attributes:!0,attributeFilter:["style"]}),l.push((()=>e.disconnect()))}return u(),()=>{l.forEach((e=>e()))}}),[e,t,n,i,a,u]),{targetRef:r}}const s={daqbuffer:"daqbuffer","\u4e8c\u7ef4\u6570\u7ec4":"doubleArray","\u4e8c\u7ef4\u6570\u7ec4\u96c6\u5408":"doubleArraySet"},u=(e,t)=>{let{controlCompId:n,dataSourceType:o,dataSourceCode:s,dataCodes:u,timer:c=-1,number:v=-1,testStatus:p=1,daqCurveSelectedSampleCodes:m}=e;const g=(0,l.useRef)(!1),h=(0,l.useRef)(!1),b=(0,l.useRef)(null),x=(0,l.useRef)(!1),f=(0,l.useRef)(!1),y=(0,l.useRef)();(0,l.useRef)(t).current=t,(0,l.useEffect)((()=>{if(!s||!n||!o||!u||0===u.length)return;const e={templateName:(0,a.n1)(),controlCompId:n,dataSourceType:o,dataSourceCode:s,dataCodes:u,timer:c,number:v,testStatus:p,daqCurveSelectedSampleCodes:null!==m&&void 0!==m?m:[]};r()(e,y.current)||(null===t||void 0===t||t(),y.current=e,x.current?g.current?(0,i.pj8)({...y.current}):(0,i.i_N)({...y.current}).then((()=>{g.current=!0,h.current=!0})):g.current&&(f.current=!0))}),[n,o,s,u,c,v,p,m]);const{targetRef:j}=d({onVisibilityChange:(0,l.useCallback)((async e=>{var t,n,l;if(x.current=e,e&&y.current){if(!g.current)return await(0,i.i_N)({...y.current}),g.current=!0,void(h.current=!0);if(f.current)return(0,i.pj8)({...y.current}),void(f.current=!1)}(b.current&&clearTimeout(b.current),e&&!h.current&&null!==(t=y.current)&&void 0!==t&&t.controlCompId)&&(await(0,i.pkF)(null===(l=y.current)||void 0===l?void 0:l.controlCompId),h.current=!0);!e&&h.current&&null!==(n=y.current)&&void 0!==n&&n.controlCompId&&(b.current=setTimeout((async()=>{await(0,i.UVQ)(y.current.controlCompId),h.current=!1}),3e3))}),[])});return(0,l.useEffect)((()=>()=>{b.current&&clearTimeout(b.current),g.current&&(0,i.UWZ)(y.current.controlCompId)}),[]),{targetRef:j}}},84617:(e,t,n)=>{n.d(t,{A:()=>s});var l=n(65043),i=n(60383),a=n(80077),o=n(39713),r=n(36950),d=n(91465);const s=e=>{let{controlCompId:t,onMessage:n}=e;const s=(0,a.wA)(),{useSubscriber:u}=(0,o.A)(),c=(0,l.useRef)(),v=(0,l.useRef)(n),p=(0,l.useRef)();(0,l.useEffect)((()=>{v.current=n}),[n]),(0,l.useEffect)((()=>(m(),()=>{var e,t;null===(e=c.current)||void 0===e||null===(t=e.close)||void 0===t||t.call(e)})),[t]);const m=async()=>{const e=`${(0,r.n1)()}-ControlCompUIData-${t}-UIData`;c.current=await u(e);for await(const[t,a]of c.current){let e;try{e=i.D(a)}catch(n){try{e=JSON.parse(a)}catch(l){console.error("GridLayout\u6570\u636e\u89e3\u6790\u5931\u8d25",l)}}2===e.mode?p.current=s((0,d.J_)("\u5927\u6570\u636e\u91cf\u52a0\u8f7d\u4e2d...")):3===e.mode?s((0,d.ge)(p.current)):v.current(e)}}}},87049:(e,t,n)=>{n.d(t,{R:()=>i,a:()=>a});var l=n(81143);const i=l.Ay.div`
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 15px 0;

`,a=l.Ay.div`
    padding: 0 5px ;
    height: 50vh;
`},94293:(e,t,n)=>{n.d(t,{Ao:()=>d,Tr:()=>l,_0:()=>s,aN:()=>o,eg:()=>a,hg:()=>r,sr:()=>i});const l=[{value:8,label:"8"},{value:9,label:"9"},{value:10,label:"10"},{value:11,label:"11"},{value:12,label:"12"},{value:13,label:"13"},{value:14,label:"14"},{value:15,label:"15"},{value:16,label:"16"},{value:17,label:"17"},{value:18,label:"18"},{value:19,label:"19"},{value:20,label:"20"},{value:21,label:"21"},{value:22,label:"22"},{value:23,label:"23"}],i={BETWEEN:"between",ROWS:"rows",ROW:"row",CENTER:"center"},a=e=>{let{t:t}=e;return[{value:i.BETWEEN,label:t("\u9ed8\u8ba4")},{value:i.ROWS,label:t("\u5de6\u5bf9\u9f50,\u6570\u503c\u548c\u5355\u4f4d\u4e24\u884c\u663e\u793a")},{value:i.ROW,label:t("\u5de6\u5bf9\u9f50,\u6570\u503c\u548c\u5355\u4f4d\u5355\u884c\u663e\u793a")},{value:i.CENTER,label:t("\u5c45\u4e2d\u5bf9\u9f50")}]},o={TEST:"test",NOT_TEST:"not_test"},r=e=>{let{t:t}=e;return[{value:0,label:t("\u65e0\u5c0f\u6570")},{value:1,label:t("\u5c0f\u6570\u70b9\u540e\u4e00\u4f4d")},{value:2,label:t("\u5c0f\u6570\u70b9\u540e\u4e8c\u4f4d")},{value:3,label:t("\u5c0f\u6570\u70b9\u540e\u4e09\u4f4d")},{value:4,label:t("\u5c0f\u6570\u70b9\u540e\u56db\u4f4d")},{value:5,label:t("\u5c0f\u6570\u70b9\u540e\u4e94\u4f4d")},{value:6,label:t("\u5c0f\u6570\u70b9\u540e\u516d\u4f4d")},{value:7,label:t("\u5c0f\u6570\u70b9\u540e\u4e03\u4f4d")},{value:8,label:t("\u5c0f\u6570\u70b9\u540e\u516b\u4f4d")},{value:9,label:t("\u5c0f\u6570\u70b9\u540e\u4e5d\u4f4d")}]},d={ADD:"right",REMOVE:"left"},s={name:"xxxsds",code:"input_xxsd",variable_type:"Buffer",default_val:{value:[],isConstant:0,groups:[]},is_enable:0,is_feature:0,is_overall:1,is_fx:0,number_tab:{format:{numberRequire:"any",formatType:"auto",afterPoint:null,beforePoint:null,significantDigits:0,amendmentInterval:0,pointPosition:0,roundMode:0,threshold1:1,threshold2:1,roundType1:1,roundType2:1,roundType3:1},channel:{channelType:"\u65e0",channel:"\u65e0",isUserConversion:!1,lockChannels:[]},multipleMeasurements:{measurementCounts:1,measurementType:"min"},unit:{unitType:"\u65e0",unit:"\u65e0",isUserConversion:!1,lockChannels:[]}},reasonable_val_tab:{reasonableType:"empty",values:[0,0],defaultVal:10,minParam:10,MaxParam:10,isToResultList:!1},button_variable_tab:{isEnable:!1,content:"",position:"left",actionId:"",function:"f(x)",pic:"",buttonType:"action",script:""},button_tab:{isEnable:!1,content:"",position:"left",actionId:"",function:"f(x)",source:"action_lib",pic:"",type:"action",method:"post"},program_tab:{numericFormat:"",unit:"",isVisible:"",isDisabled:"",mode:"",isCheck:""},text_tab:{content:"",format:"single",canUseText:!1},select_tab:{selection:"list_single",group_id:"",items:[],format:"",comment:""},two_digit_array_tab:{rowCounts:1,columnCount:1,rowHeaderPlace:!0,columnHeaderPlace:!0,isRowType:!1,rowDefinition:[{title:"\u884c1",type:"text",options:[]}],columnDefinition:[{title:"\u52171",type:"text",options:[]}],columnData:[[""]]},custom_array_tab:{useType:"followComp"},control_tab:{type:"not_custom",dialog_type:"variable",control_name:"",code:"",default_name:"",related_variables:[],title:"",variables:[],signals:[],is_daq:!1},buffer_tab:{buffer_type:"ArrayQueue",size:0,size_expand:"expand",signals:[]},label_tab:{format:"",content:"",fontSize:12,fore:""},picture_tab:{src:"",showName:!1,path:"",name:""},related_var_tab:{vars:[]}}}}]);
//# sourceMappingURL=7813.1f9bfc5f.chunk.js.map