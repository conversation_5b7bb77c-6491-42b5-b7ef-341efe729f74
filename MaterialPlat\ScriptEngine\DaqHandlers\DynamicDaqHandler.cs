using Buffers;
using MQ;
using NPOI.SS.Formula.Functions;
using ScriptEngine;
using ScriptEngine.DBHandler.TableWriters;
using ScriptEngine.InputVar.InputVars;
using ScriptEngine.InstantiatedTemplate.SignalVar;
using SignalExample;
using System.Diagnostics;
using System.Linq;
using System.Reactive.Linq;
using System.Reactive.Subjects;
using System.Text.Json;
using System.Text.Json.Serialization;
using static Logging.CCSSLogger;

namespace Scripting;

/// <summary>
/// 动态采集的处理
/// 1.主要功能实现高速和周期数据采集、峰谷值采集、异常数据采集
/// </summary>
public class DynamicDaqHandler : IDisposable
{
    int bufferCount = 1000;
    private DynamicDataWriter writer;
    //订阅共有:总流订阅、异常点处理订阅、下发采集数据流订阅、后n周期订阅、筛选数据流订阅、推送UI订阅,保存二维数组订阅、保存Buffer数据订阅
    //峰谷只用到：后n周期订阅、筛选数据流订阅、保存二维数组订阅、保存Buffer数据订阅
    //UI暂时没用
    private IDisposable?
    //总流订阅
    _totalFlowSubscription,
    //异常点处理订阅
    _exceptionPointSubscription,
    //下发采集数据流订阅
    _sendDaqDataSubscription,
    //后n周期订阅
    _postNCycleSubscription,
    //筛选数据流订阅
    _filterDataSubscription,
    //保存二维数组订阅,
    _saveDoubleArraryscriptions,
    //保存Buffer数据订阅
    _saveProcessDataSubscriptions;
    // 无效周期，因为不想采集第一个周期，第一个周期为周期0不需要采集
    private double _invalidCycle = 0.00;
    // 动态采集数据流 - 支持批量处理以提升性能
    private ISubject<IList<Dictionary<string, double>>> DynamicDaqHandlerSubject = new Subject<IList<Dictionary<string, double>>>();
    //处理数据流 - 支持批量处理
    private ISubject<IList<Dictionary<string, double>>>  _ProcessDataSub = new Subject<IList<Dictionary<string, double>>>();

    //处理数据后的数据流 - 支持批量处理
    private ISubject<IList<Dictionary<string, double>>> _ProcessDataSubAffter = new Subject<IList<Dictionary<string, double>>>();   

     //处理数据流 - 支持批量处理
    private ISubject<Dictionary<string, double>>  _ProcessDataSubFG = new Subject<Dictionary<string, double>>();

    //处理数据后的数据流 - 支持批量处理
    private ISubject<Dictionary<string, double>> _ProcessDataSubAffterFG = new Subject<Dictionary<string, double>>();   

    private record DAQVariable(string Code, string Name, double Value, int Index);

    private record UICmdParam(List<DAQVariable>? VarValues);

    // 试验流程在执行的时候存放已经被采集的周期
    private HashSet<ulong> _savedCyclesSet = new HashSet<ulong>();
    // 建立环形缓冲区，一直存放最后几个周期的数据，在实验的终止的时候将存储的数据存储,注意去重的逻辑
    private CircularBuffer<List<Dictionary<string, double>>>? _latestCycleBuffers;

    private ITemplate _templateIns;
    private bool _isSaveDb;
    private BufferInputVar _inputVarBuffer;
    private string _simpleCode;
    private string _subTaskID;

    /// <summary>
    /// 是否采集异常数据
    /// </summary>
    private bool _isExceptionData;

    /// <summary>
    /// 异常数据buffer
    /// </summary>
    private BufferInputVar? abnormaldaqBuffer;
    
    /// <summary>
    /// 异常数据表
    /// </summary>
    private DynamicDataWriter? abnormalwriter;

    /// <summary>
    /// 异常数据的上限值，超过该值的数据将被视为异常数据
    /// </summary>
    private double _exceptionDataUpperLimit;

    /// <summary>
    /// 异常数据的下限值，低于该值的数据将被视为异常数据
    /// </summary>
    private double _exceptionDataLowerLimit;

    /// <summary>
    /// 判断异常数据的精度，用于确定数据是否超出上下限
    /// </summary>
    private double _exceptionDataPrecision;

    /// <summary>
    /// 数据采集的模式，表示采集数据的方式（如按时间、按位移等）
    /// </summary>
    private string _collectMode;

    /// <summary>
    /// 数据采集的频率，表示采集数据的间隔或频率
    /// </summary>
    private double _collectFrequency;

    /// <summary>
    /// 最终要采集的周期数，表示数据采集的总周期数
    /// </summary>
    private ulong _finalCollectedCycles;

    /// <summary>
    /// 开始要采集的周期数，表示数据采集的起始周期
    /// </summary>
    private ulong _firstCollectedCycles;

    /// <summary>
    /// 是否需要间隔采集，表示是否在采集过程中跳过某些周期
    /// </summary>
    private bool _isIntervalCollected;

    /// <summary>
    /// 间隔采集的周期数，表示每次采集之间跳过的周期数
    /// </summary>
    private ulong _intervalCollectedCycles;

    /// <summary>
    /// 是否采用指定周期采集，表示是否只采集指定的周期
    /// </summary>
    private bool _isSpecifyCycle;

    private bool _isFinalCollected;

    private bool _isFirstCollected;

    /// <summary>
    /// 需要采集的指定周期，表示需要采集的具体周期数组
    /// </summary>
    private ulong[] _specifyCycle;

    /// <summary>
    /// 频率信号变量的代码，表示用于采集频率的信号变量名称
    /// </summary>
    private string _frequencySignalCode;

    /// <summary>
    /// 数据采集的频率（DAQ 速率），表示数据采集的速率（如每秒采集多少次）
    /// </summary>
    private int _daqRate;

    /// <summary>
    /// 控制模式的信号变量code
    /// </summary>
    private string _activeCtrlSignalCode;
    private string _cycleSignalCode;
    private double _circleCmdFrequency;
    /// <summary>
    /// 采集间隔的点数，默认值为1，含义为所有点都采集
    /// </summary>
    private int _intervalPoints = 1;
    
    //采集的当前周期
    private double _currentCycle = 0.00;
    //一个周期点数
    private double countPerCircle;
    /// <summary>
    /// 类名,区分是周期数据采集还是峰谷值采集
    /// </summary>
    private string _className;

    private bool _isLogarithmicCollection;
    //private double _currentCircles;
    //存二维数组
    bool _isDoubleArray;
    string _doubleArrayCode;
   
    DoubleArrayInputVar _doubleArrayInputVar;
    public DynamicDaqHandler(DynamicDaqHandlerParameters parameters)
    {
        SetParameters(parameters);
        if (_simpleCode != null)
        {
            var tableName = _simpleCode + _inputVarBuffer.Code;
            if (abnormaldaqBuffer != null)
            {
                abnormalwriter=(DynamicDataWriter)_templateIns.CurrentInst.TableWriters[_simpleCode + abnormaldaqBuffer.Code];
            }
            writer = (DynamicDataWriter)_templateIns.CurrentInst.TableWriters[tableName];
        }
        // 初始化订阅
        InitializeSubscription();
    }

    private void InitializeSubscription()
    {
        switch (_className)
        {
            case "SubTaskHightGatherData":
                //周期数据采集
                InitHighGatherDataSub();
                break;
            case "SubTaskPeakGatherData":
                //峰谷数据采集
                InitPeakGatherDataSub();
                break;
        }
    }

    // 处理峰谷值采集的订阅
    private void InitPeakGatherDataSub()
    {
        
        var filteredDataStream = _templateIns.TemplateObservable.Where(x =>
           x.Keys.Any(key => _inputVarBuffer.SignalCodes.Contains(key) || key == "create_time")
         );
        //后N周期订阅，用于存储最后N周期的数据
        if (_isFinalCollected)
        {
            var stopwatchPeakPostNCycleSubscription = new Stopwatch();
            int stopwatchPeakPostNCycleSubscription_i = 0;
            _postNCycleSubscription = filteredDataStream.Subscribe(

               (signal) =>
                   {
                       if (!signal.ContainsKey(_cycleSignalCode))
                       {
                           Logger.Info($"Cycle signal code '{_cycleSignalCode}' not found in the dictionary.");
                           return;
                       }
                       stopwatchPeakPostNCycleSubscription.Start();
                       // 在该缓冲区
                       if (_latestCycleBuffers.Tail != -1 && _latestCycleBuffers.GetValue().Last()[_cycleSignalCode] == signal[_cycleSignalCode])
                       {
                           // 如果当前周期和最新周期相同，则不添加

                       }
                       else
                       {
                           // 如果当前周期和最新周期不相同，则添加
                           _latestCycleBuffers!.AddValue(new List<Dictionary<string, double>> { signal });
                       }
                       stopwatchPeakPostNCycleSubscription.Stop();
                       stopwatchPeakPostNCycleSubscription_i++;
                       if (stopwatchPeakPostNCycleSubscription_i % 100000 == 0)
                       {
                           stopwatchPeakPostNCycleSubscription_i = 0;
                           Logger.Error($"峰谷值采集后N周期订阅100000条数据处理时间： {stopwatchPeakPostNCycleSubscription.ElapsedMilliseconds}ms");
                           stopwatchPeakPostNCycleSubscription.Reset();
                       }
                   });
            
        }
        /* 下方代码逻辑原码
       //保存数据到二维数组
        _dataSubscription = filteredDataStream
            .Where((signal, Index) => FilterCycle(signal, Index)).GroupBy(signal => signal[_cycleSignalCode])
            .Subscribe(group =>
            {

                group.Take(1).Subscribe(vars =>
                {
                    
                    // 发送数据到_saveDoubleArrary观察者
                    _saveDoubleArrary.OnNext(vars);
                    // 发送数据到_saveProcessData观察者
                    ProcessData(vars);
                    //SendToUI(vars);
                    //ProcessData(vars);
                    
                });
                
            });
            */
        //将数据筛选后下发 - 峰谷值采集批量处理优化
        var stopwatchPeakFilterDataSubscription = new Stopwatch();
        int stopwatchPeakFilterDataSubscription_i = 0;
        _filterDataSubscription = filteredDataStream
            .Take(1)
            .Do(signal =>
            {
                //过滤第一个点
                _invalidCycle = signal[_cycleSignalCode];
            })
            .Concat(filteredDataStream)
            .Where(dic =>
            {
                if (!dic.ContainsKey(_cycleSignalCode))
                {
                    Logger.Error($"Cycle signal code '{_cycleSignalCode}' not found in the dictionary.");
                    return false;
                }
                //采集周期和当前周期相等直接丢，一个周期只要一个数
                if (_currentCycle == dic[_cycleSignalCode])
                {
                    return false;
                }
                else
                {
                    _currentCycle = dic[_cycleSignalCode];
                    return true;
                }
            })
            .Where((signal, Index) => FilterCycle(signal, Index))
            .Subscribe(vars =>
            {
                stopwatchPeakFilterDataSubscription.Start();
                _ProcessDataSubAffterFG.OnNext(vars);
                stopwatchPeakFilterDataSubscription.Stop();
                stopwatchPeakFilterDataSubscription_i++;
                if (stopwatchPeakFilterDataSubscription_i % 100 == 0)
                {
                    stopwatchPeakFilterDataSubscription_i = 0;
                    Logger.Error($"峰谷值采集100周期处理时间： {stopwatchPeakFilterDataSubscription.ElapsedMilliseconds}ms");
                    stopwatchPeakFilterDataSubscription.Reset();
                }
            });
        // 保存数据到buffer中 - 批量处理数据存储
        var stopwatchPeakSaveProcessDataSubscriptions = new Stopwatch();
        int stopwatchPeakSaveProcessDataSubscriptions_i = 0;
        _saveProcessDataSubscriptions = _ProcessDataSubAffterFG
            .Subscribe(vars =>
            {
                stopwatchPeakSaveProcessDataSubscriptions.Start();
                // 批量处理数据存储，减少ProcessData调用频次
                ProcessData(vars);
                stopwatchPeakSaveProcessDataSubscriptions.Stop();
                stopwatchPeakSaveProcessDataSubscriptions_i++;
                if (stopwatchPeakSaveProcessDataSubscriptions_i % 100 == 0)
                {
                    stopwatchPeakSaveProcessDataSubscriptions_i = 0;
                    Logger.Error($"峰谷值采集100周期数据处理时间： {stopwatchPeakSaveProcessDataSubscriptions.ElapsedMilliseconds}ms");
                    stopwatchPeakSaveProcessDataSubscriptions.Reset();
                }
            });
        //保存二维数组下发订阅 - 批量处理二维数组保存
        var stopwatchPeakSaveDoubleArraryscriptions = new Stopwatch();
        int stopwatchPeakSaveDoubleArraryscriptions_i = 0;
        if (_isDoubleArray == true)
        {
            _saveDoubleArraryscriptions = _ProcessDataSubAffterFG
            .Subscribe(vars =>
            {
                stopwatchPeakSaveDoubleArraryscriptions.Start();
                // 批量保存二维数组数据，减少数据库写入频次
                SaveDoubleArrary(vars);
                stopwatchPeakSaveDoubleArraryscriptions.Stop();
                stopwatchPeakSaveDoubleArraryscriptions_i++;
                if (stopwatchPeakSaveDoubleArraryscriptions_i % 100 == 0)
                {
                    stopwatchPeakSaveDoubleArraryscriptions_i = 0;
                    Logger.Error($"峰谷值采集保存100周期数据二维数组处理时间： {stopwatchPeakSaveDoubleArraryscriptions.ElapsedMilliseconds}ms");
                    stopwatchPeakSaveDoubleArraryscriptions.Reset();
                }
            });
        }

    }
    /// <summary>
    /// 初始化周期数据采集的订阅
    /// </summary>
    private void InitHighGatherDataSub()
    {
        Action<Dictionary<string, double>> GetCmdFrequencyFromStream = (x) =>
        {
           
            //TODO 这里的需要赋值信号变量“频率”的值，这里“频率”信号变量code暂时写死
            _circleCmdFrequency = x[_frequencySignalCode];
            GetInterval();
        };
        
        //异常数据采集Func,这里需要确定的是哪个异常通道TODO,这个值也需要读取前端
        Func<Dictionary<string, double>, bool> ConditionUnNormalData = (signal) =>
        {
            if (_isExceptionData)
            {
                // 计算异常的数据的上限值和下限值
                // 前端 传值周期波的最大值_meanValue 和最小值参数  _exceptionDataLowerLimit
                // 算法 异常数据判定方法为：
                // (_exceptionDataUpperLimit - _exceptionDataLowerLimit)*_exceptionDataPrecision = 可以接受的波动幅度
                // 正常的范围应该为  [_exceptionDataLowerLimit - 可以接受的波动幅度  ,_exceptionDataUpperLimit+可以接受的波动幅度]
                var acceptValue =
                    (_exceptionDataUpperLimit - _exceptionDataLowerLimit) * _exceptionDataPrecision;
                var upLimit = _exceptionDataUpperLimit + acceptValue;
                var downLimit = _exceptionDataLowerLimit - acceptValue;
                return signal[_activeCtrlSignalCode] < downLimit
                    || signal[_activeCtrlSignalCode] > upLimit;
            }
            else
                return false;
        };

        Func<Dictionary<string, double>, long, bool> ConditionIntervalData = (signal, index) =>
            index % _intervalPoints == 0;

        // 只关心当前的buffer关心的信号变量
        var filteredDataStream = _templateIns.TemplateObservable.Where(x =>
            x.Keys.Any(key => _inputVarBuffer.SignalCodes.Contains(key) || key == "create_time")
        );
        /* 实现下面功能的原代码
        var filteredCycle = filteredDataStream
              .Where(signals => signals[_frequencySignalCode] > 0)
              .Take(1)
              .Do(signal => GetCmdFrequencyFromStream(signal))
              .Concat(filteredDataStream)
              .Do(
                  (signal) =>
                  {

                      // 处理异常数据，异常数据的处理应该在点过滤之前
                      if (ConditionUnNormalData(signal))
                      {
                          //异常数据存储到数据库中，数据库增加一个异常点字段，1为异常点，0为非异常点
                          Logger.Info($"采集到了异常数据数据：{signal}");
                          //SendToUI(signal);
                          ProcessData(signal, 1);//Todo：目前这样写，会导致整个数据库额外存储了异常点，在后续数据获取的接口，可以区分筛选异常点出来
                          _saveDoubleArrary.OnNext(signal);
                      }

                  }
              )
              .Where((dic, index) => ConditionIntervalData(dic, index)); // 应用过滤器
              */
        var stopwatchtotalFlowSubscription = new Stopwatch();
        int stopwatchtotalFlowSubscription_i = 0;
        //总流订阅 - 使用Buffer(bufferCount)进行批量处理，减少OnNext调用频次，提升CPU性能
        _totalFlowSubscription = filteredDataStream
               .Where(signals => signals[_frequencySignalCode] > 0)
               .Take(1)
               .Do(signal =>
               {
                   //过滤第一个点
                   _invalidCycle = signal[_cycleSignalCode];
                   //计算采集频率
                   GetCmdFrequencyFromStream(signal);
               })
               .Concat(filteredDataStream)
               .Buffer(bufferCount) // 缓冲bufferCount个数据点，利用上游集中传输特点
               .Subscribe(batchVars =>
                {
                    stopwatchtotalFlowSubscription.Start();
                    // 优化批量处理，直接传递数据减少中间处理
                    DynamicDaqHandlerSubject.OnNext(batchVars);
                    stopwatchtotalFlowSubscription.Stop();
                    stopwatchtotalFlowSubscription_i++;
                    if (stopwatchtotalFlowSubscription_i % 100 == 0)
                    {
                        stopwatchtotalFlowSubscription_i = 0;
                        Logger.Error($"周期数据采集总流订阅100000条数据处理时间： {stopwatchtotalFlowSubscription.ElapsedMilliseconds}ms");
                        stopwatchtotalFlowSubscription.Reset();
                    }
                });
        //异常数据订阅 - 优化异常数据检测性能       
        if (_isExceptionData)
        {
            var stopwatchExceptionPointSubscription = new Stopwatch();
            int stopwatchExceptionPointSubscription_i = 0;
            _exceptionPointSubscription = DynamicDaqHandlerSubject
           .Subscribe(
               (batchSignals) =>
               {
                    stopwatchExceptionPointSubscription.Start();
                   // 优化异常数据检测，减少不必要的循环开销
                   for (int i = 0; i < batchSignals.Count; i++)
                   {
                       var signal = batchSignals[i];
                       // 处理异常数据，异常数据的处理应该在点过滤之前
                       if (ConditionUnNormalData(signal))
                       {
                           //异常数据存储到数据库中，数据库增加一个异常点字段，1为异常点，0为非异常点
                           Logger.Info($"采集到了异常数据数据：{signal}");
                           //SendToUI(signal);
                           ProcessData(signal, 1);//Todo：目前这样写，会导致整个数据库额外存储了异常点，在后续数据获取的接口，可以区分筛选异常点出来
                           SaveDoubleArrary(signal);
                       }
                   }
                    stopwatchExceptionPointSubscription.Stop();
                    stopwatchExceptionPointSubscription_i++;
                    if (stopwatchExceptionPointSubscription_i % 100 == 0)
                    {
                        stopwatchExceptionPointSubscription_i = 0;
                        Logger.Error($"周期数据采集异常数据订阅100000条数据处理时间： {stopwatchExceptionPointSubscription.ElapsedMilliseconds}ms");
                        stopwatchExceptionPointSubscription.Reset();
                    }
               }
           );
        }
        //筛选后下发下一层数据订阅 - 优化间隔过滤性能
        var stopwatchSendDaqDataSubscription = new Stopwatch();
        int stopwatchSendDaqDataSubscription_i = 0;
        _sendDaqDataSubscription = DynamicDaqHandlerSubject
           .Subscribe(batchVars =>
            {
                stopwatchSendDaqDataSubscription.Start();
                // 优化间隔过滤，使用预分配容量减少内存重分配
                var filteredBatch = new List<Dictionary<string, double>>(batchVars.Count / _intervalPoints + 1);
                for (int i = 0; i < batchVars.Count; i++)
                {
                    if (ConditionIntervalData(batchVars[i], i))
                    {            
                            // 添加到数据流
                            filteredBatch.Add(batchVars[i]);
                    }
                }
                if (filteredBatch.Count > 0)
                {
                    _ProcessDataSub.OnNext(filteredBatch);
                }
                stopwatchSendDaqDataSubscription.Stop();
                stopwatchSendDaqDataSubscription_i++;
                if (stopwatchSendDaqDataSubscription_i % 100 == 0)
                {
                    stopwatchSendDaqDataSubscription_i = 0;
                    Logger.Error($"周期数据采集筛选后下发下一层数据订阅100000条数据处理时间： {stopwatchSendDaqDataSubscription.ElapsedMilliseconds}ms");
                    stopwatchSendDaqDataSubscription.Reset();
                }
            });
        /*实现下面功能的原代码
         _dataSubscription = filteredCycle
            .Do(
                (signal) =>
                {
                    // 在该缓冲区
                    if (_isFinalCollected)
                    {
                        _latestCycleBuffers!.AddValue(signal);
                    }
                    //_currentCircles = signal[_cycleSignalCode];没用到
                }
            )
         */
        //保存到后N周期 - 优化后N周期数据存储性能       
        if (_isFinalCollected)
        {
            var stopwatchPostNCycleSubscription = new Stopwatch();
            int stopwatchPostNCycleSubscription_i = 0;
            _postNCycleSubscription = _ProcessDataSub.Subscribe((batchSignals) =>
                    {
                        stopwatchPostNCycleSubscription.Start();
                        // 优化环形缓冲区操作，使用for循环减少迭代器开销
                        for (int i = 0; i < batchSignals.Count; i++)
                        {
                            if (!batchSignals[i].ContainsKey(_cycleSignalCode))
                            {
                                Logger.Info($"Cycle signal code '{_cycleSignalCode}' not found in the dictionary.");
                                continue;
                            }
                            if (_latestCycleBuffers.Tail != -1
                            && _latestCycleBuffers.GetValue().Last()[_cycleSignalCode] == batchSignals[i][_cycleSignalCode])
                            {
                                // 如果当前周期和最新周期相同，则添加到最近周期缓冲区
                                _latestCycleBuffers.GetValue().Add(batchSignals[i]);
                            }
                            else
                            {
                                // 如果当前周期和最新周期不相同，则添加到新的周期缓冲区
                                _latestCycleBuffers!.AddValue(new List<Dictionary<string, double>>((int)countPerCircle) { batchSignals[i] });
                            }
                        }
                        stopwatchPostNCycleSubscription.Stop();
                        stopwatchPostNCycleSubscription_i++;
                        if (stopwatchPostNCycleSubscription_i % 100 == 0)
                        {
                            stopwatchPostNCycleSubscription_i = 0;
                            Logger.Error($"周期数据采集保存到后N周期订阅100000条数据处理时间： {stopwatchPostNCycleSubscription.ElapsedMilliseconds}ms");
                            stopwatchPostNCycleSubscription.Reset();
                        }
                    });
        }
        /*实现下面功能的原代码
             .Where((signal, Index) => FilterCycle(signal, Index))
             .Subscribe(vars =>
             {
                // 处理数据
                //SendToUI(vars);
                _saveProcessData.OnNext(vars);
                _saveDoubleArrary.OnNext(vars);
             });
             //处理保存数据
              _saveProcessDataSubscriptions =_saveProcessData.Subscribe(vars =>
               {
                 // 处理数据
               ProcessData(vars);

                  });
             */
        //正常数据下发 - 优化周期过滤性能
        var stopwatchFilterDataSubscription = new Stopwatch();
        int stopwatchFilterDataSubscription_i = 0;
        _filterDataSubscription = _ProcessDataSub.Subscribe(batchSignals =>
        {
            stopwatchFilterDataSubscription.Start();
            // 优化周期过滤，预分配容量并减少条件判断
            var filteredBatch = new List<Dictionary<string, double>>(batchSignals.Count);
            for (int i = 0; i < batchSignals.Count; i++)
            {
                if (FilterCycle(batchSignals[i], i))
                {
                    filteredBatch.Add(batchSignals[i]);
                }
            }
            if (filteredBatch.Count > 0)
            {
                _ProcessDataSubAffter.OnNext(filteredBatch);
            }
            stopwatchFilterDataSubscription.Stop();
            stopwatchFilterDataSubscription_i++;
            if (stopwatchFilterDataSubscription_i % 100 == 0)
            {
                stopwatchFilterDataSubscription_i = 0;
                Logger.Error($"周期数据采集正常数据下发100000条数据处理时间： {stopwatchFilterDataSubscription.ElapsedMilliseconds}ms");
                stopwatchFilterDataSubscription.Reset();
            }
        });
         //保存二维数组下发订阅 - 优化二维数组保存性能
       
        if (_isDoubleArray == true)
        {
            var stopwatchSaveDoubleArraryscriptions = new Stopwatch();
            int stopwatchSaveDoubleArraryscriptions_i = 0;
            _saveDoubleArraryscriptions = _ProcessDataSubAffter.Subscribe(batchVars =>
           {
               stopwatchSaveDoubleArraryscriptions.Start();
               // 优化二维数组保存，使用for循环减少迭代器开销
               for (int i = 0; i < batchVars.Count; i++)
               {
                   SaveDoubleArrary(batchVars[i]);
               }
               stopwatchSaveDoubleArraryscriptions.Stop();
               stopwatchSaveDoubleArraryscriptions_i++;
               if (stopwatchSaveDoubleArraryscriptions_i % 100 == 0)
               {
                   stopwatchSaveDoubleArraryscriptions_i = 0;
                   Logger.Error($"周期数据采集保存二维数组下发订阅100000条数据处理时间： {stopwatchSaveDoubleArraryscriptions.ElapsedMilliseconds}ms");
                   stopwatchSaveDoubleArraryscriptions.Reset();
               }
           });
        }
       
        //保存到Buffer订阅 - 优化数据存储性能
        var stopwatchSaveProcessDataSubscriptions = new Stopwatch();
        int stopwatchSaveProcessDataSubscriptions_i = 0;
        _saveProcessDataSubscriptions = _ProcessDataSubAffter.Subscribe(batchVars =>
        {
            stopwatchSaveProcessDataSubscriptions.Start();
            // 优化数据存储，使用for循环提升性能
            for (int i = 0; i < batchVars.Count; i++)
            {
                ProcessData(batchVars[i]);
            }
            stopwatchSaveProcessDataSubscriptions.Stop();
            stopwatchSaveProcessDataSubscriptions_i++;
            if (stopwatchSaveProcessDataSubscriptions_i % 100 == 0)
            {
                stopwatchSaveProcessDataSubscriptions_i = 0;
                Logger.Error($"周期数据采集保存到Buffer订阅100000条数据处理时间： {stopwatchSaveProcessDataSubscriptions.ElapsedMilliseconds}ms");
                stopwatchSaveProcessDataSubscriptions.Reset();
            }
        });           
    }

    private static readonly JsonSerializerOptions _jsonOptions = new JsonSerializerOptions
    {
        Converters = { new DoubleConverter() },
        WriteIndented = false,
        DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull
    };
 
    private void SaveDoubleArrary(Dictionary<string, double> data)
    {
        if (_isDoubleArray == true && _doubleArrayInputVar != null)
        {
            Dictionary<string, object> rowData = new();
            foreach (DoubleArrayColumn columnObject in _doubleArrayInputVar!.GetColumnObjects())
            {
                if (columnObject.CorrelationCode != null && data.TryGetValue(columnObject.CorrelationCode, out double value))
                    rowData.Add(columnObject.Code, value);
            }
            // 增加一行数据
            _doubleArrayInputVar!.AddRowData(rowData);
        }
    }
   
    /// <summary>
    /// 根据不同的条件来换算间隔N个点采集一个
    /// 需要确保每个周期最多采集500个点
    /// 计算规则：
    /// 1. 按照时间间隔采集，则按照采集频率计算出采集间隔
    /// 2. 其中的daqrate是采集频率，硬件的CCSSInfo中能够知道指定轴的该参数，所以这里的采集和轴是绑定的
    /// 3. 主要目的是算出：_intervalPoints，后面做过滤使用
    /// </summary>
    /// <returns></returns>
    private void GetInterval()
    {
        //防止异常数据
        if (_collectFrequency <= 0)
        {
            _collectFrequency = 1;
            Logger.Error($"采集频率为0，请检查采集频率");
        }
        if (_collectMode == "TimeInterval")
        {
            _intervalPoints = (int)(_daqRate * _collectFrequency);
        }
        else
        {
            _intervalPoints = (int)(_daqRate / _collectFrequency);
        }
        if (_intervalPoints <= 0)
        {
            Logger.Warn($"计算得到的采集间隔点数 ({_intervalPoints}) 无效。已强制设为1。");
            _intervalPoints = 1;
        }

        //每个周期包含多少个点
        // 例如模拟器中daqrate =1000,_circleCmdFrequency如果是1的话，就是1000/1=1000个点
        if (_circleCmdFrequency <= 0)
        {
            _circleCmdFrequency = 1;
        }
        countPerCircle = _daqRate / _circleCmdFrequency;
        // 最后要保存的缓冲区大小= 最后保存的周期数* 每个周期包含的点数
        Logger.Error($"_daqRate: {_daqRate}, _finalCollectedCycles: {_finalCollectedCycles}, countPerCircle: {countPerCircle},_collectFrequency：{_collectFrequency}， _intervalPoints: {_intervalPoints}");
        // 计算每个周期采集的点数
        countPerCircle = countPerCircle / _intervalPoints;
    }
    
    /// <summary>
    ///  包含三个和周期过滤有关的Func
    ///  1. ConditionIntervalCircle  返回true 的条件是 
    ///  当前周期在要采集的前N个周期中，或者在后N个周期中，又或者当前周期%间隔周期为0
    /// </summary>
    /// <param name="dic"></param>
    /// <returns></returns>
    private bool FilterCycle(Dictionary<string, double> dic, long index)
    {

        if (!dic.ContainsKey(_cycleSignalCode))
        {
            Logger.Error($"Cycle signal code '{_cycleSignalCode}' not found in the dictionary.");
            return false;
        }
        if (_invalidCycle == dic[_cycleSignalCode])
        {
            return false;
        }
        else
        {
            _invalidCycle = 0;
        }
        var currentCycle = dic[_cycleSignalCode];

        if ((_isFirstCollected && currentCycle <= _firstCollectedCycles) ||
                (_isIntervalCollected && Math.Abs(currentCycle % _intervalCollectedCycles) < 0.5) ||//周期是整数容差0.5合理
                (_isSpecifyCycle && _specifyCycle.Contains((ulong)currentCycle)))
            return true;

        if (_isLogarithmicCollection)
        {
            var temp = (ulong)Math.Pow(10, Math.Floor(Math.Log10(currentCycle)));
            return currentCycle == 0 ? false : currentCycle % temp == 0;
        }
        return false;
    }
    
    /// <summary>
    /// 对应周期数据采集的数据处理
    /// </summary>
    /// <param name="vars"></param>
    private void ProcessData(Dictionary<string, double> vars, int abnormal = 0)
    {
        
        int bufferIndex = 0;
        Dictionary<string,double> filteredVars = _inputVarBuffer.SignalCodes
        .ToDictionary(
            item => item,
            item => vars.TryGetValue(item,out double value) ? value : double.NaN
        );

        foreach (var kvp in filteredVars)
        {
            bufferIndex = _inputVarBuffer.Value[kvp.Key].AddValue(kvp.Value);
        }
        filteredVars["index"] = bufferIndex;
        filteredVars["create_time"] = vars["create_time"];
        if (abnormal == 0)
        {
            if (!_isSaveDb || string.IsNullOrEmpty(_simpleCode))
            {
                return;
            }
            _savedCyclesSet.Add((ulong)vars[_cycleSignalCode]);
            // 存库逻辑
            writer?.Add(filteredVars);
        }
        else
        {
            if (abnormalwriter!=null)
            {
                // 异常点存储
                abnormalwriter?.Add(filteredVars);
            }
        }

    }

    private void SetParameters(DynamicDaqHandlerParameters parameters)
    {
        _className = parameters.ClassName;
        _templateIns = parameters.Template;
        _subTaskID = parameters.SubTaskID;
        _simpleCode = parameters.SimpleCode;
        _isSaveDb = parameters.IsSaveDb;

        _frequencySignalCode = parameters.FrequencySignalCode;//原始数据通道中，频率的code - signal_pinlv
        _cycleSignalCode = parameters.CycleSignalCode;//原始数据通道中，频率的code - signal_cycle

        //1. 缓冲区是否清空， -》这个在SubTaskHightGatherData中进行处理的
        _daqRate = 10000;// parameters.DaqRate;//ToDo：2. 轴 -》这个是为了获取DAQRATE
        Logger.Error("动态数据采集初始化：daqRate:" + _daqRate);
        _inputVarBuffer = parameters.BufferInputVar;//3. buffer这个是作为创建参数存到了
        _collectMode = parameters.CollectMode;//4. 采样方式选择：时间间隔、采集频率
        _collectFrequency = parameters.CollectFrequency;//5. 采样率，根据采样方式不同，计算不同

        _isFinalCollected = parameters.IsFinalCollected;//6. 采集最后N周期的勾选项[新增]
        _finalCollectedCycles = parameters.FinalCollectedCycles;//6. 采集最后N周期的周期数，如果勾选项为false则不采集
        if(_finalCollectedCycles<=0)_isFinalCollected=false;
        _isFirstCollected = parameters.IsFirstCollected;//7. 采集前N周期的勾选项[新增]
        _firstCollectedCycles = parameters.FirstCollectedCycles;//7. 采集前N周期的周期数，如果勾选项为false，则不采集

        _isSpecifyCycle = parameters.IsSpecifyCycle;//8. 特定周期的勾选项[已有]
        _specifyCycle = parameters.SpecifyCycle;//8. 特定周期的勾选项，如果勾选项为false，则不采集

        _isIntervalCollected = parameters.IsIntervalCollected;//9. 间隔N周期的勾选项[已有]
        _intervalCollectedCycles = parameters.IntervalCollectedCycles;//9. 间隔N周期的周期数，如果勾选项为false，则不采集

        _isLogarithmicCollection = parameters.LogarithmicCollect;//10. 对数采集的勾选项[已有]，如果勾选项为false，对应的对数采集不生效

        _isExceptionData = parameters.IsExceptionData;//11. 异常数据采集勾选项[已有]，如果勾选项为false，则下面四个11相关的内容不生效
        _exceptionDataUpperLimit = parameters.ExceptionDataUpperLimit;//11. 最大值
        _exceptionDataLowerLimit = parameters.ExceptionDataLowerLimit;//11. 最小值
        _exceptionDataPrecision = parameters.ExceptionDataPrecision;//11. 异常精度
        _activeCtrlSignalCode = parameters.ActiveCtrlSignalCode;//11. 异常数据检测信号变量的code
        _isDoubleArray = parameters._isDoubleArray;//是否存入二维数组
        _doubleArrayCode = parameters._doubleArrayCode;//二维数组code
        abnormaldaqBuffer = parameters.abnormaldaqBuffer;//异常数据缓冲区
        if (_isDoubleArray == true)
        {
            _doubleArrayInputVar = _templateIns.GetVarByName<DoubleArrayInputVar>(_doubleArrayCode);
        }
        // 初始化后N周期环形缓冲区
        _latestCycleBuffers = new CircularBuffer<List<Dictionary<string, double>>>((int)_finalCollectedCycles);
    }

    /// <summary>
    /// 更新采集流的参数
    /// </summary>
    /// <param name="parameters"></param>
    public void Update(DynamicDaqHandlerParameters parameters)
    {
        // 更新传入的参数
        SetParameters(parameters);
        InitializeSubscription();
    }

    /// <summary>
    /// 存储最后N个周期函数
    /// </summary>
    public void StoreLastNCycles()
    {
        
        //在采集过程中，环形缓冲区一直在存储数据，在采集子任务终止的时候， latestCycleBuffers的数据需要去重之后存储DB
        if (_isFinalCollected && _latestCycleBuffers != null&&_latestCycleBuffers.Tail>=0)
        {
            // 拿到最后N周期的第一个数
            // 因为第一个可能不完整，所以如果已经存了最后一个周期，则去掉最后一个周期  
            // 如果没有存最后一个周期，则存最后N周期中所有周期不是0的数据(相当于所有数据)
            double latestCycle = _latestCycleBuffers.GetBuffer()[0][0][_cycleSignalCode];
            //删除最后N周期在数据库中已经存储的数据（大于最后N周期的第一个周期），避免重复存储
            writer.RemoveLast(_templateIns.Db.Connection, _cycleSignalCode, latestCycle);
            while (_inputVarBuffer.Value[_cycleSignalCode].Count>0&&_inputVarBuffer.Value[_cycleSignalCode].GetValue() >= latestCycle)
            {
                _inputVarBuffer.RemoveLast();
            }

            // 获取与当前周期信号相关的二维数组列对象
            if ( _isDoubleArray == true && _doubleArrayInputVar != null)
            {
                DoubleArrayColumn columnObject = _doubleArrayInputVar!.GetColumnObjects().FirstOrDefault(x => x.CorrelationCode == _cycleSignalCode);
                if (columnObject != null)
                {
                    // 如果二维数组中最后一行的周期值大于等于当前最新周期，则循环删除多余的数据行，避免数据重复
                    while (Convert.ToDouble(columnObject.Values.Last()) >= latestCycle)
                    {
                        _doubleArrayInputVar.DeleteRowData(columnObject.Values.Count - 1);
                    }
                }
            }
            if (!_savedCyclesSet.Contains((ulong)latestCycle)) latestCycle = 0;
            // 最后n个周期全存
            foreach (var canSaveToDB in _latestCycleBuffers.GetBuffer())
            {
                foreach (var item in canSaveToDB)
                {
                    ProcessData(item);
                    SaveDoubleArrary(item);
                }
            }
            _latestCycleBuffers.Reset();
        }
    }

    public void Dispose()
    {
        StoreLastNCycles();
        // 取消订阅
        _totalFlowSubscription?.Dispose();
        _exceptionPointSubscription?.Dispose();
        _sendDaqDataSubscription?.Dispose();
        _postNCycleSubscription?.Dispose();
        _filterDataSubscription?.Dispose();
        _saveDoubleArraryscriptions?.Dispose();
        _saveProcessDataSubscriptions?.Dispose();
        GC.SuppressFinalize(this);
    }

    /// <summary>
    /// Json序列化时，对double类型的处理
    /// </summary>
    public class DoubleConverter : JsonConverter<double>
    {
        /// <summary>
        /// 重写double类型 WriteJson
        /// </summary>
        public override void Write(
            Utf8JsonWriter writer,
            double value,
            JsonSerializerOptions options
        )
        {
            // 自定义序列化逻辑
            if (double.IsPositiveInfinity(value))
                writer.WriteStringValue("Infinity");
            else if (double.IsNegativeInfinity(value))
                writer.WriteStringValue("-Infinity");
            else if (double.IsNaN(value))
                writer.WriteNullValue();
            else
                writer.WriteNumberValue(value);
        }

        /// <summary>
        /// 正常读double值
        /// </summary>
        public override double Read(
            ref Utf8JsonReader reader,
            Type typeToConvert,
            JsonSerializerOptions options
        )
        {
            return reader.GetDouble();
        }
    }
}
