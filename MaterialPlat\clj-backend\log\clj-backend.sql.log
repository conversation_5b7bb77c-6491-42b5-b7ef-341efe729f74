2025-09-11 09:10:38,731 [qtp1525161017-256] INFO  jdbc.sqlonly - select record.*, lastInspect.inspect_time as last_inspect_time, floor(julianday('now') - julianday(substr(limit_inspect_time,1,10))) 
AS days_difference, (inspection_frequency_days - inspection_remind_days) as remind_period_days 
from t_inspection_record record left join ( select record.inspection_item_id, max(inspect_time) 
as inspect_time from t_inspection_record record INNER JOIN t_inspection_item item on item.delete_flag 
= 0 and item.inspection_item_id = record.inspection_item_id GROUP BY record.inspection_item_id 
) lastInspect on lastInspect.inspection_item_id = record.inspection_item_id where days_difference 
< remind_period_days and (days_difference >= 0 and (record.inspect_time is null or (record.inspect_time 
is not null and floor(julianday('now') - julianday(substr(record.inspect_time,1,10))) = 0))) 
AND record.inspect_time is null order by limit_inspect_time desc, inspection_frequency_days 
asc 
 
2025-09-11 09:10:38,794 [qtp1525161017-256] INFO  jdbc.sqlonly - select record.*, lastInspect.inspect_time as last_inspect_time, floor(julianday('now') - julianday(substr(limit_inspect_time,1,10))) 
AS days_difference, (inspection_frequency_days - inspection_remind_days) as remind_period_days 
from t_inspection_record record left join ( select record.inspection_item_id, max(inspect_time) 
as inspect_time from t_inspection_record record INNER JOIN t_inspection_item item on item.delete_flag 
= 0 and item.inspection_item_id = record.inspection_item_id GROUP BY record.inspection_item_id 
) lastInspect on lastInspect.inspection_item_id = record.inspection_item_id where days_difference 
< remind_period_days and (days_difference < 0 and (record.inspect_time is null or (record.inspect_time 
is not null and floor(julianday('now') - julianday(substr(record.inspect_time,1,10))) = 0))) 
AND record.inspect_time is null order by limit_inspect_time desc, inspection_frequency_days 
asc 
 
