"use strict";(self.webpackChunkzhongji_experiment_react=self.webpackChunkzhongji_experiment_react||[]).push([[9882],{49882:(e,n,r)=>{r.r(n),r.d(n,{default:()=>a});var i=r(65043),s=r(72535),t=r(70579);const a=e=>{let{sizes:n,onDragEnd:r,id:a,render:d,onDrag:o,disabled:c=!1,minSize:u=25,...f}=e;const[g,l]=(0,i.useState)(n);(0,i.useEffect)((()=>{l(n)}),[n]);return(0,t.jsx)(s.A,{id:`ver-${a}`,minSize:u,snapOffset:0,render:d,onDragEnd:c?void 0:(e,n)=>{r(e,n)},onDrag:c?void 0:(e,n,r)=>{l(r),o(e,n,r)},gridTemplateRows:g,disabled:c,...f},`ver-${a}`)}}}]);
//# sourceMappingURL=9882.649fe475.chunk.js.map